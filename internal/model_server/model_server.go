package model_server

import (
	"fmt"
	"os"
	"os/signal"
	"rtb_model_server/conf"
	"rtb_model_server/internal/kafka"
	"rtb_model_server/internal/model_index"
	"rtb_model_server/internal/zaplog"
	"syscall"

	"rtb_model_server/common/domob_thrift/rtb_model_server"

	"git.apache.org/thrift.git/lib/go/thrift"
	"go.uber.org/zap"
)

type ModelServer struct {
	modelIndex *model_index.ModelIndexManager
}

var modelIndexManager *model_index.ModelIndexManager

func GetGlobalModelIndexManger() *model_index.ModelIndexManager {
	return modelIndexManager
}

func NewModelServer() *ModelServer {
	modelIndexManager = model_index.NewModelIndexManager()
	return &ModelServer{
		modelIndex: modelIndexManager,
	}
}

func (m *ModelServer) Start() error {
	var err error
	// 启动model index
	if err = m.startModelIndex(); err != nil {
		return err
	}

	// 初始化Kafka生产者
	if err = m.initKafkaProducers(); err != nil {
		zaplog.Logger.Error("Failed to initialize Kafka producers", zap.Error(err))
		return fmt.Errorf("Failed to initialize Kafka producers, err: %v", err)
	}

	// 启动model server
	if err = m.startModelServer(); err != nil {
		return err
	}

	// 设置优雅关闭
	go m.setupGracefulShutdown()
	return nil
}

func (m *ModelServer) startModelIndex() error {
	var err error
	//启动ModelIndex
	err = m.modelIndex.Start()
	return err
}

func (m *ModelServer) startModelServer() error {
	listenAddr := fmt.Sprintf(":%d", conf.GlobalConfig.Server.Port)
	zaplog.Logger.Info("starting model server", zap.String("address", listenAddr))

	// 创建Thrift传输层
	transport, err := thrift.NewTServerSocket(listenAddr)
	if err != nil {
		return err
	}

	// 创建协议工厂
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()

	// 创建处理器
	handler := &ModelServerHandler{}
	processor := rtb_model_server.NewRTBModelServerProcessor(handler)

	// 创建服务器
	serverTransportFactory := thrift.NewTBufferedTransportFactory(8192)
	server := thrift.NewTSimpleServer4(processor, transport, serverTransportFactory, protocolFactory)

	zaplog.Logger.Info("model server started successfully")
	if err := server.Serve(); err != nil {
		return err
	}
	return nil
}

// initKafkaProducers 初始化Kafka生产者
func (m *ModelServer) initKafkaProducers() error {
	// 初始化Kafka生产者
	err := kafka.InitKafkaProducers()
	if err != nil {
		return err
	}

	zaplog.Logger.Info("Kafka producers initialized successfully")
	return nil
}

// setupGracefulShutdown 设置优雅关闭
func (m *ModelServer) setupGracefulShutdown() {
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c
	// 关闭model index
	m.modelIndex.Stop()

	// 关闭Kafka生产者
	kafka.CloseKafkaProducers()
	zaplog.Logger.Info("Kafka producers closed")

	zaplog.Logger.Info("graceful shutdown completed")
	os.Exit(0)
}
