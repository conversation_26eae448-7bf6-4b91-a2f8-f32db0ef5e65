package mock

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"rtb_model_server/common/domob_thrift/rtb_model_server"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/conf"
	"strconv"

	"git.apache.org/thrift.git/lib/go/thrift"
	"github.com/google/uuid"
)

func MockRequest() {
	// 创建传输层和协议层
	transport, err := thrift.NewTSocket(fmt.Sprintf(":%d", conf.GlobalConfig.Server.Port))
	if err != nil {
		log.Fatalf("Error creating socket: %v", err)
	}

	transportFactory := thrift.NewTBufferedTransportFactory(8192)
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()

	// 创建客户端
	clientTransport := transportFactory.GetTransport(transport)
	defer clientTransport.Close()

	if err := clientTransport.Open(); err != nil {
		log.Fatalf("Error opening connection: %v", err)
	}

	client := rtb_model_server.NewRTBModelServerClientFactory(clientTransport, protocolFactory)
	req := buildBidReuqest()
	bReq, _ := json.Marshal(req)
	log.Printf("req: %s\n", string(bReq))
	result, err2 := client.GetAd(req)
	if err2 != nil {
		log.Fatalf("Error calling service: %v", err2)
	}
	bResp, _ := json.Marshal(result)
	log.Printf("resp: %s\n", string(bResp))
}

func buildBidReuqest() *rtb_types.RTBBidRequest {
	return &rtb_types.RTBBidRequest{
		ReqId:    uuid.New().String(),
		SearchId: 12346,
		UiName:   "mock-ui",

		ExchangeId: 132,
		Device: &rtb_types.RTBDeviceInfo{
			Ip:         "************",
			DmPlatform: 1,
			Oaidmd5:    "0010c95e0709774e57c26636c7d06f70",
			InstallledApps: []string{
				"com.eg.android.AlipayGphone",
				//"com.taotao.taobao",
			},
			UserAgent: "Mozilla/5.0 (Linux; Android 11; Samsung SM-G998B)",
		},
		App: &rtb_types.RTBAppInfo{
			DmMediaId: 799866793,
			AppBundle: "com.kuaishou.nebula",
		},
		BidList: []*rtb_types.RTBRequestResponseInfo{
			{
				Request: &rtb_types.RTBRequestInfo{

					SearchImpId:         123467,
					ImpId:               "xxxxxxx-imp01",
					AdMatchTypes:        []int64{1320000000020},
					SupportMediaBidType: 2,
				},
			},
		},
		RequiredCapabilities: map[int32]bool{1002: true},
		Capabilities:         map[int32]bool{2001: true},
	}
}

// HTTPClient HTTP客户端结构
type HTTPClient struct {
	modelServerAddr string
}

// NewHTTPClient 创建新的HTTP客户端
func NewHTTPClient(modelServerAddr string) *HTTPClient {
	return &HTTPClient{
		modelServerAddr: modelServerAddr,
	}
}

// Start 启动HTTP服务器
func (h *HTTPClient) Start(port int) error {
	http.HandleFunc("/bid", h.handleBidRequest)
	http.HandleFunc("/health", h.handleHealth)

	addr := ":" + strconv.Itoa(port)
	log.Printf("HTTP client server starting on %s, forwarding to model server: %s", addr, h.modelServerAddr)
	return http.ListenAndServe(addr, nil)
}

// handleBidRequest 处理竞价请求
func (h *HTTPClient) handleBidRequest(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 读取请求体
	body, err := io.ReadAll(r.Body)
	if err != nil {
		log.Printf("Error reading request body: %v", err)
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}
	defer r.Body.Close()

	// 解析JSON到RTBBidRequest
	var bidRequest rtb_types.RTBBidRequest
	if err := json.Unmarshal(body, &bidRequest); err != nil {
		log.Printf("Error parsing JSON: %v", err)
		http.Error(w, "Error parsing JSON", http.StatusBadRequest)
		return
	}

	// 转发到model server
	response, err := h.forwardToModelServer(&bidRequest)
	if err != nil {
		log.Printf("Error forwarding to model server: %v", err)
		http.Error(w, "Error forwarding to model server > "+err.Error(), http.StatusAccepted)
		return
	}

	// 返回响应
	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(response); err != nil {
		log.Printf("Error encoding response: %v", err)
		http.Error(w, "Error encoding response", http.StatusInternalServerError)
	}
}

// handleHealth 健康检查
func (h *HTTPClient) handleHealth(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]string{"status": "ok"})
}

// forwardToModelServer 转发请求到model server
func (h *HTTPClient) forwardToModelServer(req *rtb_types.RTBBidRequest) (*rtb_model_server.RTBModelServerResponse, error) {
	// 创建Thrift连接
	transport, err := thrift.NewTSocket(h.modelServerAddr)
	if err != nil {
		return nil, fmt.Errorf("error creating socket: %v", err)
	}

	transportFactory := thrift.NewTBufferedTransportFactory(8192)
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()

	// 创建客户端
	clientTransport := transportFactory.GetTransport(transport)
	defer clientTransport.Close()

	if err := clientTransport.Open(); err != nil {
		return nil, fmt.Errorf("error opening connection: %v", err)
	}

	client := rtb_model_server.NewRTBModelServerClientFactory(clientTransport, protocolFactory)

	// 记录请求日志
	bReq, _ := json.Marshal(req)
	log.Printf("Forwarding request: %s", string(bReq))

	// 调用服务
	result, err := client.GetAd(req)
	if err != nil {
		return nil, fmt.Errorf("error calling service: %v", err)
	}

	// 记录响应日志
	bResp, _ := json.Marshal(result)
	log.Printf("Received response: %s", string(bResp))

	return result, nil
}
