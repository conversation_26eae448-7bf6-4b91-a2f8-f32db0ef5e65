package modules

import (
	"bufio"
	"context"
	"encoding/json"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/pkg/errors"
	"go.uber.org/zap"

	"rtb_model_server/conf"
	"rtb_model_server/internal/lib"
	"rtb_model_server/internal/zaplog"
)

// ResourceTarget 资源定向配置项
type ResourceTarget struct {
	ResourceID     int32   `json:"resource_id"`
	AdPosIDs       []int64 `json:"ad_pos_ids"`       // 广告位ID列表
	Type           int32   `json:"type"`             // 类型：1-白名单，2-黑名单
	ExchangeIDs    []int32 `json:"exchange_ids"`     // 交易所ID列表
	AdxExchangeIDs []int32 `json:"adx_exchange_ids"` // ADX交易所ID列表
	MediaIDs       []int64 `json:"media_ids"`        // 媒体ID列表
}

// ResourceTargetProcessor 资源定向处理器
type ResourceTargetProcessor struct {
	dirPath     string
	filePrefix  string
	watcher     *fsnotify.Watcher
	ctx         context.Context
	cancel      context.CancelFunc
	mu          sync.RWMutex
	reloadChan  chan struct{}
	lastModTime time.Time
	filePattern *regexp.Regexp
	currentFile string

	// 根据广告导出允许加载的资源定向类型列表，避免资源包过大
	allowResourceTargetList    []int32
	allowResourceTargetHandler func() []int32

	// 索引结构
	resourceTargets []*ResourceTarget
	// 媒体ID -> 资源定向配置项索引
	mediaIndex map[int64]map[int32]*ResourceTarget
	// 广告位ID -> 资源定向配置项索引
	adxExchangeIndex map[int32]map[int32]*ResourceTarget
	// 广告位ID -> 资源定向配置项索引
	adPosIndex map[int64]map[int32]*ResourceTarget
}

var resourceTargetProcessor *ResourceTargetProcessor

// NewResourceTargetProcessor 创建资源定向处理器
func NewResourceTargetProcessor(handler func() []int32) (*ResourceTargetProcessor, error) {
	ctx, cancel := context.WithCancel(context.Background())

	// 从配置文件路径中提取目录和文件前缀
	filePath := conf.GlobalConfig.ModelIndex.ResourceTargetPath
	dirPath := filepath.Dir(filePath)
	fileName := filepath.Base(filePath)

	// 创建文件名模式匹配正则表达式 FILENAME.yyyymmddhhiiss
	filePattern, err := regexp.Compile(`^` + regexp.QuoteMeta(fileName) + `\.\d{14}$`)
	if err != nil {
		return nil, errors.Wrap(err, "failed to compile file pattern")
	}

	processor := &ResourceTargetProcessor{
		dirPath:                    dirPath,
		filePrefix:                 fileName,
		filePattern:                filePattern,
		ctx:                        ctx,
		cancel:                     cancel,
		reloadChan:                 make(chan struct{}, 1),
		resourceTargets:            make([]*ResourceTarget, 0),
		mediaIndex:                 make(map[int64]map[int32]*ResourceTarget),
		adxExchangeIndex:           make(map[int32]map[int32]*ResourceTarget),
		adPosIndex:                 make(map[int64]map[int32]*ResourceTarget),
		allowResourceTargetHandler: handler,
	}

	// 查找并加载最新的配置文件
	if err := processor.findAndLoadLatestFile(); err != nil {
		return nil, errors.Wrap(err, "failed to find and load latest resource targets")
	}

	// 启动文件监控
	if err := processor.startFileWatcher(); err != nil {
		return nil, errors.Wrap(err, "failed to start file watcher")
	}

	resourceTargetProcessor = processor
	return processor, nil
}

// 启动文件监控
func (p *ResourceTargetProcessor) startFileWatcher() error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return errors.Wrap(err, "failed to create file watcher")
	}
	p.watcher = watcher

	// 添加目录到监控列表
	if err := p.watcher.Add(p.dirPath); err != nil {
		zaplog.Logger.Error("failed to add directory to watcher", zap.String("dir", p.dirPath), zap.Error(err))
		return err
	}
	zaplog.Logger.Info("watching resource target directory", zap.String("dir", p.dirPath), zap.String("pattern", p.filePattern.String()))

	// 启动监控协程
	go p.watchFile()
	go p.handleReload()

	return nil
}

// 停止文件监控
func (p *ResourceTargetProcessor) StopFileWatcher() {
	if p.cancel != nil {
		p.cancel()
	}
	if p.watcher != nil {
		p.watcher.Close()
	}
	zaplog.Logger.Info("resource target file watcher stopped")
}

// 监控文件变化
func (p *ResourceTargetProcessor) watchFile() {
	for {
		select {
		case <-p.ctx.Done():
			return
		case event, ok := <-p.watcher.Events:
			if !ok {
				return
			}

			// 检查文件名是否符合格式 FILENAME.yyyymmddhhiiss
			filename := filepath.Base(event.Name)
			if !p.filePattern.MatchString(filename) {
				continue
			}

			zaplog.Logger.Debug("resource target file changed", zap.String("file", event.Name), zap.String("op", event.Op.String()))
			// 只处理写入和创建事件
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				zaplog.Logger.Debug("resource target file modified", zap.String("file", event.Name))

				// 检查文件修改时间，避免短时间内多次触发
				fileInfo, err := os.Stat(event.Name)
				if err != nil {
					zaplog.Logger.Error("failed to get file info", zap.String("file", event.Name), zap.Error(err))
					continue
				}

				// 如果文件修改时间与上次相同或者间隔太短，则跳过
				if !p.lastModTime.IsZero() && fileInfo.ModTime().Sub(p.lastModTime) < time.Second {
					continue
				}
				p.lastModTime = fileInfo.ModTime()

				// 更新当前文件路径
				p.currentFile = event.Name

				// 非阻塞发送重新加载信号
				select {
				case p.reloadChan <- struct{}{}:
					zaplog.Logger.Info("triggering resource target reload", zap.String("new_file", event.Name))
				default:
					// 如果channel已满，说明已经有重新加载在等待，跳过
				}
			}
		case err, ok := <-p.watcher.Errors:
			if !ok {
				return
			}
			zaplog.Logger.Error("file watcher error", zap.Error(err))
		}
	}
}

// 处理重新加载
func (p *ResourceTargetProcessor) handleReload() {
	for {
		select {
		case <-p.ctx.Done():
			return
		case <-p.reloadChan:
			// 等待一小段时间，确保所有文件写入完成
			time.Sleep(10 * time.Second)
			zaplog.Logger.Info("starting reload of resource target file", zap.String("file", p.currentFile))
			if err := p.LoadResourceTargets(); err != nil {
				zaplog.Logger.Error("failed to reload resource targets", zap.Error(err))
			} else {
				zaplog.Logger.Info("resource targets reloaded successfully", zap.String("file", p.currentFile))
			}
		}
	}
}

// findAndLoadLatestFile 查找并加载最新的符合格式的文件
func (p *ResourceTargetProcessor) findAndLoadLatestFile() error {
	// 读取目录中的所有文件
	files, err := os.ReadDir(p.dirPath)
	if err != nil {
		return errors.Wrap(err, "failed to read directory")
	}

	// 查找符合格式的最新文件
	var latestFile string
	var latestTime time.Time

	for _, file := range files {
		// 检查文件名是否符合格式
		if !p.filePattern.MatchString(file.Name()) {
			continue
		}

		// 获取文件信息
		filePath := filepath.Join(p.dirPath, file.Name())
		fileInfo, err := os.Stat(filePath)
		if err != nil {
			continue
		}

		// 比较修改时间，找到最新的文件
		if latestFile == "" || fileInfo.ModTime().After(latestTime) {
			latestFile = filePath
			latestTime = fileInfo.ModTime()
		}
	}

	if latestFile == "" {
		return errors.New("no valid resource target file found")
	}

	// 设置当前文件并加载
	p.currentFile = latestFile
	p.lastModTime = latestTime
	zaplog.Logger.Info("found latest resource target file", zap.String("file", latestFile))

	return p.LoadResourceTargets()
}

// 加载资源定向配置
func (p *ResourceTargetProcessor) LoadResourceTargets() error {
	p.allowResourceTargetList = p.allowResourceTargetHandler()
	zaplog.Logger.Info("allow resource target list", zap.Int32s("list", p.allowResourceTargetList))

	if p.currentFile == "" {
		return errors.New("no current file to load")
	}

	file, err := os.Open(p.currentFile)
	if err != nil {
		return errors.Wrap(err, "failed to open resource target file")
	}
	defer file.Close()

	t1 := time.Now()

	// 创建新的索引结构
	newResourceTargets := make([]*ResourceTarget, 0)
	newMediaIndex := make(map[int64]map[int32]*ResourceTarget)
	newAdxExchengeIndex := make(map[int32]map[int32]*ResourceTarget)
	newAdPosIndex := make(map[int64]map[int32]*ResourceTarget)

	reader := bufio.NewReader(file)
	lineNum := 0

	allowResourceTargetMap := make(map[int32]struct{})
	for _, resourceTarget := range p.allowResourceTargetList {
		allowResourceTargetMap[resourceTarget] = struct{}{}
	}

	// 逐行读取并解析
	for {
		lineNum++
		line, isPrefix, err := reader.ReadLine()
		if err != nil {
			break
		}

		// 处理超长行
		fullLine := string(line)
		for isPrefix {
			line, isPrefix, err = reader.ReadLine()
			if err != nil {
				break
			}
			fullLine += string(line)
		}

		// 跳过表头和空行
		if lineNum == 1 || strings.TrimSpace(fullLine) == "" {
			continue
		}

		// 按制表符分割字段
		fields := strings.Split(fullLine, "\t")
		if len(fields) < 6 {
			zaplog.Logger.Warn("invalid resource target line", zap.Int("line_num", lineNum))
			continue
		}

		// 解析资源ID
		resourceID, err := strconv.ParseInt(fields[0], 10, 32)
		if err != nil {
			zaplog.Logger.Warn("invalid resource_id", zap.String("value", fields[0]), zap.Int("line_num", lineNum))
			continue
		}

		// 解析广告位ID列表（逗号分隔的文本）
		adPosIDs := make([]int64, 0)
		adPosIDStrs := strings.Split(fields[1], ",")
		for _, idStr := range adPosIDStrs {
			if idStr = strings.TrimSpace(idStr); idStr == "" {
				continue
			}
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				zaplog.Logger.Warn("invalid ad_pos_id", zap.String("value", idStr), zap.Int("line_num", lineNum))
				continue
			}
			adPosIDs = append(adPosIDs, id)
		}

		// 解析类型
		targetType, err := strconv.ParseInt(fields[2], 10, 32)
		if err != nil {
			zaplog.Logger.Warn("invalid type", zap.String("value", fields[2]), zap.Int("line_num", lineNum))
			continue
		}

		// 解析交易所ID列表（JSON数组）
		exchangeIDs := make([]int32, 0)
		if fields[3] != "[]" {
			var ids []int32
			if err := json.Unmarshal([]byte(fields[3]), &ids); err != nil {
				zaplog.Logger.Warn("invalid exchange_ids json", zap.String("value", fields[3]), zap.Int("line_num", lineNum))
			} else {
				exchangeIDs = ids
			}
		}

		// 解析ADX交易所ID列表（JSON数组）
		adxExchangeIDs := make([]int32, 0)
		if fields[4] != "[]" {
			var ids []int32
			if err := json.Unmarshal([]byte(fields[4]), &ids); err != nil {
				zaplog.Logger.Warn("invalid adx_exchange_ids json", zap.String("value", fields[4]), zap.Int("line_num", lineNum))
			} else {
				adxExchangeIDs = ids
			}
		}

		// 解析媒体ID列表（JSON数组）
		mediaIDs := make([]int64, 0)
		if fields[5] != "[]" {
			var ids []int64
			if err := json.Unmarshal([]byte(fields[5]), &ids); err != nil {
				zaplog.Logger.Warn("invalid media_ids json", zap.String("value", fields[5]), zap.Int("line_num", lineNum))
			} else {
				mediaIDs = ids
			}
		}

		// 仅缓存允许的资源定向配置项
		if _, ok := allowResourceTargetMap[int32(resourceID)]; !ok {
			continue
		}

		// 创建资源定向配置项
		target := &ResourceTarget{
			ResourceID:     int32(resourceID),
			AdPosIDs:       adPosIDs,
			Type:           int32(targetType),
			ExchangeIDs:    exchangeIDs,
			AdxExchangeIDs: adxExchangeIDs,
			//MediaIDs:       mediaIDs,
		}

		// 添加到配置列表
		newResourceTargets = append(newResourceTargets, target)

		for _, mediaId := range mediaIDs {
			if newMediaIndex[mediaId] == nil {
				newMediaIndex[mediaId] = make(map[int32]*ResourceTarget, 0)
			}
			newMediaIndex[mediaId][target.ResourceID] = target
		}
		for _, adxExchangeId := range target.AdxExchangeIDs {
			if newAdxExchengeIndex[adxExchangeId] == nil {
				newAdxExchengeIndex[adxExchangeId] = make(map[int32]*ResourceTarget, 0)
			}
			newAdxExchengeIndex[adxExchangeId][target.ResourceID] = target
		}
		// 只缓存AdxExchanegeId和MediaId为空的
		for _, adPosId := range target.AdPosIDs {
			if len(mediaIDs) == 0 && len(target.AdxExchangeIDs) == 0 {
				if newAdPosIndex[adPosId] == nil {
					newAdPosIndex[adPosId] = make(map[int32]*ResourceTarget, 0)
				}
				newAdPosIndex[adPosId][target.ResourceID] = target
			}
		}
	}

	// 使用ReadLine方法，错误已在循环中处理

	zaplog.Logger.Info("load resource target file success",
		zap.String("file", p.currentFile),
		zap.Int("count", len(newResourceTargets)),
		zap.Int64("cost", time.Since(t1).Milliseconds()),
		zap.Int("index_media_count", len(newMediaIndex)),
		zap.Int("index_adx_exchange_count", len(newAdxExchengeIndex)),
		zap.Int("index_ad_pos_count", len(newAdPosIndex)),
	)

	p.mu.Lock()
	defer p.mu.Unlock()

	p.resourceTargets = newResourceTargets
	p.mediaIndex = newMediaIndex
	p.adxExchangeIndex = newAdxExchengeIndex
	p.adPosIndex = newAdPosIndex

	return nil
}

// FilterByTargets 根据媒体ID、ADX交易所ID、交易所ID和广告位ID筛选资源
// 返回两个列表：type=1的resource_id列表和type=2的resource_id列表
func (p *ResourceTargetProcessor) FilterByTargets(mediaID int64, adxExchangeID int32, exchangeID int32, adPosID int64) (type1Resources []int32, type2Resources []int32) {
	//case1，根据adxExchangeID来进行匹配
	if adxExchangeID > 0 {
		if targets, ok := p.adxExchangeIndex[adxExchangeID]; ok {
			for _, target := range targets {
				// 如果存在广告位ID列表，且不包含当前广告位ID，跳过
				if !p.isExchangeAndAmdsMatched(adPosID, exchangeID, target) {
					continue
				}
				// 广告位ID匹配，根据类型添加到结果列表
				if target.Type == 1 {
					type1Resources = append(type1Resources, target.ResourceID)
				} else {
					type2Resources = append(type2Resources, target.ResourceID)
				}
			}
		}
	}

	if mediaID != 0 {
		if targets, ok := p.mediaIndex[mediaID]; ok {
			for _, target := range targets {
				// 如果存在广告位ID列表，且不包含当前广告位ID，跳过
				if !p.isExchangeAndAmdsMatched(adPosID, exchangeID, target) {
					continue
				}
				// 广告位ID匹配，根据类型添加到结果列表
				if target.Type == 1 {
					type1Resources = append(type1Resources, target.ResourceID)
				} else {
					type2Resources = append(type2Resources, target.ResourceID)
				}
			}
		}
	}
	if adPosID != 0 {
		if targets, ok := p.adPosIndex[adPosID]; ok {
			for _, target := range targets {
				// 如果存在广告位ID列表，且不包含当前广告位ID，跳过
				if !p.isExchangeAndAmdsMatched(adPosID, exchangeID, target) {
					continue
				}
				// 广告位ID匹配，根据类型添加到结果列表
				if target.Type == 1 {
					type1Resources = append(type1Resources, target.ResourceID)
				} else {
					type2Resources = append(type2Resources, target.ResourceID)
				}
			}
		}
	}
	return
}

func (p *ResourceTargetProcessor) isExchangeAndAmdsMatched(adPosID int64, exchangeID int32, target *ResourceTarget) bool {
	if !lib.InSlice[int64](target.AdPosIDs, adPosID) {
		return false
	}
	if !lib.InSlice[int32](target.ExchangeIDs, exchangeID) {
		return false
	}
	return true
}
