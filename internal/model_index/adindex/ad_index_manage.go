package adindex

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
	"strings"
	"sync"
	"time"

	"git.apache.org/thrift.git/lib/go/thrift"
	"github.com/fsnotify/fsnotify"
	"github.com/pkg/errors"
	"go.uber.org/zap"
)

type AdIndexManager struct {
	config            *conf.ModelIndexConfig
	watcher           *fsnotify.Watcher
	ctx               context.Context
	cancel            context.CancelFunc
	fileUpdateTracker map[string]time.Time
	mu                sync.RWMutex
	reloadChan        chan struct{}
	// 新增版本号相关字段
	currentVersion  string
	versionFilePath string
	// 加载状态跟踪
	lastLoadSuccess bool
	indexUpdateTime time.Time
	lastLoadTime    time.Time
}

// 为不同类型的数据创建对应的sync.Map
var (
	dictAdCampaign  = sync.Map{}
	dictAdStrategy  = sync.Map{}
	dictAdCreative  = sync.Map{}
	dictAdSponsor   = sync.Map{}
	dictAdPromotion = sync.Map{}
	dictAdTracking  = sync.Map{}
	// 资源包缓存
	dictResourceUsed = sync.Map{}
	// 预算数据
	dictCampaignBudget = sync.Map{}
	dictStrategyBudget = sync.Map{}
)

func NewAdIndex() *AdIndexManager {
	ctx, cancel := context.WithCancel(context.Background())
	adIndex := &AdIndexManager{
		config:            &conf.GlobalConfig.ModelIndex,
		ctx:               ctx,
		cancel:            cancel,
		fileUpdateTracker: make(map[string]time.Time),
		reloadChan:        make(chan struct{}, 1),
		lastLoadSuccess:   false, // 初始状态为未成功加载
		lastLoadTime:      time.Now(),
	}

	// 设置VERSION文件路径
	adIndex.versionFilePath = filepath.Join(filepath.Dir(adIndex.config.AdIndexPath.AdCampaign), "VERSION")

	return adIndex
}

// StartFileWatcher 启动文件监控协程
func (i *AdIndexManager) StartFileWatcher() error {
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return errors.Wrap(err, "failed to create file watcher")
	}
	i.watcher = watcher

	// 只监控VERSION文件所在的目录
	versionDir := filepath.Dir(i.versionFilePath)
	if err := i.watcher.Add(versionDir); err != nil {
		return errors.Wrap(err, "failed to add version directory to watcher")
	}

	zaplog.Logger.Info("watching VERSION file", zap.String("path", i.versionFilePath))

	// 启动监控协程
	go i.watchVersionFile()
	go i.handleReload()

	zaplog.Logger.Info("file watcher started successfully")
	return nil
}

// StopFileWatcher 停止文件监控
func (i *AdIndexManager) StopFileWatcher() {
	if i.cancel != nil {
		i.cancel()
	}
	if i.watcher != nil {
		i.watcher.Close()
	}
	zaplog.Logger.Info("file watcher stopped")
}

// GetAdIndexFileExpiredOrMissing 检查广告索引文件是否过期或缺失（基于内存变量）
func (i *AdIndexManager) GetAdIndexFileExpiredOrMissing() bool {
	i.mu.RLock()
	lastLoadSuccess := i.lastLoadSuccess
	currentVersion := i.currentVersion
	indexUpdateTime := i.indexUpdateTime
	i.mu.RUnlock()

	// 如果上次加载失败，直接返回true
	if !lastLoadSuccess {
		zaplog.Logger.Warn("last ad index load failed, rejecting requests")
		return true
	}

	// 如果当前版本为空，认为文件缺失
	if currentVersion == "" {
		zaplog.Logger.Warn("current version is empty, considering as missing")
		return true
	}

	// 如果最后加载时间为零值，认为文件缺失
	if indexUpdateTime.IsZero() {
		zaplog.Logger.Warn("last load time is zero, considering as missing")
		return true
	}

	// 获取配置的过期时间（分钟），默认为10分钟
	expireMinutes := i.config.AdIndexFileExpireMinutes
	if expireMinutes <= 0 {
		expireMinutes = 10 // 默认值
	}

	// 检查是否超过配置的过期时间
	isExpired := time.Since(indexUpdateTime) > time.Duration(expireMinutes)*time.Minute
	return isExpired
}

// readVersionFromFile 从VERSION文件读取版本号
func (i *AdIndexManager) readVersionFromFile() (string, error) {
	data, err := os.ReadFile(i.versionFilePath)
	if err != nil {
		return "", errors.Wrap(err, "failed to read version file")
	}
	version := strings.TrimSpace(string(data))
	if version == "" {
		return "", errors.New("VERSION file is empty")
	}
	return version, nil
}

// getVersionedFilePath 根据版本号构建实际文件路径
func (i *AdIndexManager) getVersionedFilePath(basePath string) string {
	if i.currentVersion == "" {
		return basePath
	}
	return basePath + "." + i.currentVersion
}

// updateVersion 更新版本号并检查文件是否存在
func (i *AdIndexManager) updateVersion() error {
	version, err := i.readVersionFromFile()
	if err != nil {
		return err
	}

	// 检查版本是否发生变化
	if i.currentVersion == version {
		return nil
	}

	// 验证所有版本化文件是否存在
	filePaths := []string{
		i.config.AdIndexPath.AdCampaign,
		i.config.AdIndexPath.AdStrategy,
		i.config.AdIndexPath.AdCreative,
		i.config.AdIndexPath.AdSponsor,
		i.config.AdIndexPath.AdPromotion,
		i.config.AdIndexPath.AdTracking,
	}
	for _, basePath := range filePaths {
		versionedPath := basePath + "." + version
		if _, err := os.Stat(versionedPath); err != nil {
			// 文件缺失时返回错误
			errorMsg := fmt.Sprintf("versioned file not found: %s", versionedPath)
			return errors.Wrapf(err, errorMsg)
		}
	}

	i.mu.Lock()
	i.currentVersion = version
	i.mu.Unlock()

	zaplog.Logger.Info("version updated", zap.String("version", version))
	return nil
}

// watchVersionFile 监控VERSION文件变化
func (i *AdIndexManager) watchVersionFile() {
	for {
		select {
		case <-i.ctx.Done():
			return
		case event, ok := <-i.watcher.Events:
			if !ok {
				return
			}

			// 只处理VERSION文件的变化
			if filepath.Base(event.Name) != "VERSION" {
				continue
			}
			zaplog.Logger.Info("VERSION file changed", zap.String("file", event.Name), zap.String("op", event.Op.String()))
			// 只处理写入和创建事件
			if event.Op&fsnotify.Write == fsnotify.Write || event.Op&fsnotify.Create == fsnotify.Create {
				// 非阻塞发送重新加载信号
				select {
				case i.reloadChan <- struct{}{}:
					zaplog.Logger.Info("VERSION file updated, triggering reload")
				default:
					// 如果channel已满，说明已经有重新加载在等待，跳过
				}
			}

		case err, ok := <-i.watcher.Errors:
			if !ok {
				return
			}
			zaplog.Logger.Error("file watcher error", zap.Error(err))
		}
	}
}

// handleReload 处理重新加载请求
func (i *AdIndexManager) handleReload() {
	for {
		select {
		case <-i.ctx.Done():
			return
		case <-i.reloadChan:
			// 等待一小段时间，确保所有文件写入完成
			time.Sleep(2 * time.Second)

			zaplog.Logger.Info("starting atomic reload of all index files")
			if err := i.LoadAdIndexData(); err != nil {
				zaplog.Logger.Error("failed to reload index data", zap.Error(err))
				// 加载失败时更新状态
				i.mu.Lock()
				i.lastLoadSuccess = false
				i.lastLoadTime = time.Now()
				i.mu.Unlock()
			} else {
				zaplog.Logger.Info("index data reloaded successfully")
				// 清空文件更新追踪器
				i.mu.Lock()
				i.fileUpdateTracker = make(map[string]time.Time)
				i.lastLoadSuccess = true
				i.lastLoadTime = time.Now()
				i.mu.Unlock()
			}
		}
	}
}

func (i *AdIndexManager) LoadAdIndexData() error {
	return i.loadAdIndexData()
}

func (i *AdIndexManager) loadAdIndexData() error {
	// 首先更新版本号
	if err := i.updateVersion(); err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "failed to update version")
	}

	t1 := time.Now()
	sponsorCount, err := i.LoadSponsor()
	if err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "load sponsor error")
	}
	campaignCount, err := i.LoadCampaign()
	if err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "load campaign error")
	}
	strategyCount, err := i.LoadStrategy()
	if err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "load strategy error")
	}

	promotionCount, err := i.LoadPromotion()
	if err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "load promotion error")
	}
	trackingCount, err := i.LoadAdTracking()
	if err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "load tracking error")
	}
	creativeCount, err := i.LoadCreative()
	if err != nil {
		i.mu.Lock()
		i.lastLoadSuccess = false
		i.lastLoadTime = time.Now()
		i.mu.Unlock()
		return errors.Wrap(err, "load creative error")
	}

	// 所有加载成功，更新状态
	i.mu.Lock()
	i.lastLoadSuccess = true
	i.lastLoadTime = time.Now()
	i.indexUpdateTime, _ = time.ParseInLocation("20060102_150405", i.currentVersion, time.Local)
	i.mu.Unlock()

	zaplog.Logger.Info("load ad index success",
		zap.String("version", i.currentVersion),
		zap.Int("campaignCount", campaignCount),
		zap.Int("strategyCount", strategyCount),
		zap.Int("creativeCount", creativeCount),
		zap.Int("sponsorCount", sponsorCount),
		zap.Int("promotionCount", promotionCount),
		zap.Int("trackingCount", trackingCount),
		zap.Int("resourceTargetCount", i.ResourceTargetPkgNumber()),
		zap.Duration("cost", time.Duration(time.Since(t1).Milliseconds())),
	)
	return nil
}

func (i *AdIndexManager) loadAdCampaign() {
	i.LoadCampaign()
}

// Indexable 定义一个接口，所有可索引的数据类型都需要实现
type Indexable interface {
	GetId() int32
	Read(thrift.TProtocol) error
}

// 包装器类型，用于实现Indexable接口
type IndexableCampaign struct {
	*rtb_adinfo_types.RTBCampaign
}

type IndexableStrategy struct {
	*rtb_adinfo_types.RTBStrategy
}

type IndexableCreative struct {
	*rtb_adinfo_types.RTBCreative
}

type IndexableSponsor struct {
	*rtb_adinfo_types.RTBSponsor
}

type IndexablePromotion struct {
	*rtb_adinfo_types.RTBPromotion
}

type IndexableAdTracking struct {
	*rtb_adinfo_types.AdTrackingInfo
}

// 实现Indexable接口
func (i IndexableCampaign) GetId() int32 {
	return i.Id
}

func (i IndexableStrategy) GetId() int32 {
	return i.Id
}

func (i IndexableCreative) GetId() int32 {
	return i.Id
}

func (i IndexableSponsor) GetId() int32 {
	return i.Id
}

func (i IndexablePromotion) GetId() int32 {
	return i.Id
}

func (i IndexableAdTracking) GetId() int32 {
	return i.Id
}

func (i IndexableCampaign) Read(p thrift.TProtocol) error {
	return i.RTBCampaign.Read(p)
}

func (i IndexableStrategy) Read(p thrift.TProtocol) error {
	return i.RTBStrategy.Read(p)
}

func (i IndexableCreative) Read(p thrift.TProtocol) error {
	return i.RTBCreative.Read(p)
}

func (i IndexableSponsor) Read(p thrift.TProtocol) error {
	return i.RTBSponsor.Read(p)
}

func (i IndexablePromotion) Read(p thrift.TProtocol) error {
	return i.RTBPromotion.Read(p)
}

func (i IndexableAdTracking) Read(p thrift.TProtocol) error {
	return i.AdTrackingInfo.Read(p)
}

// GetIndexMap 根据类型获取对应的map
func GetIndexMap(indexType string) *sync.Map {
	switch indexType {
	case "campaign":
		return &dictAdCampaign
	case "strategy":
		return &dictAdStrategy
	case "creative":
		return &dictAdCreative
	case "sponsor":
		return &dictAdSponsor
	case "promotion":
		return &dictAdPromotion
	case "ad_tracking":
		return &dictAdTracking
	default:
		return nil
	}
}

// FilterFunc 定义过滤函数类型
type FilterFunc[T Indexable] func(T) bool

// LoadAdIndexData 通用的加载广告索引数据的函数，支持自定义过滤器
func LoadAdIndexData[T Indexable](indexPath string, indexType string, factory func() T, filter FilterFunc[T]) (int, error) {
	fd, err := os.Open(indexPath)
	if err != nil {
		return 0, err
	}
	defer fd.Close()

	transport := thrift.NewStreamTransportR(fd)
	protocol := thrift.NewTBinaryProtocolTransport(transport)
	if err := transport.Open(); err != nil {
		return 0, err
	}
	defer transport.Close()

	// 获取对应类型的map
	indexMap := GetIndexMap(indexType)
	if indexMap == nil {
		return 0, nil
	}

	// 记录旧数据
	oldEventMap := make(map[int32]bool)
	indexMap.Range(func(key, value interface{}) bool {
		if indexable, ok := value.(Indexable); ok {
			oldEventMap[indexable.GetId()] = false
		}
		return true
	})

	var count int
	var filteredCount int
	// 主循环：读取数据并存储到map
	for {
		// 使用工厂函数创建新实例
		event := factory()
		err := event.Read(protocol)
		if err != nil {
			if err == io.EOF || strings.Contains(err.Error(), "EOF") {
				break
			}
			transport.Flush()
			continue
		}
		count++

		// 应用过滤器
		if filter != nil && !filter(event) {
			filteredCount++
			transport.Flush()
			continue
		}

		indexMap.Store(event.GetId(), event)
		oldEventMap[event.GetId()] = true
		transport.Flush()
	}

	// 清理旧数据
	for id, exist := range oldEventMap {
		if !exist {
			indexMap.Delete(id)
		}
	}

	// 记录过滤统计信息
	if filteredCount > 0 {
		zaplog.Logger.Info("data filtered during loading",
			zap.String("indexType", indexType),
			zap.Int("totalRead", count),
			zap.Int("filtered", filteredCount),
			zap.Int("loaded", count-filteredCount))
	}

	return count - filteredCount, nil
}
