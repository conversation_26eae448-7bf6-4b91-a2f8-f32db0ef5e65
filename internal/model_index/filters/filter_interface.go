package filters

import (
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/lib"
)

type FilterType int

type Filters interface {
	Filter(ctx *ctx.RequestContext) FilterType
}

type FilterResult struct {
	FilterType FilterType
	Cid        int32
}

const (
	// 无过滤
	FilterTypeNone FilterType = iota
	// 异常过滤
	FilterTypeCreateContextException
	// 异常过滤
	FilterTypeFilterException

	// INDEX文件相关过滤
	// INDEX文件过期（或者文件有异常）
	FilterTypeIndexExpiredOrInvalid

	// Campgin层级
	// 投放时间过滤（计划和广告组）
	FilterTypeDeliveryTime
	// 预算原因过滤
	FilterTypeBudgetReason

	// Startegy层级
	// 交易所ID过滤
	FilterTypeExchangeTarget
	// 已安装过滤
	FilterTypeInstalledApps
	// 资源包过滤
	FilterTypeResourceTarget
	// 人群定向
	FilterTypeDMPTarget
	// 国家定向
	FilterTypeCountryTarget
	// 城市定向
	FilterTypeCityTarget
	// 手机品牌定向
	FilterTypeDeviceTarget
	// 系统定向
	FilterTypeOSVersionTarget
	// 网络类型定向
	FilterTypeNetworkTypeTarget
	// 运营商定向
	FilterTypeCarrierTarget
	// 交易所人群定向
	FilterTypeExchangeDmpTarget
	// 性别定向
	FilterTypeGenderTarget
	// 年龄定向
	FilterTypeAgeTarget
	// 兴趣定向
	FilterTypeInterestTarget
	// 出价类型过滤
	FilterTypeBidTypeTarget
	// 广告位过滤
	FilterTypeTagIdTarget

	// Creative层级
	// 广告位（AdMatchType）
	FilterTypeAdMatchTypeTarget
	// 请求能力要求
	FilterTypeAbility
	// 审核状态过滤
	FilterTypeAuditStatus

	// Creative Dim定向
	FilterTypeCreativeDimTarget
	// 视频时长定向
	FilterTypeVideoDurationTarget

	//流量相关
	// 点击频控过滤
	FilterTypeAdClkFrequency
	// 曝光频控过滤
	FilterTypeAdImpFrequency
	// RTA过滤
	FilterTypeForbiddenSponsor
)

// 不在在此处才会进入过滤日志，避免日志过大
var funnelLogForbiddenFilterType = []FilterType{
	FilterTypeAdMatchTypeTarget,
}

// 暂时不用改方法
func FunnelLogWithFilterType(filterType FilterType) bool {
	return !lib.InSlice(funnelLogForbiddenFilterType, filterType)
}
