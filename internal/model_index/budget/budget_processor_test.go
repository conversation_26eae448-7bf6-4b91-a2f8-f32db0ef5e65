package budget

import (
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/conf"
	"rtb_model_server/internal/context"
	"rtb_model_server/internal/mock"
	"rtb_model_server/internal/model_index/adindex"
	"rtb_model_server/internal/zaplog"
	"testing"
	"time"

	"go.uber.org/zap"
)

func init() {
	// 初始化配置
	conf.ConfigInit("conf/rtb_model_server.yaml")
	// 初始化日志
	zaplog.Logger = zap.NewNop()
}

// AdIndexInterface defines the interface for AdIndex operations
type AdIndexInterface interface {
	GetCreative(cid int32) (*rtb_adinfo_types.RTBCreative, error)
	GetStrategy(strategyId int32) (*rtb_adinfo_types.RTBStrategy, error)
	GetCampaign(campaignId int32) (*rtb_adinfo_types.RTBCampaign, error)
	GetStrategyBudget(strategyId int32) (int64, bool)
}

// Ensure AdIndexManager implements the interface
var _ AdIndexInterface = (*adindex.AdIndexManager)(nil)

// Ensure MockAdIndex implements the interface
var _ AdIndexInterface = (*mock.MockAdIndex)(nil)

// TestBudgetProcessorSingleton 测试预算处理器单例模式
func TestBudgetProcessorSingleton(t *testing.T) {
	// 获取两个实例
	processor1 := GetBudgetProcessor()
	processor2 := GetBudgetProcessor()

	// 验证是同一个实例
	if processor1 != processor2 {
		t.Error("BudgetProcessor should be singleton")
	}

	// 验证实例不为空
	if processor1 == nil {
		t.Fatal("BudgetProcessor instance should not be nil")
	}

	t.Log("BudgetProcessor singleton test passed")
}

// TestBudgetStrategies 测试预算控制策略
func TestBudgetStrategies(t *testing.T) {
	processor := GetBudgetProcessor()

	// 测试策略注册
	strategies := processor.GetStrategies()
	if len(strategies) == 0 {
		t.Error("No strategies registered")
	}

	// 验证默认策略存在
	expectedStrategies := []string{"base", "cpc", "awake", "random"}
	for _, expected := range expectedStrategies {
		if _, exists := strategies[expected]; !exists {
			t.Errorf("Strategy '%s' not found", expected)
		}
	}

	t.Logf("Registered strategies: %v", getStrategyNames(strategies))
}

// TestAddRemoveStrategy 测试添加和移除策略
func TestAddRemoveStrategy(t *testing.T) {
	// 初始化测试用的Logger
	zaplog.Logger = zap.NewNop()

	processor := GetBudgetProcessor()

	// 添加自定义策略
	customStrategy := NewBaseBudgetStrategy()
	processor.AddStrategy("custom", customStrategy)

	// 验证策略已添加
	strategies := processor.GetStrategies()
	if _, exists := strategies["custom"]; !exists {
		t.Error("Custom strategy not added")
	}

	// 移除策略
	processor.RemoveStrategy("custom")

	// 验证策略已移除
	strategies = processor.GetStrategies()
	if _, exists := strategies["custom"]; exists {
		t.Error("Custom strategy not removed")
	}

	t.Log("Add/Remove strategy test passed")
}

// TestRequestContext 测试专用的RequestContext，用于模拟AdIndex
type TestRequestContext struct {
	*context.RequestContext
	MockAdIndex AdIndexInterface
}

// GetCreative 模拟获取创意
func (ctx *TestRequestContext) GetCreative(cid int32) (*rtb_adinfo_types.RTBCreative, error) {
	return ctx.MockAdIndex.GetCreative(cid)
}

// GetStrategy 模拟获取策略
func (ctx *TestRequestContext) GetStrategy(strategyId int32) (*rtb_adinfo_types.RTBStrategy, error) {
	return ctx.MockAdIndex.GetStrategy(strategyId)
}

// GetCampaign 模拟获取活动
func (ctx *TestRequestContext) GetCampaign(campaignId int32) (*rtb_adinfo_types.RTBCampaign, error) {
	return ctx.MockAdIndex.GetCampaign(campaignId)
}

// GetStrategyBudget 模拟获取策略预算
func (ctx *TestRequestContext) GetStrategyBudget(strategyId int32) (int64, bool) {
	return ctx.MockAdIndex.GetStrategyBudget(strategyId)
}

// TestApplyBudgetControl 测试预算控制应用
func TestApplyBudgetControl(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.BudgetConfig = conf.BudgetConfig{
		SpeedStopRate:        0.1,
		SmallBudgetThreshold: 1000,
		MaxBidPerMinute:      60,
		BudgetThresholdRatio: 0.1,
		DecelerationFactor:   3.0,
		DefaultMediaPrice:    100,
		StatsReloadInterval:  300,
		StatsFilePath:        "test_stats.data",
	}

	processor := GetBudgetProcessor()

	// 创建模拟上下文
	mockAdIndex := mock.NewMockAdIndex()
	mockAdIndex.CreateTestData()

	// 创建测试上下文
	testCtx := &TestRequestContext{
		RequestContext: &context.RequestContext{
			CreativeFilterMap: make(map[int32]int32),
		},
		MockAdIndex: mockAdIndex,
	}

	// 测试策略选择逻辑
	cids := []int32{1001, 1002, 1003}

	// 测试各个策略的选择
	for _, cid := range cids {
		creative, err := mockAdIndex.GetCreative(cid)
		if err != nil {
			t.Errorf("Failed to get creative %d: %v", cid, err)
			continue
		}

		strategy, err := mockAdIndex.GetStrategy(creative.StrategyId)
		if err != nil {
			t.Errorf("Failed to get strategy %d: %v", creative.StrategyId, err)
			continue
		}

		// 测试策略选择
		budgetStrategy := processor.selectStrategy(int32(strategy.CostType), int32(creative.AdMatchType))
		t.Logf("CID %d: CostType=%d, AdMatchType=%d, Selected Strategy=%s",
			cid, strategy.CostType, creative.AdMatchType, budgetStrategy.GetStrategyName())
	}

	// 测试Process方法（兼容性测试）
	_, err := processor.Process(testCtx.RequestContext, cids)
	if err != nil {
		t.Errorf("Process should not return error: %v", err)
	}

	// 检查过滤结果
	for _, cid := range cids {
		if filterCode, filtered := testCtx.CreativeFilterMap[cid]; filtered {
			t.Logf("CID %d filtered with code %d", cid, filterCode)
		} else {
			t.Logf("CID %d passed budget control", cid)
		}
	}
}

// TestSelectStrategy 测试策略选择
func TestSelectStrategy(t *testing.T) {
	processor := GetBudgetProcessor()

	// 测试CPC策略选择
	cpcStrategy := processor.selectStrategy(2, 0) // costType=2 表示CPC
	if cpcStrategy.GetStrategyName() != "CPCBudgetStrategy" {
		t.Errorf("Should select CPC strategy for costType=2, got: %s", cpcStrategy.GetStrategyName())
	}

	// 测试唤醒策略选择
	awakeStrategy := processor.selectStrategy(1, 8) // adMatchType=8 表示唤醒类广告
	if awakeStrategy.GetStrategyName() != "AwakeBudgetStrategy" {
		t.Errorf("Should select Awake strategy for adMatchType=8, got: %s", awakeStrategy.GetStrategyName())
	}

	// 测试默认策略选择
	defaultStrategy := processor.selectStrategy(1, 1) // 普通广告
	if defaultStrategy.GetStrategyName() != "BaseBudgetStrategy" {
		t.Errorf("Should select Base strategy for regular ads, got: %s", defaultStrategy.GetStrategyName())
	}

	t.Log("Strategy selection test passed")
}

// TestStatsProcessor 测试统计处理器集成
func TestStatsProcessor(t *testing.T) {
	processor := GetBudgetProcessor()

	// 测试统计数据更新
	processor.UpdateBudgetConsumption(1001, 100, 1, 0)

	// 获取统计信息
	stats := processor.GetStats(1001)
	if stats == nil {
		t.Error("Stats should not be nil after update")
	} else {
		t.Logf("Stats for 1001: Cost=%f, Impressions=%d", stats.Cost, stats.Impressions)
	}

	// 获取所有统计信息
	allStats := processor.GetAllStats()
	if len(allStats) == 0 {
		t.Log("No stats available (expected for test)")
	} else {
		t.Logf("Total stats count: %d", len(allStats))
	}
}

// TestBudgetControlStats 测试预算控制统计信息
func TestBudgetControlStats(t *testing.T) {
	processor := GetBudgetProcessor()

	// 获取统计信息
	stats := processor.GetBudgetControlStats()
	if stats == nil {
		t.Fatal("Budget control stats should not be nil")
	}

	// 验证统计信息包含预期字段
	expectedFields := []string{"registered_strategies", "strategy_names", "default_strategy"}
	for _, field := range expectedFields {
		if _, exists := stats[field]; !exists {
			t.Errorf("Stats should contain field '%s'", field)
		}
	}

	t.Logf("Budget control stats: %+v", stats)
}

// TestProcessCompatibility 测试与旧接口的兼容性
func TestProcessCompatibility(t *testing.T) {
	// 初始化配置
	conf.GlobalConfig.BudgetConfig = conf.BudgetConfig{
		StatsReloadInterval: 300,
		StatsFilePath:       "test_stats.data",
	}

	processor := GetBudgetProcessor()

	// 创建模拟上下文
	mockAdIndex := mock.NewMockAdIndex()
	mockAdIndex.CreateTestData()

	ctx := &context.RequestContext{
		CreativeFilterMap: make(map[int32]int32),
	}

	// 测试Process方法
	creativeIds := []int32{1001, 1002}
	_, err := processor.Process(ctx, creativeIds)
	if err != nil {
		t.Errorf("Process should not return error: %v", err)
	}

	// 检查过滤结果
	for _, cid := range creativeIds {
		if filterCode, filtered := ctx.CreativeFilterMap[cid]; filtered {
			t.Logf("CID %d filtered with code %d", cid, filterCode)
		} else {
			t.Logf("CID %d passed budget control", cid)
		}
	}
}

// TestDefaultStrategy 测试默认策略设置
func TestDefaultStrategy(t *testing.T) {
	processor := GetBudgetProcessor()

	// 获取当前默认策略
	originalDefault := processor.defaultStrategy

	// 设置新的默认策略
	newDefault := NewCPCBudgetStrategy()
	processor.SetDefaultStrategy(newDefault)

	// 验证默认策略已更改
	if processor.defaultStrategy != newDefault {
		t.Error("Default strategy not updated")
	}

	// 恢复原始默认策略
	processor.SetDefaultStrategy(originalDefault)

	t.Log("Default strategy test passed")
}

// TestAutoReload 测试自动重载功能
func TestPolling(t *testing.T) {
	processor := GetBudgetProcessor()

	// 测试停止轮询方法调用
	processor.StopPolling()

	// 等待一小段时间
	time.Sleep(100 * time.Millisecond)

	t.Log("Polling test passed")
}

// 辅助函数：获取策略名称列表
func getStrategyNames(strategies map[string]BudgetStrategy) []string {
	names := make([]string, 0, len(strategies))
	for name := range strategies {
		names = append(names, name)
	}
	return names
}
