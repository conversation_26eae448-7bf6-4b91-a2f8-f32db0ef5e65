package budget

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"

	"github.com/fsnotify/fsnotify"
	"go.uber.org/zap"
)

// 统计处理器单例
type StatsProcessor struct {
	statsMap         map[int32]*BudgetStats // 统计信息映射
	strategyStatsMap map[int32]*Stats       // 策略层级统计映射
	campaignStatsMap map[int32]*Stats       // 活动层级统计映射
	mu               sync.RWMutex           // 读写锁
	lastLoadTime     time.Time              // 最后加载时间
	stopChan         chan struct{}          // 停止信号通道
	// 文件监听相关字段
	currentStatsFile string         // 当前加载的统计文件路径
	fileNamePattern  *regexp.Regexp // 文件名匹配模式
	watcher          *fsnotify.Watcher // 文件系统监听器
	lastModTime      time.Time      // 文件最后修改时间
}

var (
	statsProcessorInstance *StatsProcessor
	statsProcessorOnce     sync.Once
)

// 获取统计处理器单例
func GetStatsProcessor() *StatsProcessor {
	statsProcessorOnce.Do(func() {
		statsProcessorInstance = &StatsProcessor{
			statsMap:         make(map[int32]*BudgetStats),
			strategyStatsMap: make(map[int32]*Stats),
			campaignStatsMap: make(map[int32]*Stats),
			lastLoadTime:     time.Now(),
			stopChan:         make(chan struct{}),
		}

		// 初始化文件监听
		if err := statsProcessorInstance.initFileWatching(); err != nil {
			if zaplog.Logger != nil {
				zaplog.Logger.Error("failed to initialize file watching", zap.Error(err))
			}
		}

		// 程序启动时加载最新的统计文件
		if err := statsProcessorInstance.LoadStats(); err != nil {
			if zaplog.Logger != nil {
				zaplog.Logger.Error("failed to load stats on startup", zap.Error(err))
			}
		} else {
			if zaplog.Logger != nil {
				zaplog.Logger.Info("stats loaded successfully on startup",
					zap.String("loaded_file", statsProcessorInstance.currentStatsFile))
			}
		}
	})
	return statsProcessorInstance
}

// 加载统计数据
func (s *StatsProcessor) LoadStats() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 获取最新的统计文件
	statsFilePath, err := s.getLatestStatsFile()
	if err != nil {
		return fmt.Errorf("failed to get latest stats file: %v", err)
	}

	if statsFilePath == s.currentStatsFile {
		zaplog.Logger.Info("stats file not changed, skip loading", zap.String("path", statsFilePath))
		return nil
	}

	if statsFilePath == "" {
		// 如果没有找到匹配的文件，尝试使用配置的固定路径
		statsFilePath = conf.GlobalConfig.BudgetConfig.StatsFilePath
	}

	file, err := os.Open(statsFilePath)
	if err != nil {
		if os.IsNotExist(err) {
			if zaplog.Logger != nil {
				zaplog.Logger.Error("stats file not exist, skip loading", zap.String("path", statsFilePath), zap.Error(err))
			}
			return nil
		}
		return fmt.Errorf("failed to open stats file: %v", err)
	}
	defer file.Close()

	// 更新当前文件路径
	s.currentStatsFile = statsFilePath

	// 更新文件最后修改时间
	if fileInfo, err := os.Stat(statsFilePath); err == nil {
		s.lastModTime = fileInfo.ModTime()
	}

	newStatsMap := make(map[int32]*BudgetStats)
	scanner := bufio.NewScanner(file)
	lineNum := 0
	for scanner.Scan() {
		lineNum++
		line := strings.TrimSpace(scanner.Text())
		if line == "" || strings.HasPrefix(line, "#") {
			continue
		}

		stats, err := s.parseStatsLine(line)
		if err != nil {
			zaplog.Logger.Debug("failed to parse stats line",
				zap.Int("line_num", lineNum),
				zap.String("line", line),
				zap.Error(err))
			continue
		}

		newStatsMap[stats.CreativeId] = stats
	}

	if err := scanner.Err(); err != nil {
		return fmt.Errorf("error reading stats file: %v", err)
	}

	s.statsMap = newStatsMap
	s.lastLoadTime = time.Now()

	if zaplog.Logger != nil {
		zaplog.Logger.Info("stats loaded successfully",
			zap.String("path", statsFilePath),
			zap.Int("count", len(newStatsMap)))
	}

	return nil
}

// 解析统计数据行
// SQL查询结果格式：uid, pid, sid, cid, sum(bid) as bid, sum(win) as win, sum(imp) as imp, sum(clk) as clk, sum(act) as act, sum(media_price) as media_price, sum(settled_ad_price) as price
// 数据以tab分隔
func (s *StatsProcessor) parseStatsLine(line string) (*BudgetStats, error) {
	fields := strings.Split(line, "\t")
	if len(fields) != 11 {
		return nil, fmt.Errorf("invalid stats line format, expected 11 fields but got %d", len(fields))
	}

	stats := &BudgetStats{}

	// 解析用户ID (uid) - 字段0，暂时不使用
	// uid := fields[0]

	// 解析策略ID (pid) - 字段1
	if campaignId, err := strconv.ParseInt(fields[1], 10, 32); err == nil {
		stats.CampaignId = int32(campaignId)
		if campaignId <= 0 {
			return nil, fmt.Errorf("invalid campaign_id (pid): %s", fields[1])
		}
	} else {
		return nil, fmt.Errorf("invalid campaign_id (pid): %s", fields[1])
	}

	// 解析活动ID (sid) - 字段2
	if strategyId, err := strconv.ParseInt(fields[2], 10, 32); err == nil {
		stats.StrategyId = int32(strategyId)
		if strategyId <= 0 {
			return nil, fmt.Errorf("invalid strategy_id (sid): %s", fields[2])
		}
	} else {
		return nil, fmt.Errorf("invalid strategy_id (sid): %s", fields[2])
	}

	// 解析创意ID (cid) - 字段3
	if creativeId, err := strconv.ParseInt(fields[3], 10, 32); err == nil {
		stats.CreativeId = int32(creativeId)
		if creativeId <= 0 || creativeId > 100000000 {
			return nil, fmt.Errorf("invalid creative_id (cid): %s", fields[3])
		}
	} else {
		return nil, fmt.Errorf("invalid creative_id (cid): %s", fields[3])
	}

	// 解析竞价总额 (bid) - 字段4，暂时不使用
	// bid := fields[4]

	// 解析赢得竞价次数 (win) - 字段5，暂时不使用
	// win := fields[5]

	// 解析展示次数 (imp) - 字段6
	if impressions, err := strconv.ParseInt(fields[6], 10, 64); err == nil {
		stats.Impressions = impressions
	} else {
		return nil, fmt.Errorf("invalid impressions (imp): %s", fields[6])
	}

	// 解析点击次数 (clk) - 字段7
	if clicks, err := strconv.ParseInt(fields[7], 10, 64); err == nil {
		stats.Clicks = clicks
	} else {
		return nil, fmt.Errorf("invalid clicks (clk): %s", fields[7])
	}

	// 解析转化次数 (act) - 字段8，暂时不使用
	// act := fields[8]

	// 解析媒体价格 (media_price) - 字段9，暂时不使用
	// mediaPrice := fields[9]

	// 解析结算广告价格 (price) - 字段10，作为消费金额
	if cost, err := strconv.ParseFloat(fields[10], 64); err == nil {
		stats.Cost = cost
	} else {
		return nil, fmt.Errorf("invalid cost (price): %s", fields[10])
	}

	// 暂时将ExchangeId设为0，因为SQL查询结果中没有这个字段
	stats.ExchangeId = 0

	stats.LastUpdateTime = time.Now().Unix()

	// 同时计算策略层级统计
	if strategyStats, exists := s.strategyStatsMap[stats.StrategyId]; exists {
		strategyStats.Cost += stats.Cost
		strategyStats.Impressions += stats.Impressions
		strategyStats.Clicks += stats.Clicks
		stats.StrategyStats = strategyStats
	} else {
		strategyStats := &Stats{
			Cost:        stats.Cost,
			Impressions: stats.Impressions,
			Clicks:      stats.Clicks,
		}
		s.strategyStatsMap[stats.StrategyId] = strategyStats
		stats.StrategyStats = strategyStats
	}

	// 同时计算活动层级统计
	if campaignStats, exists := s.campaignStatsMap[stats.CampaignId]; exists {
		campaignStats.Cost += stats.Cost
		campaignStats.Impressions += stats.Impressions
		campaignStats.Clicks += stats.Clicks
		stats.CampaignStats = campaignStats
	} else {
		campaignStats := &Stats{
			Cost:        stats.Cost,
			Impressions: stats.Impressions,
			Clicks:      stats.Clicks,
		}
		s.campaignStatsMap[stats.CampaignId] = campaignStats
		stats.CampaignStats = campaignStats
	}

	return stats, nil
}

// 获取统计信息
func (s *StatsProcessor) GetStats(creativeId int32) *BudgetStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if stats, exists := s.statsMap[creativeId]; exists {
		return stats
	}
	return nil
}

// 获取策略层级统计信息
func (s *StatsProcessor) GetStrategyStats(strategyId int32) *Stats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if stats, exists := s.strategyStatsMap[strategyId]; exists {
		return stats
	}
	return nil
}

// 获取活动层级统计信息
func (s *StatsProcessor) GetCampaignStats(campaignId int32) *Stats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if stats, exists := s.campaignStatsMap[campaignId]; exists {
		return stats
	}
	return nil
}

// 更新统计信息
func (s *StatsProcessor) UpdateStats(creativeId int32, cost float64, impressions int64, clicks int64) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if stats, exists := s.statsMap[creativeId]; exists {
		stats.Cost += cost
		stats.Impressions += impressions
		stats.Clicks += clicks
		stats.LastUpdateTime = time.Now().Unix()

		// 同时更新策略层级统计
		if strategyStats, exists := s.strategyStatsMap[stats.StrategyId]; exists {
			strategyStats.Cost += cost
			strategyStats.Impressions += impressions
			strategyStats.Clicks += clicks
		}

		// 同时更新活动层级统计
		if campaignStats, exists := s.campaignStatsMap[stats.CampaignId]; exists {
			campaignStats.Cost += cost
			campaignStats.Impressions += impressions
			campaignStats.Clicks += clicks
		}
	} else {
		// 如果不存在，创建新的统计记录
		s.statsMap[creativeId] = &BudgetStats{
			CreativeId:     creativeId,
			Cost:           cost,
			Impressions:    impressions,
			Clicks:         clicks,
			LastUpdateTime: time.Now().Unix(),
		}
	}
}

// 获取所有统计信息
func (s *StatsProcessor) GetAllStats() map[int32]*BudgetStats {
	s.mu.RLock()
	defer s.mu.RUnlock()

	// 返回副本以避免并发问题
	result := make(map[int32]*BudgetStats)
	for k, v := range s.statsMap {
		result[k] = &BudgetStats{
			CreativeId:     v.CreativeId,
			StrategyId:     v.StrategyId,
			CampaignId:     v.CampaignId,
			ExchangeId:     v.ExchangeId,
			Cost:           v.Cost,
			Impressions:    v.Impressions,
			Clicks:         v.Clicks,
			LastUpdateTime: v.LastUpdateTime,
		}
	}
	return result
}

// 获取统计信息摘要
func (s *StatsProcessor) GetStatsSummary() map[string]interface{} {
	s.mu.RLock()
	defer s.mu.RUnlock()

	summary := make(map[string]interface{})
	summary["total_creatives"] = len(s.statsMap)
	summary["last_load_time"] = s.lastLoadTime.Format("2006-01-02 15:04:05")

	// 统计各交易所的创意数量
	exchangeStats := make(map[int32]int)
	for _, stats := range s.statsMap {
		exchangeStats[stats.ExchangeId]++
	}
	summary["exchange_stats"] = exchangeStats

	return summary
}

// 初始化文件监听
func (s *StatsProcessor) initFileWatching() error {
	// 初始化文件名匹配模式
	fileNamePattern := conf.GlobalConfig.BudgetConfig.StatsFileNamePattern
	if fileNamePattern != "" {
		pattern := fmt.Sprintf(`^%s\.\d{14}$`, regexp.QuoteMeta(fileNamePattern))
		regex, err := regexp.Compile(pattern)
		if err != nil {
			return fmt.Errorf("failed to compile regex pattern: %v", err)
		}
		s.fileNamePattern = regex
	}

	// 创建文件系统监听器
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return fmt.Errorf("failed to create file watcher: %v", err)
	}
	s.watcher = watcher

	// 添加监听目录
	statsDir := conf.GlobalConfig.BudgetConfig.StatsFileDir
	if statsDir != "" {
		err = s.watcher.Add(statsDir)
		if err != nil {
			return fmt.Errorf("failed to add directory to watcher: %v", err)
		}
	} else {
		// 如果没有配置目录，监听固定文件的目录
		statsFilePath := conf.GlobalConfig.BudgetConfig.StatsFilePath
		if statsFilePath != "" {
			statsDir = filepath.Dir(statsFilePath)
			err = s.watcher.Add(statsDir)
			if err != nil {
				return fmt.Errorf("failed to add directory to watcher: %v", err)
			}
		}
	}

	// 启动文件监听协程
	go s.startWatching()

	if zaplog.Logger != nil {
		zaplog.Logger.Info("file watching initialized",
			zap.String("watch_dir", statsDir),
			zap.String("file_pattern", fileNamePattern))
	}

	return nil
}

// 开始文件监听
func (s *StatsProcessor) startWatching() {
	for {
		select {
		case event, ok := <-s.watcher.Events:
			if !ok {
				return
			}
			// 只处理创建和写入事件
			if event.Op&fsnotify.Create == fsnotify.Create || event.Op&fsnotify.Write == fsnotify.Write {
				// 检查文件名是否匹配模式
				fileName := filepath.Base(event.Name)
				if s.isStatsFile(fileName) || event.Name == conf.GlobalConfig.BudgetConfig.StatsFilePath {
					zaplog.Logger.Info("detected file change",
						zap.String("file", event.Name),
						zap.String("operation", event.Op.String()))
					
					// 延迟一小段时间确保文件写入完成
					time.Sleep(100 * time.Millisecond)
					
					if err := s.checkFileChanges(); err != nil {
						zaplog.Logger.Error("failed to check file changes", zap.Error(err))
					}
				}
			}
		case err, ok := <-s.watcher.Errors:
			if !ok {
				return
			}
			zaplog.Logger.Error("file watcher error", zap.Error(err))
		case <-s.stopChan:
			zaplog.Logger.Info("file watching stopped")
			return
		}
	}
}

// 检查文件变化
func (s *StatsProcessor) checkFileChanges() error {
	// 获取最新的统计文件
	latestFile, err := s.getLatestStatsFile()
	if err != nil {
		return fmt.Errorf("failed to get latest stats file: %v", err)
	}

	if latestFile == "" {
		// 如果没有找到匹配的文件，尝试使用配置的固定路径
		latestFile = conf.GlobalConfig.BudgetConfig.StatsFilePath
	}

	// 检查文件是否存在
	fileInfo, err := os.Stat(latestFile)
	if err != nil {
		if os.IsNotExist(err) {
			// 文件不存在，跳过检查
			zaplog.Logger.Debug("stats file not found, skipping", zap.String("file", latestFile))
			return nil
		}
		return fmt.Errorf("failed to stat file %s: %v", latestFile, err)
	}

	// 检查是否有新的文件或文件内容有变化
	modTime := fileInfo.ModTime()
	isNewFile := latestFile != s.currentStatsFile
	isModified := modTime.After(s.lastModTime)

	if isNewFile || isModified {
		if isNewFile {
			zaplog.Logger.Info("found new stats file, loading",
				zap.String("new_file", latestFile),
				zap.String("current_file", s.currentStatsFile),
				zap.Time("file_mod_time", modTime))
		} else {
			zaplog.Logger.Info("stats file modified, reloading",
				zap.String("file", latestFile),
				zap.Time("mod_time", modTime),
				zap.Time("last_mod_time", s.lastModTime))
		}

		// 更新最后修改时间
		s.lastModTime = modTime

		// 重新加载统计数据
		if err := s.LoadStats(); err != nil {
			return fmt.Errorf("failed to reload stats: %v", err)
		}

		zaplog.Logger.Info("stats reloaded successfully (file watching)",
			zap.String("loaded_file", s.currentStatsFile),
			zap.Bool("was_new_file", isNewFile))
	} else {
		zaplog.Logger.Debug("no file changes detected",
			zap.String("file", latestFile),
			zap.Time("mod_time", modTime),
			zap.Time("last_mod_time", s.lastModTime))
	}

	return nil
}

// 停止文件监控
func (s *StatsProcessor) StopFileWatching() {
	if s.watcher != nil {
		s.watcher.Close()
	}
	close(s.stopChan)
	zaplog.Logger.Info("file watching stopped")
}

// 获取最新的统计文件
func (s *StatsProcessor) getLatestStatsFile() (string, error) {
	statsDir := conf.GlobalConfig.BudgetConfig.StatsFileDir
	fileNamePattern := conf.GlobalConfig.BudgetConfig.StatsFileNamePattern

	// 如果没有配置目录和模式，使用原有逻辑
	if statsDir == "" || fileNamePattern == "" {
		return conf.GlobalConfig.BudgetConfig.StatsFilePath, nil
	}

	// 读取目录中的所有文件
	files, err := os.ReadDir(statsDir)
	if err != nil {
		return "", fmt.Errorf("failed to read stats directory: %v", err)
	}

	// 构建文件名匹配正则表达式
	// 文件名模式：fileNamePattern.yyyymmddhhiiss
	pattern := fmt.Sprintf(`^%s\.\d{14}$`, regexp.QuoteMeta(fileNamePattern))
	regex, err := regexp.Compile(pattern)
	if err != nil {
		return "", fmt.Errorf("failed to compile regex pattern: %v", err)
	}

	zaplog.Logger.Debug("searching for stats files",
		zap.String("stats_dir", statsDir),
		zap.String("file_pattern", fileNamePattern),
		zap.String("regex_pattern", pattern),
		zap.Int("total_files", len(files)))

	var latestFile string
	var latestTime time.Time

	for _, file := range files {
		fileName := file.Name()

		if !regex.MatchString(fileName) {
			zaplog.Logger.Debug("skipping file",
				zap.String("filename", fileName),
				zap.Bool("matches_regex", regex.MatchString(fileName)))
			continue
		}

		// 提取时间戳部分 (yyyymmddhhiiss)
		timestampStr := fileName[len(fileNamePattern)+1:] // +1 for the dot
		fileTime, err := time.Parse("20060102150405", timestampStr)
		if err != nil {
			zaplog.Logger.Warn("failed to parse timestamp from filename",
				zap.String("filename", fileName),
				zap.String("timestamp", timestampStr),
				zap.Error(err))
			continue
		}

		if latestFile == "" || fileTime.After(latestTime) {
			latestFile = fileName
			latestTime = fileTime
			zaplog.Logger.Debug("found newer stats file",
				zap.String("filename", fileName),
				zap.Time("file_time", fileTime))
		}
	}

	if latestFile == "" {
		if zaplog.Logger != nil {
			zaplog.Logger.Warn("no matching stats file found",
				zap.String("stats_dir", statsDir),
				zap.String("file_pattern", fileNamePattern))
		}
		return "", nil
	}

	finalPath := filepath.Join(statsDir, latestFile)
	if zaplog.Logger != nil {
		zaplog.Logger.Info("found latest stats file",
			zap.String("file_path", finalPath),
			zap.Time("file_time", latestTime))
	}
	return finalPath, nil
}

// 检查文件名是否符合统计文件格式
func (s *StatsProcessor) isStatsFile(fileName string) bool {
	// 使用正则匹配检查文件名格式：fileNamePattern.yyyymmddhhiiss
	if s.fileNamePattern != nil {
		return s.fileNamePattern.MatchString(fileName)
	}
	return false
}
