package predict

import (
	"rtb_model_server/common/domob_thrift/predict_model_server"
	"rtb_model_server/conf"
	ctx "rtb_model_server/internal/context"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"go.uber.org/zap"
)

type PredictProcessor struct {
	connPool    *ConnectionPool
	monitorStop chan struct{}
	monitorWg   sync.WaitGroup
}

func NewPredictProcessor() *PredictProcessor {
	config := conf.GlobalConfig.PredictServer
	connTimeout := time.Duration(config.ConnTimeout) * time.Millisecond

	connPool := NewConnectionPool(
		config.Addr,
		config.PoolSize,
		config.MaxIdleConns,
		connTimeout,
	)

	p := &PredictProcessor{
		connPool:    connPool,
		monitorStop: make(chan struct{}),
	}

	// 启动连接池监控协程
	p.startPoolMonitor()

	return p
}

// Close 关闭连接池
func (p *PredictProcessor) Close() {
	// 停止监控协程
	close(p.monitorStop)
	p.monitorWg.Wait()

	// 关闭连接池
	p.connPool.Close()
}

// GetPoolStats 获取连接池统计信息
func (p *PredictProcessor) GetPoolStats() (total, inUse, idle int) {
	return p.connPool.GetPoolStats()
}

// startPoolMonitor 启动连接池监控协程
func (p *PredictProcessor) startPoolMonitor() {
	p.monitorWg.Add(1)
	go func() {
		defer p.monitorWg.Done()

		// 监控间隔，可以根据需要调整
		monitorInterval := 30 * time.Second
		ticker := time.NewTicker(monitorInterval)
		defer ticker.Stop()

		zaplog.Logger.Info("Connection pool monitor started")

		for {
			select {
			case <-p.monitorStop:
				zaplog.Logger.Info("Connection pool monitor stopped")
				return
			case <-ticker.C:
				// 获取连接池统计信息
				total, inUse, idle := p.GetPoolStats()
				zaplog.Logger.Info("Connection Pool Stats", zap.Int("total", total), zap.Int("inUse", inUse), zap.Int("idle", idle))

				// 可以在这里添加更多监控逻辑，比如:
				// - 检查连接池使用率是否过高
				// - 记录监控指标到监控系统
				// - 在连接池耗尽时发出告警
				if total > 0 {
					usageRate := float64(inUse) / float64(total) * 100
					if usageRate > 80 {
						zaplog.Logger.Warn("WARNING: Connection pool usage rate is high", zap.Float64("usageRate", usageRate))
					}
				}
			}
		}
	}()
}

type PredictResult struct {
	CreativeId int32
	PreCtr     int64
	PreCvr     int64
	PreDeepCvr int64
}

// 调用预估服务，返回预估结果
func (p *PredictProcessor) Predict(requestCtx *ctx.RequestContext, creativeIds []int32) (map[int32]PredictResult, error) {
	// 从连接池获取连接
	conn, err := p.connPool.GetConnection()
	if err != nil {
		return nil, err
	}
	defer p.connPool.ReleaseConnection(conn)

	req := p.buildRequestHeader(requestCtx, creativeIds)
	resp, err := conn.client.GetPredictValue(req)
	if err != nil {
		return nil, err
	}
	results := p.buildResponse(resp)
	return results, nil
}
func (p *PredictProcessor) buildResponse(predictRsp *predict_model_server.PredictModelServerResponse) map[int32]PredictResult {
	results := make(map[int32]PredictResult)
	for _, ad := range predictRsp.ResponseList {

		results[int32(ad.Cid)] = PredictResult{
			CreativeId: int32(ad.Cid),
			PreCtr:     ad.Ctr,
			PreCvr:     ad.Cvr,
			PreDeepCvr: ad.DeepCvr,
		}
	}
	return results
}

func (p *PredictProcessor) buildRequestHeader(requestCtx *ctx.RequestContext, creativeIds []int32) *predict_model_server.PredictModelServerRequest {
	bidReq := requestCtx.BidRequest
	now := time.Now()
	var absolutePos int32
	if len(bidReq.BidList) > 0 && bidReq.BidList[0].Request != nil {
		absolutePos = bidReq.BidList[0].Request.AbsolutePos
	}

	var deviceIdMd5 string
	if bidReq.Device.DmPlatform == 1 {
		deviceIdMd5 = bidReq.Device.Oaidmd5
	} else {
		deviceIdMd5 = bidReq.Device.Idfamd5
	}

	req := &predict_model_server.PredictModelServerRequest{
		ReqId:         bidReq.ReqId,
		ReqTs:         int32(now.Unix()),
		SearchId:      bidReq.SearchId,
		DeviceIdMd5:   deviceIdMd5,
		DmPlatform:    bidReq.Device.DmPlatform,
		ExchangeId:    bidReq.ExchangeId,
		AdxExchangeId: bidReq.AdxExchangeId,
		DmGeoId:       int32(bidReq.Device.DmGeoId),
		DmOsId:        int32(bidReq.Device.DmOsId),
		DmMediaId:     bidReq.App.DmMediaId,
		AppBundle:     bidReq.App.AppBundle,
		AbsolutePos:   absolutePos,
		DmAccesstypeId: bidReq.Device.DmAccesstypeId,
		
	}
	req.AdList = p.buildAdList(requestCtx, creativeIds)
	return req
}

// 这里注意下，如果需要预估的创意太多，会导致预估超时，
func (p *PredictProcessor) buildAdList(requestCtx *ctx.RequestContext, creativeIds []int32) []*predict_model_server.PredictAdInfo {
	var index int32
	result := make([]*predict_model_server.PredictAdInfo, 0)
	for _, cid := range creativeIds {
		creative, err := requestCtx.AdIndex.GetCreative(cid)
		if err != nil {
			continue
		}
		tracking, err := requestCtx.AdIndex.GetAdTracking(creative.AdTrackingIds[0])
		if err != nil {
			continue
		}
		strategy, err := requestCtx.AdIndex.GetStrategy(creative.StrategyId)
		if err != nil {
			continue
		}
		ad := &predict_model_server.PredictAdInfo{
			AdIndex:    index,
			CreativeId: cid,
			StrategyId: creative.StrategyId,
			CampaignId: creative.CampaignId,
			SponsorId:  creative.SponsorId,
			ProductId:  tracking.ProductId,
			AdCostType:   strategy.MediaCostType,
			ChnId:        tracking.ChnId,
			AppId:        tracking.AppId,
			FinPriceSource: 0, // 获取不到，后续删除
			AdPosId:       creative.Container.AdMatchType,

		}
		result = append(result, ad)
		index++

	}
	return result
}
