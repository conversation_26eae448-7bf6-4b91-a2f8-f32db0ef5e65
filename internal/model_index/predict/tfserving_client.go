package predict

import (
	"context"
	"fmt"
	"rtb_model_server/internal/zaplog"
	"rtb_model_server/proto/tensorflow_serving"
	"time"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/connectivity"
	"google.golang.org/grpc/credentials/insecure"
)

// BatchPredictData 批量预测数据结构
type BatchPredictData struct {
	BatchSize      int                `json:"batch_size"`
	CreativeIDs    []int32            `json:"creative_ids"`    // 创意ID列表，保持顺序对齐
	RawFeatures    map[string][]int64 `json:"raw_features"`    // 每个特征是数组，支持批量
	HashedFeatures [][]uint64         `json:"hashed_features"` // 每个样本的hash特征列表（如果需要）
}

// HashFeatureNames 45个需要hash的特征名称（按slot_id顺序）
var HashFeatureNames = []string{
	"dsp_id", "exchange_id", "week_day", "hour", "inventory_type",
	"adp_id", "client_ip", "adp_dim", "bidfloor", "sponsor_id",
	"campaign_id", "strategy_id", "creative_id", "dsp_advertiser_id", "dsp_creative_id",
	"creative_type", "media_bid_type", "dm_platform", "template_id", "app_bundle",
	"ad_source", "province", "city", "standard_make", "dev_make",
	"dev_model", "exp_id", "dm_media_id", "absolute_pos", "dsp_ad_slot",
	"api_version", "tanx_task_id", "tanx_group_id", "tanx_ad_id", "client_ipv6",
	"country", "domob_bidfloor", "cat_id", "bid_id", "schain",
	"surge_score", "dsp_cost_mod", "budget_type_v1", "app_name", "app_package_name",
}

// TFServingClient TensorFlow Serving gRPC 客户端
type TFServingClient struct {
	conn       *grpc.ClientConn
	client     tensorflow_serving.PredictionServiceClient
	serverAddr string
	modelName  string
}

// NewTFServingClient 创建 TensorFlow Serving gRPC 客户端
func NewTFServingClient(serverAddr, modelName string) (*TFServingClient, error) {
	zaplog.Logger.Info("Creating TensorFlow Serving gRPC client",
		zap.String("server", serverAddr),
		zap.String("model", modelName))

	// 创建 gRPC 连接
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	conn, err := grpc.DialContext(ctx, serverAddr,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithBlock(),
	)
	if err != nil {
		zaplog.Logger.Error("Failed to connect to TensorFlow Serving",
			zap.String("server", serverAddr),
			zap.Error(err))
		return nil, fmt.Errorf("grpc connection failed: %w", err)
	}

	// 创建 gRPC 客户端
	client := tensorflow_serving.NewPredictionServiceClient(conn)

	tfClient := &TFServingClient{
		conn:       conn,
		client:     client,
		serverAddr: serverAddr,
		modelName:  modelName,
	}

	// 测试连接
	if err := tfClient.TestConnection(); err != nil {
		conn.Close()
		zaplog.Logger.Error("Failed to test TensorFlow Serving connection",
			zap.String("server", serverAddr),
			zap.Error(err))
		return nil, fmt.Errorf("connection test failed: %w", err)
	}

	zaplog.Logger.Info("TensorFlow Serving gRPC client created successfully",
		zap.String("server", serverAddr))

	return tfClient, nil
}

// Close 关闭连接
func (c *TFServingClient) Close() {
	if c.conn != nil {
		c.conn.Close()
		zaplog.Logger.Info("TensorFlow Serving gRPC connection closed")
	}
}

// TestConnection 测试连接状态
func (c *TFServingClient) TestConnection() error {
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	state := c.conn.GetState()
	if state != connectivity.Ready && state != connectivity.Idle {
		return fmt.Errorf("connection not ready, state: %v", state)
	}

	return nil
}

// Predict 执行批量预测 - 严格按照 TensorFlow Serving 官网 gRPC 请求结构
func (c *TFServingClient) Predict(ctx context.Context, batchFeatures []map[string]interface{}) (map[string]interface{}, error) {
	// 检查连接状态
	if err := c.TestConnection(); err != nil {
		return nil, fmt.Errorf("connection check failed: %w", err)
	}

	if len(batchFeatures) == 0 {
		return nil, fmt.Errorf("empty batch features")
	}

	batchSize := len(batchFeatures)
	zaplog.Logger.Debug("Preparing batch prediction",
		zap.String("model", c.modelName),
		zap.Int("batch_size", batchSize))

	// 构建严格符合 TensorFlow Serving 官网标准的 gRPC 请求
	request := &tensorflow_serving.PredictRequest{
		ModelSpec: &tensorflow_serving.ModelSpec{
			Name:          c.modelName,
			SignatureName: "serving_default",
		},
		Inputs: make(map[string]*tensorflow_serving.TensorProto),
	}

	// 为每个特征创建独立的张量 - 46个特征
	// 收集所有特征名称
	allFeatureNames := make(map[string]bool)
	for _, features := range batchFeatures {
		for featureName := range features {
			allFeatureNames[featureName] = true
		}
	}

	// 为每个特征创建张量 - shape=(batch_size,)
	for featureName := range allFeatureNames {
		batchValues := make([]int64, batchSize)

		// 收集该特征在所有样本中的值，保持顺序对齐
		for i, features := range batchFeatures {
			if value, exists := features[featureName]; exists {
				if intVal, ok := c.convertToInt64(value); ok {
					batchValues[i] = intVal
				} else {
					batchValues[i] = 0
				}
			} else {
				batchValues[i] = 0 // 默认值
			}
		}

		// 创建张量 - shape=(batch_size,)
		tensor := &tensorflow_serving.TensorProto{
			Dtype: tensorflow_serving.DataType_DT_INT64,
			TensorShape: &tensorflow_serving.TensorShapeProto{
				Dim: []*tensorflow_serving.TensorShapeProto_Dim{
					{Size: int64(batchSize)},
				},
			},
			Int64Val: batchValues,
		}

		request.Inputs[featureName] = tensor
	}

	// 验证输入数量：应该有YAML配置中定义的特征数量 + dsp_bid
	expectedFeatureCount := len(allFeatureNames)
	zaplog.Logger.Debug("Feature count validation",
		zap.Int("actual_features", expectedFeatureCount),
		zap.Int("input_tensors", len(request.Inputs)))

	zaplog.Logger.Debug("Sending gRPC batch prediction request",
		zap.String("model", c.modelName),
		zap.Int("batch_size", batchSize),
		zap.Int("input_count", len(request.Inputs)),
		zap.Bool("hash_features_enabled", true))

	// 发送 gRPC 请求
	response, err := c.client.Predict(ctx, request)
	if err != nil {
		zaplog.Logger.Error("gRPC batch prediction request failed",
			zap.String("model", c.modelName),
			zap.Error(err))
		return nil, fmt.Errorf("prediction failed: %w", err)
	}

	zaplog.Logger.Debug("gRPC batch prediction request completed",
		zap.String("model", c.modelName),
		zap.Int("output_count", len(response.Outputs)))

	// 转换响应
	result := make(map[string]interface{})
	for name, tensor := range response.Outputs {
		value, err := c.convertFromTensor(tensor)
		if err != nil {
			zaplog.Logger.Error("Failed to convert tensor to value",
				zap.String("output", name),
				zap.Error(err))
			continue
		}
		result[name] = value
	}

	return result, nil
}

// convertToInt64 将任意类型转换为 int64
func (c *TFServingClient) convertToInt64(value interface{}) (int64, bool) {
	switch v := value.(type) {
	case int64:
		return v, true
	case int32:
		return int64(v), true
	case int:
		return int64(v), true
	case float64:
		return int64(v), true
	case float32:
		return int64(v), true
	default:
		return 0, false
	}
}

// getDefaultInt64Value 获取特征的默认 int64 值
func (c *TFServingClient) getDefaultInt64Value(featureName string) int64 {
	// 基于模型期望的46个特征的默认值
	predefinedFeatures := map[string]int64{
		// 45个预定义特征（按字母顺序，与模型完全匹配）
		"absolute_pos": 0, "ad_source": 0, "adp_dim": 0, "adp_id": 0, "api_version": 0,
		"app_bundle": 0, "app_name": 0, "app_package_name": 0, "bid_id": 0, "bidfloor": 0,
		"budget_type_v1": 0, "campaign_id": 0, "cat_id": 0, "city": 0, "client_ip": 0,
		"client_ipv6": 0, "country": 0, "creative_id": 0, "creative_type": 0, "dev_make": 0,
		"dev_model": 0, "dm_media_id": 0, "dm_platform": 0, "domob_bidfloor": 0, "dsp_ad_slot": 0,
		"dsp_advertiser_id": 0, "dsp_cost_mod": 0, "dsp_creative_id": 0, "dsp_id": 0,
		"exchange_id": 0, "exp_id": 0, "hour": 0, "inventory_type": 0, "media_bid_type": 0,
		"province": 0, "schain": 0, "sponsor_id": 0, "standard_make": 0, "strategy_id": 0,
		"surge_score": 0, "tanx_ad_id": 0, "tanx_group_id": 0, "tanx_task_id": 0, "template_id": 0, "week_day": 0,
		// dsp_bid 特征
		"dsp_bid": 0,
	}

	if defaultVal, exists := predefinedFeatures[featureName]; exists {
		return defaultVal
	}

	return int64(0) // 默认返回 0
}

// getDefaultValueForFeature 获取特征的默认值
func (c *TFServingClient) getDefaultValueForFeature(featureName string) interface{} {
	if featureName == "hashed_features" {
		return make([]int64, 45) // 序列特征：45个0值
	}
	return int64(0) // 单特征：默认值0
}

// createTensorFromBatch 从批量值创建张量 - 简化版本
func (c *TFServingClient) createTensorFromBatch(featureName string, batchValues []interface{}) (*tensorflow_serving.TensorProto, error) {
	if len(batchValues) == 0 {
		return nil, fmt.Errorf("empty batch values for feature %s", featureName)
	}

	batchSize := int64(len(batchValues))

	// 检查第一个非空值来确定数据类型
	var sampleValue interface{}
	for _, value := range batchValues {
		if value != nil {
			sampleValue = value
			break
		}
	}

	if sampleValue == nil {
		return nil, fmt.Errorf("all values are nil for feature %s", featureName)
	}

	switch sampleValue.(type) {
	case []int64:
		// 序列特征：shape=(batch_size, 序列长度)
		return c.createSequenceTensor(batchValues, batchSize, true)
	case int64, int32, int:
		// 单特征：shape=(batch_size,)
		return c.createSingleTensor(batchValues, batchSize, true)
	case []float32:
		// 序列特征：shape=(batch_size, 序列长度)
		return c.createSequenceTensor(batchValues, batchSize, false)
	case float32, float64:
		// 单特征：shape=(batch_size,)
		return c.createSingleTensor(batchValues, batchSize, false)
	default:
		return nil, fmt.Errorf("unsupported batch type for feature %s: %T", featureName, sampleValue)
	}
}

// createSingleTensor 创建单特征张量
func (c *TFServingClient) createSingleTensor(batchValues []interface{}, batchSize int64, isInt bool) (*tensorflow_serving.TensorProto, error) {
	tensor := &tensorflow_serving.TensorProto{
		TensorShape: &tensorflow_serving.TensorShapeProto{
			Dim: []*tensorflow_serving.TensorShapeProto_Dim{{Size: batchSize}},
		},
	}

	if isInt {
		tensor.Dtype = tensorflow_serving.DataType_DT_INT64
		values := make([]int64, batchSize)
		for i, value := range batchValues {
			if intVal, ok := c.convertToInt64(value); ok {
				values[i] = intVal
			}
		}
		tensor.Int64Val = values
	} else {
		tensor.Dtype = tensorflow_serving.DataType_DT_FLOAT
		values := make([]float32, batchSize)
		for i, value := range batchValues {
			switch v := value.(type) {
			case float32:
				values[i] = v
			case float64:
				values[i] = float32(v)
			}
		}
		tensor.FloatVal = values
	}

	return tensor, nil
}

// createSequenceTensor 创建序列特征张量
func (c *TFServingClient) createSequenceTensor(batchValues []interface{}, batchSize int64, isInt bool) (*tensorflow_serving.TensorProto, error) {
	// 找到最大序列长度
	maxSeqLen := int64(0)
	for _, value := range batchValues {
		if isInt {
			if seq, ok := value.([]int64); ok && int64(len(seq)) > maxSeqLen {
				maxSeqLen = int64(len(seq))
			}
		} else {
			if seq, ok := value.([]float32); ok && int64(len(seq)) > maxSeqLen {
				maxSeqLen = int64(len(seq))
			}
		}
	}

	tensor := &tensorflow_serving.TensorProto{
		TensorShape: &tensorflow_serving.TensorShapeProto{
			Dim: []*tensorflow_serving.TensorShapeProto_Dim{
				{Size: batchSize},
				{Size: maxSeqLen},
			},
		},
	}

	if isInt {
		tensor.Dtype = tensorflow_serving.DataType_DT_INT64
		allValues := make([]int64, 0, batchSize*maxSeqLen)
		for _, value := range batchValues {
			if seq, ok := value.([]int64); ok {
				allValues = append(allValues, seq...)
				// 填充到最大长度
				for i := len(seq); i < int(maxSeqLen); i++ {
					allValues = append(allValues, 0)
				}
			} else {
				// 填充默认值
				for i := int64(0); i < maxSeqLen; i++ {
					allValues = append(allValues, 0)
				}
			}
		}
		tensor.Int64Val = allValues
	} else {
		tensor.Dtype = tensorflow_serving.DataType_DT_FLOAT
		allValues := make([]float32, 0, batchSize*maxSeqLen)
		for _, value := range batchValues {
			if seq, ok := value.([]float32); ok {
				allValues = append(allValues, seq...)
				for i := len(seq); i < int(maxSeqLen); i++ {
					allValues = append(allValues, 0.0)
				}
			} else {
				for i := int64(0); i < maxSeqLen; i++ {
					allValues = append(allValues, 0.0)
				}
			}
		}
		tensor.FloatVal = allValues
	}

	return tensor, nil
}

// convertFromTensor 将张量转换为值
func (c *TFServingClient) convertFromTensor(tensor *tensorflow_serving.TensorProto) (interface{}, error) {
	switch tensor.Dtype {
	case tensorflow_serving.DataType_DT_INT64:
		return tensor.Int64Val, nil
	case tensorflow_serving.DataType_DT_FLOAT:
		return tensor.FloatVal, nil
	case tensorflow_serving.DataType_DT_DOUBLE:
		return tensor.DoubleVal, nil
	default:
		return nil, fmt.Errorf("unsupported tensor type: %v", tensor.Dtype)
	}
}
