package predict

import (
	"context"
	"errors"
	"rtb_model_server/internal/zaplog"
	"sync"
	"time"

	"go.uber.org/zap"
)

// GRPCConnection 表示一个gRPC连接
type GRPCConnection struct {
	client   *TFServingClient
	inUse    bool
	lastUsed time.Time
}

// GRPCConnectionPool gRPC连接池管理器
type GRPCConnectionPool struct {
	connPool     []*GRPCConnection
	poolSize     int
	maxIdleConns int
	connTimeout  time.Duration
	serverAddr   string
	modelName    string
	mu           sync.Mutex
}

// NewGRPCConnectionPool 创建新的gRPC连接池
func NewGRPCConnectionPool(serverAddr, modelName string, poolSize, maxIdleConns int, connTimeout time.Duration) *GRPCConnectionPool {
	p := &GRPCConnectionPool{
		poolSize:     poolSize,
		maxIdleConns: maxIdleConns,
		connTimeout:  connTimeout,
		serverAddr:   serverAddr,
		modelName:    modelName,
		connPool:     make([]*GRPCConnection, 0, poolSize),
	}

	// 初始化连接池
	var errorCount int
	var lastError error
	for i := 0; i < maxIdleConns; i++ {
		conn, err := p.createConnection()
		if err != nil {
			lastError = err
			errorCount++
			zaplog.Logger.Error("failed to create initial gRPC connection", zap.Int("index", i), zap.Error(err))
			continue
		}
		p.connPool = append(p.connPool, conn)
	}
	if errorCount > 0 {
		zaplog.Logger.Warn("init gRPC connection pool with errors", 
			zap.Int("error_count", errorCount), 
			zap.Error(lastError))
	}

	zaplog.Logger.Info("gRPC connection pool created", 
		zap.String("server", serverAddr),
		zap.String("model", modelName),
		zap.Int("pool_size", poolSize),
		zap.Int("initial_connections", len(p.connPool)))

	return p
}

// createConnection 创建新的gRPC连接
func (p *GRPCConnectionPool) createConnection() (*GRPCConnection, error) {
	client, err := NewTFServingClient(p.serverAddr, p.modelName)
	if err != nil {
		return nil, err
	}

	return &GRPCConnection{
		client:   client,
		inUse:    false,
		lastUsed: time.Now(),
	}, nil
}

// GetConnection 从连接池获取连接
func (p *GRPCConnectionPool) GetConnection() (*GRPCConnection, error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 查找可用的空闲连接
	for _, conn := range p.connPool {
		if !conn.inUse {
			// 检查连接是否还有效
			if err := conn.client.TestConnection(); err == nil {
				conn.inUse = true
				conn.lastUsed = time.Now()
				return conn, nil
			} else {
				// 连接已断开，重新创建
				conn.client.Close()
				newClient, err := NewTFServingClient(p.serverAddr, p.modelName)
				if err != nil {
					continue
				}
				conn.client = newClient
				conn.inUse = true
				conn.lastUsed = time.Now()
				return conn, nil
			}
		}
	}

	// 如果没有空闲连接且池未满，创建新连接
	if len(p.connPool) < p.poolSize {
		conn, err := p.createConnection()
		if err != nil {
			return nil, err
		}
		conn.inUse = true
		p.connPool = append(p.connPool, conn)
		return conn, nil
	}

	return nil, errors.New("gRPC connection pool exhausted")
}

// ReleaseConnection 释放连接回连接池
func (p *GRPCConnectionPool) ReleaseConnection(conn *GRPCConnection) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if conn != nil {
		conn.inUse = false
		conn.lastUsed = time.Now()
	}
}

// Close 关闭连接池
func (p *GRPCConnectionPool) Close() {
	p.mu.Lock()
	defer p.mu.Unlock()

	for _, conn := range p.connPool {
		if conn.client != nil {
			conn.client.Close()
		}
	}
	p.connPool = nil
	zaplog.Logger.Info("gRPC connection pool closed")
}

// GetPoolStats 获取连接池统计信息
func (p *GRPCConnectionPool) GetPoolStats() (total, inUse, idle int) {
	p.mu.Lock()
	defer p.mu.Unlock()

	total = len(p.connPool)
	for _, conn := range p.connPool {
		if conn.inUse {
			inUse++
		} else {
			idle++
		}
	}
	return total, inUse, idle
}

// CleanupIdleConnections 清理空闲连接
func (p *GRPCConnectionPool) CleanupIdleConnections(maxIdleTime time.Duration) {
	p.mu.Lock()
	defer p.mu.Unlock()

	now := time.Now()
	var activeConns []*GRPCConnection

	for _, conn := range p.connPool {
		if conn.inUse || now.Sub(conn.lastUsed) < maxIdleTime {
			activeConns = append(activeConns, conn)
		} else {
			// 关闭空闲时间过长的连接
			conn.client.Close()
			zaplog.Logger.Debug("closed idle gRPC connection", 
				zap.Duration("idle_time", now.Sub(conn.lastUsed)))
		}
	}

	p.connPool = activeConns
}

// Predict 使用连接池执行预测
func (p *GRPCConnectionPool) Predict(ctx context.Context, batchFeatures []map[string]interface{}) (map[string]interface{}, error) {
	conn, err := p.GetConnection()
	if err != nil {
		return nil, err
	}
	defer p.ReleaseConnection(conn)

	return conn.client.Predict(ctx, batchFeatures)
}
