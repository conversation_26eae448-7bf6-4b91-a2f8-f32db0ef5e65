package kafka

import (
	"sync"

	"go.uber.org/zap"

	"rtb_model_server/conf"
	"rtb_model_server/internal/zaplog"
)

var (
	// globalFunnelProducer 全局漏斗数据生产者
	globalFunnelProducer *FunnelProducer
	// initOnce 确保只初始化一次
	initOnce sync.Once
	// initError 初始化错误
	initError error
)

// InitKafkaProducers 初始化Kafka生产者
func InitKafkaProducers() error {
	initOnce.Do(func() {
		// 检查是否启用Kafka
		if !conf.GlobalConfig.FunnelKafkaConfig.Enabled {
			zaplog.Logger.Info("Kafka funnel producer is disabled")
			return
		}

		// 创建漏斗数据生产者
		// 将 conf.KafkaConfig 转换为 kafka.KafkaConfig
		kafkaConfig := &KafkaConfig{
			Brokers:          conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.Brokers,
			ClientID:         conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.ClientID,
			RequiredAcks:     conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.RequiredAcks,
			Compression:      conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.Compression,
			FlushMessages:    conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.FlushMessages,
			FlushMaxMessages: conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.FlushMaxMessages,
			FlushFrequency:   conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.FlushFrequency,
			Timeout:          conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.Timeout,
			MaxRetries:       conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.MaxRetries,
			RetryBackoff:     conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.RetryBackoff,
			// SASL认证配置
			EnableSASL:       conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.EnableSASL,
			SASLMechanism:    conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.SASLMechanism,
			SASLUsername:     conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.SASLUsername,
			SASLPassword:     conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.SASLPassword,
			// TLS配置
			EnableTLS:        conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.EnableTLS,
			TLSSkipVerify:    conf.GlobalConfig.FunnelKafkaConfig.KafkaConfig.TLSSkipVerify,
		}
		
		// 获取队列大小配置
		queueSize := conf.GlobalConfig.FunnelKafkaConfig.QueueSize
		zaplog.Logger.Info("Initializing funnel producer", 
			zap.Int("queue_size", queueSize),
			zap.String("topic", conf.GlobalConfig.FunnelKafkaConfig.Topic))
		
		producer, err := NewFunnelProducer(
			kafkaConfig,
			conf.GlobalConfig.FunnelKafkaConfig.Topic,
			queueSize,
		)
		if err != nil {
			initError = err
			zaplog.Logger.Error("Failed to create funnel producer", zap.Error(err))
			return
		}

		globalFunnelProducer = producer
		zaplog.Logger.Info("Kafka funnel producer initialized",
			zap.String("topic", conf.GlobalConfig.FunnelKafkaConfig.Topic))
	})

	return initError
}

// GetFunnelProducer 获取全局漏斗数据生产者
func GetFunnelProducer() *FunnelProducer {
	return globalFunnelProducer
}

// CloseKafkaProducers 关闭所有Kafka生产者
func CloseKafkaProducers() {
	if globalFunnelProducer != nil {
		if err := globalFunnelProducer.Close(); err != nil {
			zaplog.Logger.Error("Failed to close funnel producer", zap.Error(err))
		}
		globalFunnelProducer = nil
	}
}