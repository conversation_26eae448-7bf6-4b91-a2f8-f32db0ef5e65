# Kafka SASL认证配置指南

本文档介绍如何在RTB Model Server中配置Kafka的SASL认证。

## 配置说明

### 基本配置

在 `conf/rtb_model_server.yaml` 文件中，Kafka配置位于 `FunnelKafkaConfig.KafkaConfig` 节点下。

### SASL认证配置项

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `EnableSASL` | bool | false | 是否启用SASL认证 |
| `SASLMechanism` | string | "PLAIN" | SASL认证机制 |
| `SASLUsername` | string | "" | SASL用户名 |
| `SASLPassword` | string | "" | SASL密码 |
| `EnableTLS` | bool | false | 是否启用TLS |
| `TLSSkipVerify` | bool | false | 是否跳过TLS证书验证 |

### 支持的SASL认证机制

- `PLAIN`: 明文传输用户名和密码（需要配合TLS使用）
- `SCRAM-SHA-256`: 使用SHA-256的SCRAM认证
- `SCRAM-SHA-512`: 使用SHA-512的SCRAM认证

## 配置示例

### 1. 使用PLAIN机制（推荐配合TLS）

```yaml
FunnelKafkaConfig:
  Enabled: true
  Topic: "your_topic"
  QueueSize: 10000
  KafkaConfig:
    Brokers:
      - "kafka1.example.com:9093"
      - "kafka2.example.com:9093"
    ClientID: "rtb-model-server"
    # SASL配置
    EnableSASL: true
    SASLMechanism: "PLAIN"
    SASLUsername: "your_username"
    SASLPassword: "your_password"
    # TLS配置
    EnableTLS: true
    TLSSkipVerify: false
```

### 2. 使用SCRAM-SHA-256机制

```yaml
FunnelKafkaConfig:
  Enabled: true
  Topic: "your_topic"
  QueueSize: 10000
  KafkaConfig:
    Brokers:
      - "kafka1.example.com:9092"
      - "kafka2.example.com:9092"
    ClientID: "rtb-model-server"
    # SASL配置
    EnableSASL: true
    SASLMechanism: "SCRAM-SHA-256"
    SASLUsername: "your_username"
    SASLPassword: "your_password"
    # 可选的TLS配置
    EnableTLS: false
    TLSSkipVerify: false
```

### 3. 无认证配置（默认）

```yaml
FunnelKafkaConfig:
  Enabled: true
  Topic: "your_topic"
  QueueSize: 10000
  KafkaConfig:
    Brokers:
      - "kafka1.example.com:9092"
      - "kafka2.example.com:9092"
    ClientID: "rtb-model-server"
    # SASL配置（禁用）
    EnableSASL: false
    # TLS配置（禁用）
    EnableTLS: false
```

## 安全建议

1. **生产环境**: 强烈建议在生产环境中启用TLS (`EnableTLS: true`) 并设置 `TLSSkipVerify: false`
2. **密码管理**: 避免在配置文件中明文存储密码，考虑使用环境变量或密钥管理系统
3. **认证机制**: 推荐使用 `SCRAM-SHA-256` 或 `SCRAM-SHA-512` 而不是 `PLAIN`
4. **网络安全**: 确保Kafka集群的网络访问受到适当的防火墙保护

## 故障排除

### 常见错误

1. **认证失败**
   - 检查用户名和密码是否正确
   - 确认Kafka服务器支持所选的SASL机制
   - 验证用户是否有相应的权限

2. **TLS连接失败**
   - 检查Kafka服务器是否启用了TLS
   - 验证证书是否有效（如果 `TLSSkipVerify: false`）
   - 确认端口号是否正确（TLS通常使用9093端口）

3. **连接超时**
   - 检查网络连接
   - 验证Kafka服务器地址和端口
   - 调整 `Timeout` 配置

### 日志检查

启用SASL认证后，应用程序会在日志中输出相关信息：

```
SASL authentication enabled mechanism=SCRAM-SHA-256 username=your_username
TLS enabled skip_verify=false
```

## 参考资料

- [Apache Kafka SASL文档](https://kafka.apache.org/documentation/#security_sasl)
- [Sarama客户端文档](https://github.com/IBM/sarama)