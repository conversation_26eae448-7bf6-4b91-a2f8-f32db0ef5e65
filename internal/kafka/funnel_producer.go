package kafka

import (
	"encoding/json"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"go.uber.org/zap"

	funnel "rtb_model_server/common/funnel_gengo"
	"rtb_model_server/internal/zaplog"
)

// FunnelProducer 漏斗数据生产者
type FunnelProducer struct {
	producer   Producer
	topic      string
	mu         sync.Mutex
	queueSize  int           // 队列大小限制，0表示不限制
	queueCount int32         // 当前队列中的消息数量
	msgQueue   chan *queueMsg // 消息队列
	closed     bool          // 是否已关闭
	wg         sync.WaitGroup // 等待所有消息处理完成
}

// queueMsg 队列消息
type queueMsg struct {
	topic string
	key   string
	value []byte
}

// NewFunnelProducer 创建一个新的漏斗数据生产者
func NewFunnelProducer(config *KafkaConfig, topic string, queueSize int) (*FunnelProducer, error) {
	if config == nil {
		return nil, fmt.Errorf("kafka config is nil")
	}

	if topic == "" {
		return nil, fmt.Errorf("topic is empty")
	}

	// 创建Kafka生产者
	producer, err := NewKafkaProducer(config)
	if err != nil {
		return nil, fmt.Errorf("failed to create kafka producer: %w", err)
	}

	// 如果队列大小为0，设置一个默认值
	if queueSize <= 0 {
		queueSize = 10000 // 默认队列大小
	}

	fp := &FunnelProducer{
		producer:   producer,
		topic:      topic,
		queueSize:  queueSize,
		queueCount: 0,
		msgQueue:   make(chan *queueMsg, queueSize),
		closed:     false,
	}

	// 启动消息处理协程
	fp.startMessageProcessor()

	return fp, nil
}

// startMessageProcessor 启动消息处理协程
func (p *FunnelProducer) startMessageProcessor() {
	// 启动消息处理协程
	p.wg.Add(1)
	go func() {
		defer p.wg.Done()
		for msg := range p.msgQueue {
			// 发送消息到Kafka
			p.producer.SendMessageAsync(msg.topic, msg.key, msg.value, nil)
			// 减少队列计数
			atomic.AddInt32(&p.queueCount, -1)
		}
	}()

	// 启动队列状态监控协程
	p.wg.Add(1)
	go func() {
		defer p.wg.Done()
		ticker := time.NewTicker(1 * time.Minute)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				p.mu.Lock()
				if p.closed {
					p.mu.Unlock()
					return
				}
				p.mu.Unlock()

				// 获取当前队列状态
				currentCount := atomic.LoadInt32(&p.queueCount)
				usagePercent := float64(currentCount) / float64(p.queueSize) * 100

				// 记录队列状态
				zaplog.Logger.Info("Funnel data queue status",
					zap.Int("queue_size", p.queueSize),
					zap.Int32("current_count", currentCount),
					zap.Float64("usage_percent", usagePercent))
			}
		}
	}()
}

// SendFunnelData 发送漏斗数据到Kafka
func (p *FunnelProducer) SendFunnelData(data *funnel.FunnelData) error {
	if data == nil {
		return fmt.Errorf("funnel data is nil")
	}

	// 将FunnelData序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal funnel data: %w", err)
	}

	// 使用RequestId作为消息的key
	key := data.RequestId

	// 发送消息到Kafka
	err = p.producer.SendMessage(p.topic, key, jsonData)
	if err != nil {
		return fmt.Errorf("failed to send funnel data: %w", err)
	}

	zaplog.Logger.Debug("Funnel data sent",
		zap.String("request_id", data.RequestId),
		zap.Int32("funnel_id", data.FunnelId),
		zap.Int32("date", data.Date),
		zap.Int32("hour", data.Hour))

	return nil
}

// SendFunnelDataAsync 异步发送漏斗数据到Kafka
func (p *FunnelProducer) SendFunnelDataAsync(data *funnel.FunnelData) error {
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return fmt.Errorf("producer is closed")
	}
	p.mu.Unlock()

	if data == nil {
		return fmt.Errorf("funnel data is nil")
	}

	// 将FunnelData序列化为JSON
	jsonData, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("failed to marshal funnel data: %w", err)
	}

	// 使用RequestId作为消息的key
	key := data.RequestId

	// 检查队列是否已满
	currentCount := atomic.LoadInt32(&p.queueCount)
	if currentCount >= int32(p.queueSize) {
		zaplog.Logger.Warn("Funnel data queue is full, dropping message",
			zap.String("request_id", data.RequestId),
			zap.Int32("funnel_id", data.FunnelId),
			zap.Int("queue_size", p.queueSize),
			zap.Int32("current_count", currentCount))
		return nil
	}

	// 增加队列计数
	atomic.AddInt32(&p.queueCount, 1)

	// 创建队列消息
	msg := &queueMsg{
		topic: p.topic,
		key:   key,
		value: jsonData,
	}

	// 尝试发送到队列，使用非阻塞方式
	select {
	case p.msgQueue <- msg:
		// 消息成功加入队列
		zaplog.Logger.Debug("Funnel data queued",
			zap.String("request_id", data.RequestId),
			zap.Int32("funnel_id", data.FunnelId),
			zap.Int32("date", data.Date),
			zap.Int32("hour", data.Hour))
	default:
		// 队列已满，减少计数并丢弃消息
		atomic.AddInt32(&p.queueCount, -1)
		zaplog.Logger.Warn("Failed to queue funnel data, channel is full",
			zap.String("request_id", data.RequestId),
			zap.Int32("funnel_id", data.FunnelId))
	}

	return nil
}

// Close 关闭生产者
func (p *FunnelProducer) Close() error {
	p.mu.Lock()
	if p.closed {
		p.mu.Unlock()
		return nil
	}
	p.closed = true
	p.mu.Unlock()

	// 关闭消息队列
	close(p.msgQueue)

	// 等待所有消息处理完成，设置超时
	done := make(chan struct{})
	go func() {
		p.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		// 所有消息处理完成
	case <-time.After(5 * time.Second):
		// 超时，记录警告
		zaplog.Logger.Warn("Timeout waiting for funnel data queue to drain")
	}

	// 关闭底层生产者
	return p.producer.Close()
}

// QueueStats 获取队列统计信息
func (p *FunnelProducer) QueueStats() (int, int32) {
	return p.queueSize, atomic.LoadInt32(&p.queueCount)
}