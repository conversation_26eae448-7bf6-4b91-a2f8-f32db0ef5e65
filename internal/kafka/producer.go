package kafka

import (
	"crypto/tls"
	"fmt"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"

	"rtb_model_server/internal/zaplog"
)

// RecordMetadata 记录元数据
type RecordMetadata struct {
	Partition int32
	Offset    int64
}

// Producer Kafka生产者接口
type Producer interface {
	// SendMessage 发送消息到Kafka
	SendMessage(topic string, key string, value []byte) error
	// SendMessageAsync 异步发送消息到Kafka
	SendMessageAsync(topic string, key string, value []byte, callback func(metadata *RecordMetadata, err error))
	// Close 关闭生产者
	Close() error
}

// KafkaProducer Kafka生产者实现
type KafkaProducer struct {
	syncProducer  sarama.SyncProducer
	asyncProducer sarama.AsyncProducer
	config        *KafkaConfig
	mu            sync.Mutex
}

// KafkaConfig Kafka配置
type KafkaConfig struct {
	// Brokers Kafka服务器地址列表
	Brokers []string `yaml:"brokers"`
	// ClientID 客户端ID
	ClientID string `yaml:"client_id"`
	// RequiredAcks 需要的确认数
	// 0: 不需要确认
	// 1: 只需要leader确认
	// -1: 需要所有副本确认
	RequiredAcks int16 `yaml:"required_acks"`
	// Compression 压缩类型
	// none, gzip, snappy, lz4, zstd
	Compression string `yaml:"compression"`
	// FlushMessages 缓冲的消息数量达到此值时刷新
	FlushMessages int `yaml:"flush_messages"`
	// FlushMaxMessages 一次刷新的最大消息数量
	FlushMaxMessages int `yaml:"flush_max_messages"`
	// FlushFrequency 刷新频率(毫秒)
	FlushFrequency int `yaml:"flush_frequency"`
	// Timeout 超时时间(毫秒)
	Timeout int `yaml:"timeout"`
	// MaxRetries 最大重试次数
	MaxRetries int `yaml:"max_retries"`
	// RetryBackoff 重试间隔(毫秒)
	RetryBackoff int `yaml:"retry_backoff"`
	// SASL认证配置
	// EnableSASL 是否启用SASL认证
	EnableSASL bool `yaml:"enable_sasl"`
	// SASLMechanism SASL认证机制 (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512)
	SASLMechanism string `yaml:"sasl_mechanism"`
	// SASLUsername SASL用户名
	SASLUsername string `yaml:"sasl_username"`
	// SASLPassword SASL密码
	SASLPassword string `yaml:"sasl_password"`
	// EnableTLS 是否启用TLS
	EnableTLS bool `yaml:"enable_tls"`
	// TLSSkipVerify 是否跳过TLS证书验证
	TLSSkipVerify bool `yaml:"tls_skip_verify"`
}

// NewKafkaProducer 创建一个新的Kafka生产者
func NewKafkaProducer(config *KafkaConfig) (*KafkaProducer, error) {
	if config == nil {
		return nil, fmt.Errorf("kafka config is nil")
	}

	// 创建Kafka配置
	saramaConfig := sarama.NewConfig()
	saramaConfig.ClientID = config.ClientID
	saramaConfig.Producer.RequiredAcks = sarama.RequiredAcks(config.RequiredAcks)
	saramaConfig.Producer.Return.Successes = true
	saramaConfig.Producer.Return.Errors = true

	// 设置压缩类型
	switch config.Compression {
	case "gzip":
		saramaConfig.Producer.Compression = sarama.CompressionGZIP
	case "snappy":
		saramaConfig.Producer.Compression = sarama.CompressionSnappy
	case "lz4":
		saramaConfig.Producer.Compression = sarama.CompressionLZ4
	case "zstd":
		saramaConfig.Producer.Compression = sarama.CompressionZSTD
	default:
		saramaConfig.Producer.Compression = sarama.CompressionNone
	}

	// 设置刷新配置
	if config.FlushMessages > 0 {
		saramaConfig.Producer.Flush.Messages = config.FlushMessages
	}
	if config.FlushMaxMessages > 0 {
		saramaConfig.Producer.Flush.MaxMessages = config.FlushMaxMessages
	}
	if config.FlushFrequency > 0 {
		saramaConfig.Producer.Flush.Frequency = time.Duration(config.FlushFrequency) * time.Millisecond
	}

	// 设置超时和重试
	if config.Timeout > 0 {
		saramaConfig.Producer.Timeout = time.Duration(config.Timeout) * time.Millisecond
	}
	if config.MaxRetries > 0 {
		saramaConfig.Producer.Retry.Max = config.MaxRetries
	}
	if config.RetryBackoff > 0 {
		saramaConfig.Producer.Retry.Backoff = time.Duration(config.RetryBackoff) * time.Millisecond
	}

	// 配置SASL认证
	if config.EnableSASL {
		saramaConfig.Net.SASL.Enable = true
		saramaConfig.Net.SASL.User = config.SASLUsername
		saramaConfig.Net.SASL.Password = config.SASLPassword

		// 设置SASL认证机制
		switch config.SASLMechanism {
		case "PLAIN":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
		case "SCRAM-SHA-256":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA256
		case "SCRAM-SHA-512":
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypeSCRAMSHA512
		default:
			// 默认使用PLAIN
			saramaConfig.Net.SASL.Mechanism = sarama.SASLTypePlaintext
			zaplog.Logger.Warn("Unknown SASL mechanism, using PLAIN", zap.String("mechanism", config.SASLMechanism))
		}

		zaplog.Logger.Info("SASL authentication enabled", 
			zap.String("mechanism", config.SASLMechanism),
			zap.String("username", config.SASLUsername))
	}

	// 配置TLS
	if config.EnableTLS {
		saramaConfig.Net.TLS.Enable = true
		saramaConfig.Net.TLS.Config = &tls.Config{
			InsecureSkipVerify: config.TLSSkipVerify,
		}
		zaplog.Logger.Info("TLS enabled", zap.Bool("skip_verify", config.TLSSkipVerify))
	}

	// 创建同步生产者
	syncProducer, err := sarama.NewSyncProducer(config.Brokers, saramaConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create sync producer: %w", err)
	}

	// 创建异步生产者
	asyncProducer, err := sarama.NewAsyncProducer(config.Brokers, saramaConfig)
	if err != nil {
		_ = syncProducer.Close() // 关闭已创建的同步生产者
		return nil, fmt.Errorf("failed to create async producer: %w", err)
	}

	// 启动异步生产者的错误处理
	go func() {
		for err := range asyncProducer.Errors() {
			zaplog.Logger.Error("Async producer error", zap.Error(err))
		}
	}()

	return &KafkaProducer{
		syncProducer:  syncProducer,
		asyncProducer: asyncProducer,
		config:        config,
	}, nil
}

// SendMessage 同步发送消息到Kafka
func (p *KafkaProducer) SendMessage(topic string, key string, value []byte) error {
	p.mu.Lock()
	defer p.mu.Unlock()

	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(value),
	}

	if key != "" {
		msg.Key = sarama.StringEncoder(key)
	}

	partition, offset, err := p.syncProducer.SendMessage(msg)
	if err != nil {
		return fmt.Errorf("failed to send message: %w", err)
	}

	zaplog.Logger.Debug("Message sent",
		zap.String("topic", topic),
		zap.String("key", key),
		zap.Int32("partition", partition),
		zap.Int64("offset", offset))

	return nil
}

// SendMessageAsync 异步发送消息到Kafka
func (p *KafkaProducer) SendMessageAsync(topic string, key string, value []byte, callback func(metadata *RecordMetadata, err error)) {
	msg := &sarama.ProducerMessage{
		Topic: topic,
		Value: sarama.ByteEncoder(value),
	}

	if key != "" {
		msg.Key = sarama.StringEncoder(key)
	}

	// 设置元数据，用于回调
	if callback != nil {
		msg.Metadata = callback
	}

	p.asyncProducer.Input() <- msg

	// 如果有回调，处理成功和失败的情况
	if callback != nil {
		go func() {
			select {
			case success := <-p.asyncProducer.Successes():
				metadata := &RecordMetadata{
					Partition: success.Partition,
					Offset:    success.Offset,
				}
				callback(metadata, nil)
			case err := <-p.asyncProducer.Errors():
				callback(nil, err)
			}
		}()
	}
}

// Close 关闭生产者
func (p *KafkaProducer) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// 关闭异步生产者
	if err := p.asyncProducer.Close(); err != nil {
		zaplog.Logger.Error("Failed to close async producer", zap.Error(err))
	}

	// 关闭同步生产者
	if err := p.syncProducer.Close(); err != nil {
		return fmt.Errorf("failed to close sync producer: %w", err)
	}

	return nil
}