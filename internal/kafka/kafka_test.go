package kafka

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestKafkaConfigSASL 测试SASL配置
func TestKafkaConfigSASL(t *testing.T) {
	// 测试基本配置
	config := &KafkaConfig{
		Brokers:      []string{"localhost:9092"},
		ClientID:     "test-client",
		RequiredAcks: 1,
		Compression:  "snappy",
		EnableSASL:   false,
		EnableTLS:    false,
	}

	assert.False(t, config.EnableSASL)
	assert.False(t, config.EnableTLS)
	assert.Equal(t, "test-client", config.ClientID)
	assert.Equal(t, []string{"localhost:9092"}, config.Brokers)
}

// TestKafkaConfigWithSASL 测试启用SASL的配置
func TestKafkaConfigWithSASL(t *testing.T) {
	config := &KafkaConfig{
		Brokers:       []string{"localhost:9092"},
		ClientID:      "test-client",
		RequiredAcks:  1,
		Compression:   "snappy",
		EnableSASL:    true,
		SASLMechanism: "SCRAM-SHA-256",
		SASLUsername:  "testuser",
		SASLPassword:  "testpass",
		EnableTLS:     true,
		TLSSkipVerify: false,
	}

	assert.True(t, config.EnableSASL)
	assert.True(t, config.EnableTLS)
	assert.Equal(t, "SCRAM-SHA-256", config.SASLMechanism)
	assert.Equal(t, "testuser", config.SASLUsername)
	assert.Equal(t, "testpass", config.SASLPassword)
	assert.False(t, config.TLSSkipVerify)
}

// TestKafkaConfigValidation 测试配置验证
func TestKafkaConfigValidation(t *testing.T) {
	// 测试nil配置
	producer, err := NewKafkaProducer(nil)
	assert.Nil(t, producer)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "kafka config is nil")
}
