syntax = "proto3";

package funnel;
option go_package = "./;funnel";

message FunnelData {
  int32 funnel_id = 1;
  int32 date = 2;
  int32 hour = 3;
  string request_id = 4;
  int64 search_id = 5;
  string ui_name = 6;
  
  // 基础字段
  int32 exchange_id = 7;
  int32 adx_exchange_id = 8;
  int32 dm_media_id = 9;
  string app_bundle = 10;
  
  // 设备信息
  string idfa = 11;
  string idfamd5 = 12;
  string oaidmd5 = 13;
  string gaid = 14;
  int32 platform = 15;
  int32 os = 16;
  int32 access = 17;
  int32 city = 18;
  int32 carrier = 19;
  
  // 嵌套结构
  FunnelTiming timing = 20;
  FunnelRequest request = 21;
  FunnelResponse response = 22;
  RuntimeData runtime_data = 23;
}

message FunnelTiming {
  int32 total = 1;
  int32 user_profile = 2;
  int32 filter = 3;
  int32 dmp = 4;
  int32 clk_frequency = 5;
  int32 imp_frequency = 6;
  int32 predict_server = 7;
  int32 sort = 8;
  int32 render = 9;
}

message RuntimeData {
  map<int32, int32> creative_filter_map = 1;
  repeated int32 recall_creatives = 2;
  repeated int32 device_dmp_target = 3;
  repeated int32 resource_target_white_list = 4;
  repeated int32 resource_target_black_list = 5;
  map<string, int32> clk_frequency_data = 6;
  map<string, int32> imp_frequency_data = 7;
}

// 请求信息
message FunnelRequest {
  repeated FunnelRequestAd req_ads = 1; // 请求广告列表
}

// 请求广告信息
message FunnelRequestAd {
  int32 idx = 1;            // 广告位索引
  string imp_id = 2;        // 曝光ID
  repeated int64 ad_match_type = 3;  // 广告素材ID列表
  int64 bid_floor = 4;      // 竞价标识
  int64 cpc_bid_floor = 5;  // CPC竞价标识
  int32 media_bid_type = 6; // 媒体竞价类型
  int64 deal_floor = 7;     // 交易标识
  string deal_id = 8;       // 交易ID
}

// 响应信息
message FunnelResponse {
  repeated FunnelResponseAd resp_ads = 1; // 响应广告列表
}

// 响应广告信息
message FunnelResponseAd {
  int32 idx = 1;    // 广告位索引
  int32 ad_num = 2; // 广告数量
  int32 imp_id = 3; // 曝光ID
  FunnelResponseAdInfo ads = 4; // 广告详情
}

// 响应广告详细信息
message FunnelResponseAdInfo {
  int32 index = 1;     // idx
  int32 cid = 2;       // 创意ID
  int32 sid = 3;       // 策略ID
  int32 pid = 4;       // 计划ID
  int32 uid = 5;       // 用户ID
  int64 bid = 6;       // 出价
  int64 ctr = 7;       // 点击率
  int32 funneld = 8;   // 漏斗配置ID
  int64 atr = 9;       // 转化率
  int64 adm = 10;      // 广告素材ID
  int32 bid_type = 11; // 出价类型
  int64 fin_price = 12; // 最终价格
}