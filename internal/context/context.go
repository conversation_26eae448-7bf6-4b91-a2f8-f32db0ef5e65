package context

import (
	"fmt"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_model_server"
	"rtb_model_server/common/domob_thrift/rtb_types"
	"rtb_model_server/internal/model_index/adindex"
	"rtb_model_server/internal/model_index/modules"
	"time"
)

// RequestContext 每次广告请求上下文，不会复用
type RequestContext struct {
	TimeStart time.Time
	// 广告IndexManager
	AdIndex *adindex.AdIndexManager
	// 广告请求
	BidRequest *rtb_types.RTBBidRequest
	// 广告响应
	BidResponse *rtb_model_server.RTBModelServerResponse
	// 当前正在过滤中的创意ID对应的广告库数据，这个会随着创意过滤一直在变化
	CurrentFilterCampaign  *rtb_adinfo_types.RTBCampaign
	CurrentFilterStrategy  *rtb_adinfo_types.RTBStrategy
	CurrentFilterCreative  *rtb_adinfo_types.RTBCreative
	CurrentFilterSponsor   *rtb_adinfo_types.RTBSponsor
	CurrentFilterPromotion *rtb_adinfo_types.RTBPromotion
	CurrentFilterTracking  *rtb_adinfo_types.AdTrackingInfo

	// 时间消耗情况
	TimeCost map[string]int64
	// 过滤器缓存情况，key是创意ID，value是过滤原因
	CreativeFilterMap map[int32]int32
	// 定向召回的创意ID
	RecallCreatives []int32
	//此次请求命中的DMP列表
	DeviceDmpTarget []int32
	//此次请求命中的资源包黑白名单
	ResourceTargetWhiteList []int32
	ResourceTargetBlackList []int32
	//公共的RedisPool
	DeviceDmpTargetRedisPool *modules.RedisPool
	FrequencyRedisPool       *modules.RedisPool
	// 公共的资源包
	ResourceTargetProcessor *modules.ResourceTargetProcessor
	// 公共的频控配置
	FrequencyProcessor *modules.FrequencyProcessor

	// 该设备是否开启漏斗日志，只要设备有命中，整体链路产生的日志都将开启
	EnableFunnelLog bool
	// value是匹配上的漏斗ID
	EnableDeviceFunnelLogFid int32
	// 该创意是否开启漏斗日志，这个创意至少需要命中AdMatchType层级
	// value是匹配上的漏斗ID
	EnableFunnelLogCreativesFid map[int32]int32

	// 该设备拉起的点击频控数据
	ClkFrequencyData map[string]int
	// 该设备拉起的曝光频控数据
	ImpFrequencyData map[string]int
}

// AddTimeCostMicroSecond 单位是微秒
func (ctx *RequestContext) AddTimeCost(key string, cost int64) {
	ctx.TimeCost[key] = cost
}

// GetTimeCostMicroSecond 单位是微秒
func (ctx *RequestContext) GetTimeCost(key string) int64 {
	return ctx.TimeCost[key]
}

// GetTimeCostSummary 单位是微秒
func (ctx *RequestContext) GetTimeCostSummary() string {
	var summary string
	var total int64
	for key, cost := range ctx.TimeCost {
		if key == "total" {
			total = cost
			continue
		}
		summary += fmt.Sprintf("%s=%d|", key, cost)
	}
	return fmt.Sprintf("tot=%d|%s", total, summary)
}

func (ctx *RequestContext) String() string {
	return ctx.ShortLog()
}

func (ctx *RequestContext) ShortLog() string {
	if ctx.BidRequest == nil {
		return "[empty_request]"
	}

	// 基础请求信息
	reqId := ctx.BidRequest.ReqId
	uiName := ctx.BidRequest.UiName
	exchangeId := ctx.BidRequest.ExchangeId
	adxExchangeId := ctx.BidRequest.AdxExchangeId
	searchId := ctx.BidRequest.SearchId

	// 媒体信息
	var mediaId int32
	var appBundle string
	if ctx.BidRequest.App != nil {
		mediaId = ctx.BidRequest.App.DmMediaId
		appBundle = ctx.BidRequest.App.AppBundle
	}

	// 设备信息
	var deviceId int32
	var platform int32
	var osId int32
	var accessType int32
	var cityId int32
	var carrierId int32
	var oaidmd5 string
	var gaid string
	var idfamd5 string
	var capabilities []string
	var reqired_capabilities []string
	if ctx.BidRequest.Device != nil {
		deviceId = ctx.BidRequest.Device.DmDeviceId
		platform = ctx.BidRequest.Device.DmPlatform
		osId = ctx.BidRequest.Device.DmOsId
		accessType = ctx.BidRequest.Device.DmAccesstypeId
		cityId = ctx.BidRequest.Device.GeoCity
		carrierId = ctx.BidRequest.Device.DmCarrierId
		oaidmd5 = ctx.BidRequest.Device.Oaidmd5
		gaid = ctx.BidRequest.Device.Gaid
		idfamd5 = ctx.BidRequest.Device.Idfamd5
	}
	for capability, ok := range ctx.BidRequest.Capabilities {
		if ok {
			capabilities = append(capabilities, fmt.Sprintf("%d", capability))
		}
	}
	for capability, ok := range ctx.BidRequest.RequiredCapabilities {
		if ok {
			reqired_capabilities = append(reqired_capabilities, fmt.Sprintf("%d", capability))
		}
	}

	// 时间消耗统计
	timeCostSummary := ctx.GetTimeCostSummary()

	// 广告位信息
	var totReqAdNum int
	var reqStr string
	if len(ctx.BidRequest.BidList) > 0 {
		var bidStr string
		for idx, bidInfo := range ctx.BidRequest.BidList {
			if bidInfo.Request != nil {
				req := bidInfo.Request
				totReqAdNum++
				bidStr = fmt.Sprintf("(idx:%d<imp_id=%d adm=%v bidf=%d cpc_bidf=%d media_bid_type=%d dealf=%d deal_id=%s>)",
					idx, bidInfo.Request.SearchImpId, req.AdMatchTypes,
					req.Bidfloor, req.CpcBidfloor, req.SupportMediaBidType, req.Dealfloor, req.DealId,
				)
			}
		}
		reqStr += bidStr
	}

	filterReasonStats := ""
	filterReasonMap := make(map[int32]int32)
	for _, filterType := range ctx.CreativeFilterMap {
		if _, ok := filterReasonMap[filterType]; !ok {
			filterReasonMap[filterType] = 1
		} else {
			filterReasonMap[filterType]++
		}
	}
	for filterType, filterNum := range filterReasonMap {
		filterReasonStats += fmt.Sprintf("%d=%d|", filterType, filterNum)
	}

	// 响应信息
	var responseNum int
	var totAdNum int
	var respStr string
	if ctx.BidResponse != nil && len(ctx.BidResponse.ResponseList) > 0 {
		for idx, respInfo := range ctx.BidResponse.ResponseList {
			var respAdListStr string
			var respAdStr string
			if respInfo.AdList != nil && len(respInfo.AdList) > 0 {

				for _, adInfo := range respInfo.AdList {
					respAdStr += fmt.Sprintf("<cid=%d sid=%d pid=%d uid=%d bid=%d ctr=%d atr=%d adm=%d fin_price=%d>",
						adInfo.CreativeId, adInfo.StrategyId, adInfo.CampaignId, adInfo.SponsorId, adInfo.Bid, adInfo.Ctr, adInfo.Atr, adInfo.AdMatchType, adInfo.FinPrice)
				}

			}
			respAdListStr += fmt.Sprintf("(idx:%d ad_num=%d imp_id=%d ads:%s)",
				idx, len(respInfo.AdList), respInfo.SearchImpId, respAdStr)
			respStr += respAdListStr
		}
	}

	// 构建日志字符串
	return fmt.Sprintf("[rid:%s][sid:%d][fid=%d][ui:%s][tm:%s] exchange=%d adx_exchange=%d media_id=%d app_bundle=%s device=%d platform=%d os=%d access=%d city=%d carrier=%d oaidmd5=%s gaid=%s idfamd5=%s req_ads=[%s] resp_ads=[%s] filters=[%s] tot_req_ad_num=%d response_num=%d tot_ad_num=%d",
		reqId,
		searchId,
		ctx.EnableDeviceFunnelLogFid,
		uiName,
		timeCostSummary,
		exchangeId,
		adxExchangeId,
		mediaId,
		appBundle,
		deviceId,
		platform,
		osId,
		accessType,
		cityId,
		carrierId,
		oaidmd5,
		gaid,
		idfamd5,
		reqStr,
		respStr,
		filterReasonStats,
		totReqAdNum,
		responseNum,
		totAdNum)
}

func (ctx *RequestContext) FullLog() interface{} {
	return NewFunnelData(ctx)
}
