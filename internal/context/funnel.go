package context

import (
	funnel "rtb_model_server/common/funnel_gengo"
)

func NewFunnelData(ctx *RequestContext) *funnel.FunnelData {
	if ctx == nil || ctx.BidRequest == nil {
		return &funnel.FunnelData{}
	}

	req := ctx.BidRequest
	t1 := ctx.TimeStart
	funnelData := &funnel.FunnelData{
		FunnelId:      ctx.EnableDeviceFunnelLogFid,
		Date:          int32(t1.Year())*10000 + int32(t1.Month())*100 + int32(t1.Day()),
		Hour:          int32(t1.Hour()),
		RequestId:     req.ReqId,
		SearchId:      req.SearchId,
		UiName:        req.UiName,
		ExchangeId:    req.ExchangeId,
		AdxExchangeId: req.AdxExchangeId,
		Os:            req.Device.DmOsId,
		Timing:        &funnel.FunnelTiming{},
		Request:       &funnel.FunnelRequest{},
		Response:      &funnel.FunnelResponse{},
		RuntimeData:   &funnel.RuntimeData{},
	}

	// 从App信息中获取字段
	if req.App != nil {
		funnelData.DmMediaId = req.App.DmMediaId
		funnelData.AppBundle = req.App.AppBundle
	}

	// 从Device信息中获取字段
	if req.Device != nil {
		funnelData.Idfa = req.Device.Idfa
		funnelData.Idfamd5 = req.Device.Idfamd5
		funnelData.Oaidmd5 = req.Device.Oaidmd5
		funnelData.Gaid = req.Device.Gaid
		funnelData.Platform = req.Device.DmPlatform
		funnelData.Os = req.Device.DmOsId
		funnelData.Access = req.Device.DmAccesstypeId
		funnelData.City = req.Device.GeoCity
		funnelData.Carrier = req.Device.DmCarrierId
	}

	// 填充请求广告信息
	if req.BidList != nil {
		funnelData.Request.ReqAds = make([]*funnel.FunnelRequestAd, len(req.BidList))
		for i, bidInfo := range req.BidList {
			if bidInfo.Request != nil {
				funnelData.Request.ReqAds[i] = &funnel.FunnelRequestAd{
					Idx:          int32(i),
					ImpId:        bidInfo.Request.ImpId,
					AdMatchType:  bidInfo.Request.AdMatchTypes,
					BidFloor:     bidInfo.Request.Bidfloor,
					CpcBidFloor:  bidInfo.Request.CpcBidfloor,
					MediaBidType: bidInfo.Request.SupportMediaBidType,
					DealFloor:    bidInfo.Request.Dealfloor,
					DealId:       bidInfo.Request.DealId,
				}
			} else {
				funnelData.Request.ReqAds[i] = &funnel.FunnelRequestAd{Idx: int32(i)}
			}
		}
	}

	// 填充RuntimeData
	fillRuntimeData(ctx, funnelData)
	fillResponse(ctx, funnelData)
	fillTiming(ctx, funnelData)

	return funnelData
}

// convertInt64SliceToIntSlice 将int64切片转换为int切片
func convertInt64SliceToIntSlice(int64Slice []int64) []int {
	if int64Slice == nil {
		return nil
	}
	intSlice := make([]int, len(int64Slice))
	for i, v := range int64Slice {
		intSlice[i] = int(v)
	}
	return intSlice
}

// FillRuntimeData 填充运行时数据
func fillRuntimeData(ctx *RequestContext, d *funnel.FunnelData) {
	if d == nil || d.RuntimeData == nil || ctx == nil {
		return
	}

	// 初始化map
	d.RuntimeData.CreativeFilterMap = make(map[int32]int32)
	d.RuntimeData.ClkFrequencyData = make(map[string]int32)
	d.RuntimeData.ImpFrequencyData = make(map[string]int32)

	// 从RequestContext中获取数据
	if ctx.CreativeFilterMap != nil {
		for k, v := range ctx.CreativeFilterMap {
			d.RuntimeData.CreativeFilterMap[k] = v
		}
	}

	if ctx.RecallCreatives != nil {
		d.RuntimeData.RecallCreatives = make([]int32, len(ctx.RecallCreatives))
		copy(d.RuntimeData.RecallCreatives, ctx.RecallCreatives)
	}

	if ctx.DeviceDmpTarget != nil {
		d.RuntimeData.DeviceDmpTarget = make([]int32, len(ctx.DeviceDmpTarget))
		copy(d.RuntimeData.DeviceDmpTarget, ctx.DeviceDmpTarget)
	}

	if ctx.ResourceTargetWhiteList != nil {
		d.RuntimeData.ResourceTargetWhiteList = make([]int32, len(ctx.ResourceTargetWhiteList))
		copy(d.RuntimeData.ResourceTargetWhiteList, ctx.ResourceTargetWhiteList)
	}

	if ctx.ResourceTargetBlackList != nil {
		d.RuntimeData.ResourceTargetBlackList = make([]int32, len(ctx.ResourceTargetBlackList))
		copy(d.RuntimeData.ResourceTargetBlackList, ctx.ResourceTargetBlackList)
	}

	if ctx.ClkFrequencyData != nil {
		for k, v := range ctx.ClkFrequencyData {
			d.RuntimeData.ClkFrequencyData[k] = int32(v)
		}
	}

	if ctx.ImpFrequencyData != nil {
		for k, v := range ctx.ImpFrequencyData {
			d.RuntimeData.ImpFrequencyData[k] = int32(v)
		}
	}
}

// FillResponse 填充响应数据
func fillResponse(ctx *RequestContext, d *funnel.FunnelData) {
	if d == nil || d.Response == nil || ctx == nil || ctx.BidResponse == nil {
		return
	}

	response := ctx.BidResponse
	if response.ResponseList == nil {
		return
	}

	// 初始化响应广告列表
	d.Response.RespAds = make([]*funnel.FunnelResponseAd, 0)

	// 遍历响应中的广告信息
	for idx, responseInfo := range response.ResponseList {
		if responseInfo == nil || responseInfo.AdList == nil {
			continue
		}

		respAd := &funnel.FunnelResponseAd{
			Idx:   int32(idx),
			AdNum: int32(len(responseInfo.AdList)),
			ImpId: int32(responseInfo.SearchImpId),
		}

		// 填充第一个广告的详情（如果存在）
		if len(responseInfo.AdList) > 0 {
			adInfo := responseInfo.AdList[0]
			respAd.Ads = &funnel.FunnelResponseAdInfo{
				Index:    int32(idx),
				Cid:      adInfo.CreativeId,
				Sid:      adInfo.StrategyId,
				Pid:      adInfo.CampaignId, // 使用CampaignId作为计划ID
				Uid:      adInfo.SponsorId,  // 使用SponsorId作为用户ID
				Bid:      adInfo.Bid,
				Ctr:      int64(adInfo.Ctr),
				Atr:      int64(adInfo.Atr),
				Adm:      adInfo.AdMatchType,
				BidType:  0, // RTBModelServerAdInfo中没有BidType字段，设为0
				FinPrice: adInfo.FinPrice,
			}
		} else {
			// 如果没有广告信息，创建空的广告详情
			respAd.Ads = &funnel.FunnelResponseAdInfo{
				Index: int32(idx),
			}
		}

		d.Response.RespAds = append(d.Response.RespAds, respAd)
	}
}

// FillTiming 填充时间统计数据
func fillTiming(ctx *RequestContext, d *funnel.FunnelData) {
	if d == nil || d.Timing == nil || ctx == nil {
		return
	}

	// 从RequestContext的TimeCost中获取时间统计
	if ctx.TimeCost != nil {
		if total, ok := ctx.TimeCost["total"]; ok {
			d.Timing.Total = int32(total)
		}
		if filter, ok := ctx.TimeCost["filter"]; ok {
			d.Timing.Filter = int32(filter)
		}
		if dmp, ok := ctx.TimeCost["dmp"]; ok {
			d.Timing.Dmp = int32(dmp)
		}
		if freq, ok := ctx.TimeCost["clk_freq"]; ok {
			d.Timing.ClkFrequency = int32(freq)
		}
		if freq, ok := ctx.TimeCost["imp_freq"]; ok {
			d.Timing.ImpFrequency = int32(freq)
		}
		if predict, ok := ctx.TimeCost["predict"]; ok {
			d.Timing.PredictServer = int32(predict)
		}
		if sort, ok := ctx.TimeCost["sort"]; ok {
			d.Timing.Sort = int32(sort)
		}
		if render, ok := ctx.TimeCost["render"]; ok {
			d.Timing.Render = int32(render)
		}
		if up, ok := ctx.TimeCost["up"]; ok {
			d.Timing.UserProfile = int32(up)
		}
	}
}

// ToProtobuf 转为Protobuf消息
func ToProtobuf(d *funnel.FunnelData) interface{} {
	// 导入funnel包
	// 注意：这里需要根据实际导入路径调整
	// import "rtb_model_server/internal/context/funnel"
	// return funnel.ToProto(d)

	// 由于我们不能直接在这里导入funnel包（会导致循环导入），
	// 所以这里返回interface{}，实际使用时需要在外部调用funnel.ToProto(d)
	return d
}
