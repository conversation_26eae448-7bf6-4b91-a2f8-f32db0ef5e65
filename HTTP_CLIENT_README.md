# RTB Model Server HTTP Client

## 概述

RTB Model Server HTTP Client 是一个HTTP代理服务，可以接收HTTP POST JSON请求，将其转换为Thrift协议请求并转发到RTB Model Server，然后将响应转换回JSON格式返回。

## 功能特性

- 支持HTTP POST JSON请求
- 自动转换JSON到Thrift协议
- 可配置Model Server地址
- 健康检查接口
- 请求/响应日志记录

## 构建和运行

### 本地运行

```bash
# 构建
go build -o rtb_model_client cmd/client_http/main.go

# 运行
./rtb_model_client -c conf/rtb_model_server.yaml -port 8080 -model-server localhost:9090
```

### Docker构建和运行

```bash
# 构建Docker镜像
./build-client.sh

# 运行Docker容器
docker run -p 8080:8080 rtb_model_client:latest
```

## API接口

### 1. 竞价请求接口

**URL:** `POST /bid`

**Content-Type:** `application/json`

**请求体示例:**

```json
{
  "reqId": "550e8400-e29b-41d4-a716-************",
  "searchId": 12346,
  "uiName": "test-ui",
  "exchangeId": 132,
  "device": {
    "ip": "************",
    "dmPlatform": 1,
    "oaidmd5": "7815696ecbf1c96e6894b779456d3301",
    "installledApps": [
      "com.eg.android.AlipayGphone"
    ],
    "userAgent": "Mozilla/5.0 (Linux; Android 11; Samsung SM-G998B)"
  },
  "app": {
    "dmMediaId": 12313,
    "appBundle": "com.eg.android"
  },
  "bidList": [
    {
      "request": {
        "searchImpId": 123467,
        "impId": "xxxxxxx-imp01",
        "adMatchTypes": [1320000000020],
        "supportMediaBidType": 2
      }
    }
  ],
  "requiredCapabilities": {
    "1002": true
  },
  "capabilities": {
    "2001": true
  }
}
```

**响应示例:**

```json
{
  "status": 0,
  "searchId": 12346,
  "dmpTagIds": [],
  "dmUid": 0,
  "idfa": "",
  "imei": "",
  "dmpKeywordIds": [],
  "dmpCrowdIds": [],
  "responseList": [
    {
      "searchImpId": 123467,
      "expid": -1,
      "trafId": 0,
      "adList": [
        {
          "creativeId": 12345,
          "campaignId": 67890,
          "strategyId": 11111,
          "sponsorId": 22222,
          "bid": 1000000,
          "ctr": 50000,
          "atr": 10000
        }
      ]
    }
  ]
}
```

### 2. 健康检查接口

**URL:** `GET /health`

**响应:**

```json
{
  "status": "ok"
}
```

## 命令行参数

- `-c`: 配置文件路径 (默认: `conf/rtb_model_server.yaml`)
- `-port`: HTTP服务端口 (默认: `8080`)
- `-model-server`: Model Server地址 (默认: `localhost:9090`)

## 使用示例

### curl请求示例

```bash
# 发送竞价请求
curl -X POST http://localhost:8080/bid \
  -H "Content-Type: application/json" \
  -d '{
    "reqId": "550e8400-e29b-41d4-a716-************",
    "searchId": 12346,
    "uiName": "test-ui",
    "exchangeId": 132,
    "device": {
      "ip": "************",
      "dmPlatform": 1,
      "oaidmd5": "7815696ecbf1c96e6894b779456d3301",
      "installledApps": ["com.eg.android.AlipayGphone"],
      "userAgent": "Mozilla/5.0 (Linux; Android 11; Samsung SM-G998B)"
    },
    "app": {
      "dmMediaId": 12313,
      "appBundle": "com.eg.android"
    },
    "bidList": [{
      "request": {
        "searchImpId": 123467,
        "impId": "xxxxxxx-imp01",
        "adMatchTypes": [1320000000020],
        "supportMediaBidType": 2
      }
    }],
    "requiredCapabilities": {"1002": true},
    "capabilities": {"2001": true}
  }'

# 健康检查
curl http://localhost:8080/health
```

## 日志

服务会记录以下日志信息：
- 服务启动信息
- 请求转发日志
- 响应接收日志
- 错误信息

## 错误处理

- `400 Bad Request`: JSON解析错误或请求格式错误
- `405 Method Not Allowed`: 使用了非POST方法访问/bid接口
- `500 Internal Server Error`: Model Server连接错误或内部处理错误

## 注意事项

1. 确保Model Server服务正在运行并且可访问
2. 请求的JSON格式必须符合RTBBidRequest结构
3. 服务默认监听所有网络接口，生产环境请注意安全配置
4. 建议在生产环境中配置适当的日志级别和轮转策略