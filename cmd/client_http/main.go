package main

import (
	"flag"
	"log"
	"rtb_model_server/conf"
	"rtb_model_server/internal/mock"
)

func main() {
	configFile := flag.String("c", "conf/rtb_model_server.yaml", "default conf/rtb_model_server.yaml")
	port := flag.Int("port", 8080, "HTTP server port")
	modelServerAddr := flag.String("model-server", "localhost:3398", "Model server address")
	flag.Parse()

	log.Println("Using config file: " + *configFile)
	err := conf.ConfigInit(*configFile)
	if err != nil {
		log.Fatal("config init error", err)
	}

	// 启动HTTP客户端服务
	httpClient := mock.NewHTTPClient(*modelServerAddr)
	err = httpClient.Start(*port)
	if err != nil {
		log.Fatal("HTTP client start error ", err)
	}
}
