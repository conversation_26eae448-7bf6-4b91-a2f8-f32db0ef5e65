Server:
  Name: rtb_model_server
  Port: 3398

PredictServer:
  Addr: "127.0.0.1:3399"
  ModelName: "default_model"
  PoolSize: 10
  MaxIdleConns: 5
  ConnTimeout: 1000

LogConfig:
  LogPath: log/rtb_model_sever.log
  LogLevel: debug
  MaxAgeDays: 7
  MaxSize: 100
  MaxBackups: 100
  Compress: false

FunnelLogConfig:
  LogPath: log/rtb_funnel.__POD__
  LogLevel: info
  MaxAgeDays: 7
  MaxSize: 100
  MaxBackups: 100
  Compress: true

FunnelConfig:
  ConfigFilePath: data/resource/funnel.data
  Interval: 300
  FunnelOpenRatio: 10000

FunnelKafkaConfig:
  # 是否启用Kafka发送
  Enabled: true
  # Kafka主题
  Topic: "dm_dsp_kafka_reader_test"
  # 异步发送队列大小，0表示不限制
  QueueSize: 10000
  # Kafka配置
  KafkaConfig:
    # Kafka服务器地址列表
    Brokers:
      - "kafka01-dev.domob-inc.cn:9093"
      - "kafka02-dev.domob-inc.cn:9093"
      - "kafka03-dev.domob-inc.cn:9093"
    # 客户端ID
    ClientID: "rtb-model-server"
    # 需要的确认数 (0: 不需要确认, 1: 只需要leader确认, -1: 需要所有副本确认)
    RequiredAcks: 1
    # 压缩类型 (none, gzip, snappy, lz4, zstd)
    Compression: "snappy"
    # 缓冲的消息数量达到此值时刷新
    FlushMessages: 10
    # 一次刷新的最大消息数量
    FlushMaxMessages: 100
    # 刷新频率(毫秒)
    FlushFrequency: 500
    # 超时时间(毫秒)
    Timeout: 3000
    # 最大重试次数
    MaxRetries: 3
    # 重试间隔(毫秒)
    RetryBackoff: 100
    
    # SASL认证配置 - 启用示例
    # 是否启用SASL认证
    EnableSASL: false
    # SASL认证机制 (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512)
    SASLMechanism: "SCRAM-SHA-256"
    # SASL用户名
    SASLUsername: "your_kafka_username"
    # SASL密码
    SASLPassword: "your_kafka_password"
    
    # TLS配置 - 启用示例
    # 是否启用TLS
    EnableTLS: false
    # 是否跳过TLS证书验证 (生产环境建议设为false)
    TLSSkipVerify: false

ModelIndex:
  AdIndexPath:
    AdCampaign: data/adindex/ad_campaign.data
    AdStrategy: data/adindex/ad_strategy.data
    AdCreative: data/adindex/ad_creative.data
    AdSponsor: data/adindex/ad_sponsor.data
    AdPromotion: data/adindex/ad_promotion.data
    AdTracking: data/adindex/ad_tracking.data
  ResourceTargetPath: data/resource/resource-target.data
  IP2RegionDbPath: data/ip2region/ip2region.xdb
  DeviceMappingPath: data/device/device_mapping.txt
  AdIndexFileExpireMinutes: 10000

BudgetConfig:
  # 原有配置保持兼容
  StatsFilePath: data/budget/stats.data
  # 轮询监控配置
  StatsFileDir: data/budget/
  StatsFileNamePattern: stats.data
  PollingInterval: 30
  # 其他配置项
  SpeedStopRate: 0.01
  SmallBudgetThreshold: 1000000000
  BudgetThresholdRatio: 0.01
  MaxBidPerMinute: 1000000
  DecelerationFactor: 0.01
  DefaultMediaPrice: 1000000000
  StatsReloadInterval: 300

FrequencyConfig:
  LoopInterval: 300
  FrequencyKey: FC
  FrequencyKeyCount: 2
  ClkFrequencyKeyPrefix: clk
  ImpFrequencyKeyPrefix: imp

DmpTargetRedisPool:
  Addr: "localhost:6379"
  Db: 0
  AuthInfo: ""
  PoolSize: 10
  DialTimeout: 3000
  ReadTimeout: 3000
  WriteTimeout: 3000

FrequencyRedisPool:
  Addr: "localhost:6379"
  Db: 0
  AuthInfo: ""
  PoolSize: 10
  DialTimeout: 3000
  ReadTimeout: 3000
  WriteTimeout: 3000