package conf

import (
	"github.com/spf13/viper"
)

type (
	ServerConfig struct {
		Name string `required:"true"`
		Port int    `required:"true"`
	}
	LogConfig struct {
		LogLevel   string `required:"true"`
		LogPath    string `required:"true"`
		MaxAgeDays int    `required:"true"`
		MaxSize    int    `required:"true"`
		MaxBackups int    `required:"true"`
		Compress   bool
	}
	PredictServer struct {
		Addr         string `required:"true"`
		ModelName    string `required:"true"`
		PoolSize     int    `default:"10"`
		MaxIdleConns int    `default:"5"`
		ConnTimeout  int    `default:"5000"` // milliseconds
	}
	// Model Index Configuration
	ModelIndexConfig struct {
		AdIndexPath struct {
			AdCampaign  string `required:"true"`
			AdStrategy  string `required:"true"`
			AdCreative  string `required:"true"`
			AdSponsor   string `required:"true"`
			AdPromotion string `required:"true"`
			AdTracking  string `required:"true"`
			// AdPoi       string `required:"true"`
			// AdSummary   string `required:"true"`
		} `required:"true"`
		ResourceTargetPath       string `required:"true"`
		IP2RegionDbPath          string `required:"true"`
		DeviceMappingPath        string `required:"true"`
		AdIndexFileExpireMinutes int    // 文件过期时间（分钟）
	}
	RedisPool struct {
		Addr         string
		Db           int
		AuthInfo     string
		PoolSize     int // 设置空默认为MAXCPUS*10
		DialTimeout  int // 单位ms，设置空默认5000ms
		ReadTimeout  int // 单位ms，设置空默认3000ms
		WriteTimeout int // 单位ms，设置空默认3000ms
	}
	FrequencyConfig struct {
		LoopInterval          int    // 单位s
		FrequencyKey          string //频控的Key
		FrequencyKeyCount     int    // 频控Key的数量
		ClkFrequencyKeyPrefix string
		ImpFrequencyKeyPrefix string
	}
	FunnelConfig struct {
		ConfigFilePath  string
		Interval        int
		FunnelOpenRatio int
	}
	BudgetConfig struct {
		SpeedStopRate        float64 // 速率停止阈值
		SmallBudgetThreshold int64   // 小预算阈值
		BudgetThresholdRatio float64 // 预算阈值比例
		MaxBidPerMinute      int32   // 每分钟最大出价次数
		DecelerationFactor   float64 // 减速因子
		DefaultMediaPrice    int64   // 默认媒体价格
		StatsReloadInterval  int64   // 统计数据重载间隔(秒)
		StatsFilePath        string  // 预算消耗统计文件路径（兼容旧配置）
		// 轮询监控配置
		StatsFileDir         string  // 统计文件目录
		StatsFileNamePattern string  // 统计文件名模板（不包含时间戳部分）
		PollingInterval      int64   // 轮询间隔(秒)，默认30秒
	}
)

type Config struct {
	Server             ServerConfig
	LogConfig          LogConfig
	FunnelLogConfig    LogConfig
	PredictServer      PredictServer
	ModelIndex         ModelIndexConfig
	DmpTargetRedisPool RedisPool
	FrequencyRedisPool RedisPool
	FrequencyConfig    FrequencyConfig
	FunnelConfig       FunnelConfig
	FunnelKafkaConfig  FunnelKafkaConfig
	BudgetConfig       BudgetConfig
}

var GlobalConfig Config

func ConfigInit(configPath string) (err error) {
	viper.SetConfigType("yaml")
	viper.SetConfigFile(configPath)

	// 设置默认值
	// viper.SetDefault("PredictServer.PoolSize", 10)
	// viper.SetDefault("PredictServer.MaxIdleConns", 5)
	// viper.SetDefault("PredictServer.ConnTimeout", 5000)

	// 设置预算配置默认值
	// viper.SetDefault("BudgetConfig.SpeedStopRate", 0.01)
	// viper.SetDefault("BudgetConfig.SmallBudgetThreshold", 10000)
	// viper.SetDefault("BudgetConfig.BudgetThresholdRatio", 0.2)
	// viper.SetDefault("BudgetConfig.MaxBidPerMinute", 100)
	// viper.SetDefault("BudgetConfig.DecelerationFactor", 3.0)
	// viper.SetDefault("BudgetConfig.DefaultMediaPrice", 100)
	// viper.SetDefault("BudgetConfig.StatsReloadInterval", 300)
	// viper.SetDefault("BudgetConfig.StatsFilePath", "data/budget/stats.data")

	if err = viper.ReadInConfig(); err != nil {
		return err
	}
	viper.Unmarshal(&GlobalConfig)
	return nil
}
