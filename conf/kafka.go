package conf

// KafkaConfig Kafka配置
type KafkaConfig struct {
	// Brokers Kafka服务器地址列表
	Brokers []string `yaml:"brokers"`
	// ClientID 客户端ID
	ClientID string `yaml:"client_id"`
	// RequiredAcks 需要的确认数
	// 0: 不需要确认
	// 1: 只需要leader确认
	// -1: 需要所有副本确认
	RequiredAcks int16 `yaml:"required_acks"`
	// Compression 压缩类型
	// none, gzip, snappy, lz4, zstd
	Compression string `yaml:"compression"`
	// FlushMessages 缓冲的消息数量达到此值时刷新
	FlushMessages int `yaml:"flush_messages"`
	// FlushMaxMessages 一次刷新的最大消息数量
	FlushMaxMessages int `yaml:"flush_max_messages"`
	// FlushFrequency 刷新频率(毫秒)
	FlushFrequency int `yaml:"flush_frequency"`
	// Timeout 超时时间(毫秒)
	Timeout int `yaml:"timeout"`
	// MaxRetries 最大重试次数
	MaxRetries int `yaml:"max_retries"`
	// RetryBackoff 重试间隔(毫秒)
	RetryBackoff int `yaml:"retry_backoff"`
	// SASL认证配置
	// EnableSASL 是否启用SASL认证
	EnableSASL bool `yaml:"enable_sasl"`
	// SASLMechanism SASL认证机制 (PLAIN, SCRAM-SHA-256, SCRAM-SHA-512)
	SASLMechanism string `yaml:"sasl_mechanism"`
	// SASLUsername SASL用户名
	SASLUsername string `yaml:"sasl_username"`
	// SASLPassword SASL密码
	SASLPassword string `yaml:"sasl_password"`
	// EnableTLS 是否启用TLS
	EnableTLS bool `yaml:"enable_tls"`
	// TLSSkipVerify 是否跳过TLS证书验证
	TLSSkipVerify bool `yaml:"tls_skip_verify"`
}

// FunnelKafkaConfig 漏斗数据Kafka配置
type FunnelKafkaConfig struct {
	// Enabled 是否启用Kafka发送
	Enabled bool `yaml:"enabled"`
	// Topic 主题
	Topic string `yaml:"topic"`
	// QueueSize 异步发送队列大小，0表示不限制
	QueueSize int `yaml:"queue_size"`
	// KafkaConfig Kafka配置
	KafkaConfig KafkaConfig `yaml:"kafka_config"`
}