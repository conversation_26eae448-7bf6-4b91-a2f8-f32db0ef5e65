// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: predict.proto

package tensorflow_serving

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DataType enum from tensorflow/core/framework/types.proto
type DataType int32

const (
	DataType_DT_INVALID DataType = 0
	DataType_DT_FLOAT   DataType = 1
	DataType_DT_DOUBLE  DataType = 2
	DataType_DT_INT32   DataType = 3
	DataType_DT_UINT8   DataType = 4
	DataType_DT_INT16   DataType = 5
	DataType_DT_INT8    DataType = 6
	DataType_DT_STRING  DataType = 7
	DataType_DT_INT64   DataType = 9
)

// Enum value maps for DataType.
var (
	DataType_name = map[int32]string{
		0: "DT_INVALID",
		1: "DT_FLOAT",
		2: "DT_DOUBLE",
		3: "DT_INT32",
		4: "DT_UINT8",
		5: "DT_INT16",
		6: "DT_INT8",
		7: "DT_STRING",
		9: "DT_INT64",
	}
	DataType_value = map[string]int32{
		"DT_INVALID": 0,
		"DT_FLOAT":   1,
		"DT_DOUBLE":  2,
		"DT_INT32":   3,
		"DT_UINT8":   4,
		"DT_INT16":   5,
		"DT_INT8":    6,
		"DT_STRING":  7,
		"DT_INT64":   9,
	}
)

func (x DataType) Enum() *DataType {
	p := new(DataType)
	*p = x
	return p
}

func (x DataType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataType) Descriptor() protoreflect.EnumDescriptor {
	return file_predict_proto_enumTypes[0].Descriptor()
}

func (DataType) Type() protoreflect.EnumType {
	return &file_predict_proto_enumTypes[0]
}

func (x DataType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataType.Descriptor instead.
func (DataType) EnumDescriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{0}
}

// Model specification from tensorflow_serving/apis/model.proto
type ModelSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	SignatureName string                 `protobuf:"bytes,2,opt,name=signature_name,json=signatureName,proto3" json:"signature_name,omitempty"`
	Version       int64                  `protobuf:"varint,3,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ModelSpec) Reset() {
	*x = ModelSpec{}
	mi := &file_predict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModelSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModelSpec) ProtoMessage() {}

func (x *ModelSpec) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModelSpec.ProtoReflect.Descriptor instead.
func (*ModelSpec) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{0}
}

func (x *ModelSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ModelSpec) GetSignatureName() string {
	if x != nil {
		return x.SignatureName
	}
	return ""
}

func (x *ModelSpec) GetVersion() int64 {
	if x != nil {
		return x.Version
	}
	return 0
}

// Tensor shape from tensorflow/core/framework/tensor_shape.proto
type TensorShapeProto struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Dim           []*TensorShapeProto_Dim `protobuf:"bytes,2,rep,name=dim,proto3" json:"dim,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TensorShapeProto) Reset() {
	*x = TensorShapeProto{}
	mi := &file_predict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TensorShapeProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto) ProtoMessage() {}

func (x *TensorShapeProto) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto.ProtoReflect.Descriptor instead.
func (*TensorShapeProto) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{1}
}

func (x *TensorShapeProto) GetDim() []*TensorShapeProto_Dim {
	if x != nil {
		return x.Dim
	}
	return nil
}

// Tensor from tensorflow/core/framework/tensor.proto
type TensorProto struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dtype         DataType               `protobuf:"varint,1,opt,name=dtype,proto3,enum=tensorflow.serving.DataType" json:"dtype,omitempty"`
	TensorShape   *TensorShapeProto      `protobuf:"bytes,2,opt,name=tensor_shape,json=tensorShape,proto3" json:"tensor_shape,omitempty"`
	FloatVal      []float32              `protobuf:"fixed32,5,rep,packed,name=float_val,json=floatVal,proto3" json:"float_val,omitempty"`
	DoubleVal     []float64              `protobuf:"fixed64,6,rep,packed,name=double_val,json=doubleVal,proto3" json:"double_val,omitempty"`
	Int64Val      []int64                `protobuf:"varint,7,rep,packed,name=int64_val,json=int64Val,proto3" json:"int64_val,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TensorProto) Reset() {
	*x = TensorProto{}
	mi := &file_predict_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TensorProto) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorProto) ProtoMessage() {}

func (x *TensorProto) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorProto.ProtoReflect.Descriptor instead.
func (*TensorProto) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{2}
}

func (x *TensorProto) GetDtype() DataType {
	if x != nil {
		return x.Dtype
	}
	return DataType_DT_INVALID
}

func (x *TensorProto) GetTensorShape() *TensorShapeProto {
	if x != nil {
		return x.TensorShape
	}
	return nil
}

func (x *TensorProto) GetFloatVal() []float32 {
	if x != nil {
		return x.FloatVal
	}
	return nil
}

func (x *TensorProto) GetDoubleVal() []float64 {
	if x != nil {
		return x.DoubleVal
	}
	return nil
}

func (x *TensorProto) GetInt64Val() []int64 {
	if x != nil {
		return x.Int64Val
	}
	return nil
}

// Predict request from tensorflow_serving/apis/predict.proto
type PredictRequest struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	ModelSpec     *ModelSpec              `protobuf:"bytes,1,opt,name=model_spec,json=modelSpec,proto3" json:"model_spec,omitempty"`
	Inputs        map[string]*TensorProto `protobuf:"bytes,2,rep,name=inputs,proto3" json:"inputs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PredictRequest) Reset() {
	*x = PredictRequest{}
	mi := &file_predict_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PredictRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictRequest) ProtoMessage() {}

func (x *PredictRequest) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictRequest.ProtoReflect.Descriptor instead.
func (*PredictRequest) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{3}
}

func (x *PredictRequest) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

func (x *PredictRequest) GetInputs() map[string]*TensorProto {
	if x != nil {
		return x.Inputs
	}
	return nil
}

// Predict response from tensorflow_serving/apis/predict.proto
type PredictResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	Outputs       map[string]*TensorProto `protobuf:"bytes,1,rep,name=outputs,proto3" json:"outputs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	ModelSpec     *ModelSpec              `protobuf:"bytes,2,opt,name=model_spec,json=modelSpec,proto3" json:"model_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PredictResponse) Reset() {
	*x = PredictResponse{}
	mi := &file_predict_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PredictResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PredictResponse) ProtoMessage() {}

func (x *PredictResponse) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PredictResponse.ProtoReflect.Descriptor instead.
func (*PredictResponse) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{4}
}

func (x *PredictResponse) GetOutputs() map[string]*TensorProto {
	if x != nil {
		return x.Outputs
	}
	return nil
}

func (x *PredictResponse) GetModelSpec() *ModelSpec {
	if x != nil {
		return x.ModelSpec
	}
	return nil
}

type TensorShapeProto_Dim struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Size          int64                  `protobuf:"varint,1,opt,name=size,proto3" json:"size,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TensorShapeProto_Dim) Reset() {
	*x = TensorShapeProto_Dim{}
	mi := &file_predict_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TensorShapeProto_Dim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TensorShapeProto_Dim) ProtoMessage() {}

func (x *TensorShapeProto_Dim) ProtoReflect() protoreflect.Message {
	mi := &file_predict_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TensorShapeProto_Dim.ProtoReflect.Descriptor instead.
func (*TensorShapeProto_Dim) Descriptor() ([]byte, []int) {
	return file_predict_proto_rawDescGZIP(), []int{1, 0}
}

func (x *TensorShapeProto_Dim) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *TensorShapeProto_Dim) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_predict_proto protoreflect.FileDescriptor

const file_predict_proto_rawDesc = "" +
	"\n" +
	"\rpredict.proto\x12\x12tensorflow.serving\"`\n" +
	"\tModelSpec\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12%\n" +
	"\x0esignature_name\x18\x02 \x01(\tR\rsignatureName\x12\x18\n" +
	"\aversion\x18\x03 \x01(\x03R\aversion\"}\n" +
	"\x10TensorShapeProto\x12:\n" +
	"\x03dim\x18\x02 \x03(\v2(.tensorflow.serving.TensorShapeProto.DimR\x03dim\x1a-\n" +
	"\x03Dim\x12\x12\n" +
	"\x04size\x18\x01 \x01(\x03R\x04size\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\xef\x01\n" +
	"\vTensorProto\x122\n" +
	"\x05dtype\x18\x01 \x01(\x0e2\x1c.tensorflow.serving.DataTypeR\x05dtype\x12G\n" +
	"\ftensor_shape\x18\x02 \x01(\v2$.tensorflow.serving.TensorShapeProtoR\vtensorShape\x12\x1f\n" +
	"\tfloat_val\x18\x05 \x03(\x02B\x02\x10\x01R\bfloatVal\x12!\n" +
	"\n" +
	"double_val\x18\x06 \x03(\x01B\x02\x10\x01R\tdoubleVal\x12\x1f\n" +
	"\tint64_val\x18\a \x03(\x03B\x02\x10\x01R\bint64Val\"\xf2\x01\n" +
	"\x0ePredictRequest\x12<\n" +
	"\n" +
	"model_spec\x18\x01 \x01(\v2\x1d.tensorflow.serving.ModelSpecR\tmodelSpec\x12F\n" +
	"\x06inputs\x18\x02 \x03(\v2..tensorflow.serving.PredictRequest.InputsEntryR\x06inputs\x1aZ\n" +
	"\vInputsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.tensorflow.serving.TensorProtoR\x05value:\x028\x01\"\xf8\x01\n" +
	"\x0fPredictResponse\x12J\n" +
	"\aoutputs\x18\x01 \x03(\v20.tensorflow.serving.PredictResponse.OutputsEntryR\aoutputs\x12<\n" +
	"\n" +
	"model_spec\x18\x02 \x01(\v2\x1d.tensorflow.serving.ModelSpecR\tmodelSpec\x1a[\n" +
	"\fOutputsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x125\n" +
	"\x05value\x18\x02 \x01(\v2\x1f.tensorflow.serving.TensorProtoR\x05value:\x028\x01*\x8b\x01\n" +
	"\bDataType\x12\x0e\n" +
	"\n" +
	"DT_INVALID\x10\x00\x12\f\n" +
	"\bDT_FLOAT\x10\x01\x12\r\n" +
	"\tDT_DOUBLE\x10\x02\x12\f\n" +
	"\bDT_INT32\x10\x03\x12\f\n" +
	"\bDT_UINT8\x10\x04\x12\f\n" +
	"\bDT_INT16\x10\x05\x12\v\n" +
	"\aDT_INT8\x10\x06\x12\r\n" +
	"\tDT_STRING\x10\a\x12\f\n" +
	"\bDT_INT64\x10\t2g\n" +
	"\x11PredictionService\x12R\n" +
	"\aPredict\x12\".tensorflow.serving.PredictRequest\x1a#.tensorflow.serving.PredictResponseB\fZ\n" +
	"./;predictb\x06proto3"

var (
	file_predict_proto_rawDescOnce sync.Once
	file_predict_proto_rawDescData []byte
)

func file_predict_proto_rawDescGZIP() []byte {
	file_predict_proto_rawDescOnce.Do(func() {
		file_predict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_predict_proto_rawDesc), len(file_predict_proto_rawDesc)))
	})
	return file_predict_proto_rawDescData
}

var file_predict_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_predict_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_predict_proto_goTypes = []any{
	(DataType)(0),                // 0: tensorflow.serving.DataType
	(*ModelSpec)(nil),            // 1: tensorflow.serving.ModelSpec
	(*TensorShapeProto)(nil),     // 2: tensorflow.serving.TensorShapeProto
	(*TensorProto)(nil),          // 3: tensorflow.serving.TensorProto
	(*PredictRequest)(nil),       // 4: tensorflow.serving.PredictRequest
	(*PredictResponse)(nil),      // 5: tensorflow.serving.PredictResponse
	(*TensorShapeProto_Dim)(nil), // 6: tensorflow.serving.TensorShapeProto.Dim
	nil,                          // 7: tensorflow.serving.PredictRequest.InputsEntry
	nil,                          // 8: tensorflow.serving.PredictResponse.OutputsEntry
}
var file_predict_proto_depIdxs = []int32{
	6,  // 0: tensorflow.serving.TensorShapeProto.dim:type_name -> tensorflow.serving.TensorShapeProto.Dim
	0,  // 1: tensorflow.serving.TensorProto.dtype:type_name -> tensorflow.serving.DataType
	2,  // 2: tensorflow.serving.TensorProto.tensor_shape:type_name -> tensorflow.serving.TensorShapeProto
	1,  // 3: tensorflow.serving.PredictRequest.model_spec:type_name -> tensorflow.serving.ModelSpec
	7,  // 4: tensorflow.serving.PredictRequest.inputs:type_name -> tensorflow.serving.PredictRequest.InputsEntry
	8,  // 5: tensorflow.serving.PredictResponse.outputs:type_name -> tensorflow.serving.PredictResponse.OutputsEntry
	1,  // 6: tensorflow.serving.PredictResponse.model_spec:type_name -> tensorflow.serving.ModelSpec
	3,  // 7: tensorflow.serving.PredictRequest.InputsEntry.value:type_name -> tensorflow.serving.TensorProto
	3,  // 8: tensorflow.serving.PredictResponse.OutputsEntry.value:type_name -> tensorflow.serving.TensorProto
	4,  // 9: tensorflow.serving.PredictionService.Predict:input_type -> tensorflow.serving.PredictRequest
	5,  // 10: tensorflow.serving.PredictionService.Predict:output_type -> tensorflow.serving.PredictResponse
	10, // [10:11] is the sub-list for method output_type
	9,  // [9:10] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_predict_proto_init() }
func file_predict_proto_init() {
	if File_predict_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_predict_proto_rawDesc), len(file_predict_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_predict_proto_goTypes,
		DependencyIndexes: file_predict_proto_depIdxs,
		EnumInfos:         file_predict_proto_enumTypes,
		MessageInfos:      file_predict_proto_msgTypes,
	}.Build()
	File_predict_proto = out.File
	file_predict_proto_goTypes = nil
	file_predict_proto_depIdxs = nil
}
