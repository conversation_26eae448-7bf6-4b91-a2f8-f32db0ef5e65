syntax = "proto3";

package tensorflow.serving;

option go_package = "rtb_model_server/proto/tensorflow_serving;tensorflow_serving";

// DataType enum from tensorflow/core/framework/types.proto
enum DataType {
  DT_INVALID = 0;
  DT_FLOAT = 1;
  DT_DOUBLE = 2;
  DT_INT32 = 3;
  DT_UINT8 = 4;
  DT_INT16 = 5;
  DT_INT8 = 6;
  DT_STRING = 7;
  DT_INT64 = 9;
}

// Model specification from tensorflow_serving/apis/model.proto
message ModelSpec {
  string name = 1;
  string signature_name = 2;
  int64 version = 3;
}

// Tensor shape from tensorflow/core/framework/tensor_shape.proto
message TensorShapeProto {
  message Dim {
    int64 size = 1;
    string name = 2;
  }
  repeated Dim dim = 2;
}

// Tensor from tensorflow/core/framework/tensor.proto
message TensorProto {
  DataType dtype = 1;
  TensorShapeProto tensor_shape = 2;
  repeated float float_val = 5 [packed = true];
  repeated double double_val = 6 [packed = true];
  repeated int64 int64_val = 7 [packed = true];
}

// Predict request from tensorflow_serving/apis/predict.proto
message PredictRequest {
  ModelSpec model_spec = 1;
  map<string, TensorProto> inputs = 2;
}

// Predict response from tensorflow_serving/apis/predict.proto
message PredictResponse {
  map<string, TensorProto> outputs = 1;
  ModelSpec model_spec = 2;
}

// PredictionService from tensorflow_serving/apis/prediction_service.proto
service PredictionService {
  rpc Predict(PredictRequest) returns (PredictResponse);
}
