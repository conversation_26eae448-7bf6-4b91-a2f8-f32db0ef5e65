# Proto 文件目录

本目录包含项目中使用的所有 Protocol Buffers 定义文件和生成的 Go 代码。

## 目录结构

```
proto/
├── tensorflow_serving/     # TensorFlow Serving 相关的 proto 文件
│   ├── predict.proto      # TensorFlow Serving 预测服务的 proto 定义
│   ├── predict.pb.go      # 生成的 Go 结构体代码
│   └── predict_grpc.pb.go # 生成的 gRPC 客户端/服务端代码
└── README.md              # 本文件
```

## TensorFlow Serving

`tensorflow_serving/` 目录包含与 TensorFlow Serving 通信所需的 proto 定义：

- **predict.proto**: 定义了 TensorFlow Serving 的预测服务接口
- **predict.pb.go**: 包含数据结构定义（ModelSpec, TensorProto, PredictRequest, PredictResponse 等）
- **predict_grpc.pb.go**: 包含 gRPC 客户端和服务端接口

## 使用方式

在 Go 代码中引用这些 proto 生成的类型：

```go
import "rtb_model_server/proto/tensorflow_serving"

// 使用示例
request := &tensorflow_serving.PredictRequest{
    ModelSpec: &tensorflow_serving.ModelSpec{
        Name: "my_model",
        SignatureName: "serving_default",
    },
    Inputs: make(map[string]*tensorflow_serving.TensorProto),
}
```

## 重新生成代码

如果修改了 proto 文件，需要重新生成 Go 代码：

```bash
cd proto/tensorflow_serving
protoc --go_out=. --go-grpc_out=. predict.proto
```

**注意**: 需要安装 protoc 编译器和相关的 Go 插件：
- `protoc` (Protocol Buffers 编译器)
- `protoc-gen-go` (Go 代码生成插件)
- `protoc-gen-go-grpc` (Go gRPC 代码生成插件)

## 相关文件

- `internal/model_index/predict/tfserving_client.go`: 使用这些 proto 定义的 TensorFlow Serving 客户端实现
