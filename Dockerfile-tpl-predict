FROM golang:1.23 as build

ENV GOPROXY http://goproxy.cn
ENV GO111MODULE on
ENV GOPRIVATE git.domob-inc.cn

WORKDIR /go/cache
ADD go.mod .
ADD go.sum .
RUN go mod download

WORKDIR /go/release

ADD . .
RUN GOOS=linux CGO_ENABLED=0 go build -ldflags="-s -w -X main.__version__=__VERSION__"  -installsuffix cgo -o rtb_model_server cmd/main.go
FROM  centos as prod

RUN mkdir -p /application/bin/

COPY --from=build /go/release/rtb_model_server /application/bin/rtb_model_server

WORKDIR /application/

CMD ["/application/bin/rtb_model_server", "-predict"]



