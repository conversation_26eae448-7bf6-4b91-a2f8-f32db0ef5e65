// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v3.20.3
// source: funnel.proto

package funnel

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FunnelData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FunnelId  int32  `protobuf:"varint,1,opt,name=funnel_id,json=funnelId,proto3" json:"funnel_id,omitempty"`
	Date      int32  `protobuf:"varint,2,opt,name=date,proto3" json:"date,omitempty"`
	Hour      int32  `protobuf:"varint,3,opt,name=hour,proto3" json:"hour,omitempty"`
	RequestId string `protobuf:"bytes,4,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	SearchId  int64  `protobuf:"varint,5,opt,name=search_id,json=searchId,proto3" json:"search_id,omitempty"`
	UiName    string `protobuf:"bytes,6,opt,name=ui_name,json=uiName,proto3" json:"ui_name,omitempty"`
	// 基础字段
	ExchangeId    int32  `protobuf:"varint,7,opt,name=exchange_id,json=exchangeId,proto3" json:"exchange_id,omitempty"`
	AdxExchangeId int32  `protobuf:"varint,8,opt,name=adx_exchange_id,json=adxExchangeId,proto3" json:"adx_exchange_id,omitempty"`
	DmMediaId     int32  `protobuf:"varint,9,opt,name=dm_media_id,json=dmMediaId,proto3" json:"dm_media_id,omitempty"`
	AppBundle     string `protobuf:"bytes,10,opt,name=app_bundle,json=appBundle,proto3" json:"app_bundle,omitempty"`
	// 设备信息
	Idfa     string `protobuf:"bytes,11,opt,name=idfa,proto3" json:"idfa,omitempty"`
	Idfamd5  string `protobuf:"bytes,12,opt,name=idfamd5,proto3" json:"idfamd5,omitempty"`
	Oaidmd5  string `protobuf:"bytes,13,opt,name=oaidmd5,proto3" json:"oaidmd5,omitempty"`
	Gaid     string `protobuf:"bytes,14,opt,name=gaid,proto3" json:"gaid,omitempty"`
	Platform int32  `protobuf:"varint,15,opt,name=platform,proto3" json:"platform,omitempty"`
	Os       int32  `protobuf:"varint,16,opt,name=os,proto3" json:"os,omitempty"`
	Access   int32  `protobuf:"varint,17,opt,name=access,proto3" json:"access,omitempty"`
	City     int32  `protobuf:"varint,18,opt,name=city,proto3" json:"city,omitempty"`
	Carrier  int32  `protobuf:"varint,19,opt,name=carrier,proto3" json:"carrier,omitempty"`
	// 嵌套结构
	Timing      *FunnelTiming   `protobuf:"bytes,20,opt,name=timing,proto3" json:"timing,omitempty"`
	Request     *FunnelRequest  `protobuf:"bytes,21,opt,name=request,proto3" json:"request,omitempty"`
	Response    *FunnelResponse `protobuf:"bytes,22,opt,name=response,proto3" json:"response,omitempty"`
	RuntimeData *RuntimeData    `protobuf:"bytes,23,opt,name=runtime_data,json=runtimeData,proto3" json:"runtime_data,omitempty"`
}

func (x *FunnelData) Reset() {
	*x = FunnelData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelData) ProtoMessage() {}

func (x *FunnelData) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelData.ProtoReflect.Descriptor instead.
func (*FunnelData) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{0}
}

func (x *FunnelData) GetFunnelId() int32 {
	if x != nil {
		return x.FunnelId
	}
	return 0
}

func (x *FunnelData) GetDate() int32 {
	if x != nil {
		return x.Date
	}
	return 0
}

func (x *FunnelData) GetHour() int32 {
	if x != nil {
		return x.Hour
	}
	return 0
}

func (x *FunnelData) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *FunnelData) GetSearchId() int64 {
	if x != nil {
		return x.SearchId
	}
	return 0
}

func (x *FunnelData) GetUiName() string {
	if x != nil {
		return x.UiName
	}
	return ""
}

func (x *FunnelData) GetExchangeId() int32 {
	if x != nil {
		return x.ExchangeId
	}
	return 0
}

func (x *FunnelData) GetAdxExchangeId() int32 {
	if x != nil {
		return x.AdxExchangeId
	}
	return 0
}

func (x *FunnelData) GetDmMediaId() int32 {
	if x != nil {
		return x.DmMediaId
	}
	return 0
}

func (x *FunnelData) GetAppBundle() string {
	if x != nil {
		return x.AppBundle
	}
	return ""
}

func (x *FunnelData) GetIdfa() string {
	if x != nil {
		return x.Idfa
	}
	return ""
}

func (x *FunnelData) GetIdfamd5() string {
	if x != nil {
		return x.Idfamd5
	}
	return ""
}

func (x *FunnelData) GetOaidmd5() string {
	if x != nil {
		return x.Oaidmd5
	}
	return ""
}

func (x *FunnelData) GetGaid() string {
	if x != nil {
		return x.Gaid
	}
	return ""
}

func (x *FunnelData) GetPlatform() int32 {
	if x != nil {
		return x.Platform
	}
	return 0
}

func (x *FunnelData) GetOs() int32 {
	if x != nil {
		return x.Os
	}
	return 0
}

func (x *FunnelData) GetAccess() int32 {
	if x != nil {
		return x.Access
	}
	return 0
}

func (x *FunnelData) GetCity() int32 {
	if x != nil {
		return x.City
	}
	return 0
}

func (x *FunnelData) GetCarrier() int32 {
	if x != nil {
		return x.Carrier
	}
	return 0
}

func (x *FunnelData) GetTiming() *FunnelTiming {
	if x != nil {
		return x.Timing
	}
	return nil
}

func (x *FunnelData) GetRequest() *FunnelRequest {
	if x != nil {
		return x.Request
	}
	return nil
}

func (x *FunnelData) GetResponse() *FunnelResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *FunnelData) GetRuntimeData() *RuntimeData {
	if x != nil {
		return x.RuntimeData
	}
	return nil
}

type FunnelTiming struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total         int32 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	UserProfile   int32 `protobuf:"varint,2,opt,name=user_profile,json=userProfile,proto3" json:"user_profile,omitempty"`
	Filter        int32 `protobuf:"varint,3,opt,name=filter,proto3" json:"filter,omitempty"`
	Dmp           int32 `protobuf:"varint,4,opt,name=dmp,proto3" json:"dmp,omitempty"`
	ClkFrequency  int32 `protobuf:"varint,5,opt,name=clk_frequency,json=clkFrequency,proto3" json:"clk_frequency,omitempty"`
	ImpFrequency  int32 `protobuf:"varint,6,opt,name=imp_frequency,json=impFrequency,proto3" json:"imp_frequency,omitempty"`
	PredictServer int32 `protobuf:"varint,7,opt,name=predict_server,json=predictServer,proto3" json:"predict_server,omitempty"`
	Sort          int32 `protobuf:"varint,8,opt,name=sort,proto3" json:"sort,omitempty"`
	Render        int32 `protobuf:"varint,9,opt,name=render,proto3" json:"render,omitempty"`
}

func (x *FunnelTiming) Reset() {
	*x = FunnelTiming{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelTiming) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelTiming) ProtoMessage() {}

func (x *FunnelTiming) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelTiming.ProtoReflect.Descriptor instead.
func (*FunnelTiming) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{1}
}

func (x *FunnelTiming) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *FunnelTiming) GetUserProfile() int32 {
	if x != nil {
		return x.UserProfile
	}
	return 0
}

func (x *FunnelTiming) GetFilter() int32 {
	if x != nil {
		return x.Filter
	}
	return 0
}

func (x *FunnelTiming) GetDmp() int32 {
	if x != nil {
		return x.Dmp
	}
	return 0
}

func (x *FunnelTiming) GetClkFrequency() int32 {
	if x != nil {
		return x.ClkFrequency
	}
	return 0
}

func (x *FunnelTiming) GetImpFrequency() int32 {
	if x != nil {
		return x.ImpFrequency
	}
	return 0
}

func (x *FunnelTiming) GetPredictServer() int32 {
	if x != nil {
		return x.PredictServer
	}
	return 0
}

func (x *FunnelTiming) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FunnelTiming) GetRender() int32 {
	if x != nil {
		return x.Render
	}
	return 0
}

type RuntimeData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CreativeFilterMap       map[int32]int32  `protobuf:"bytes,1,rep,name=creative_filter_map,json=creativeFilterMap,proto3" json:"creative_filter_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	RecallCreatives         []int32          `protobuf:"varint,2,rep,packed,name=recall_creatives,json=recallCreatives,proto3" json:"recall_creatives,omitempty"`
	DeviceDmpTarget         []int32          `protobuf:"varint,3,rep,packed,name=device_dmp_target,json=deviceDmpTarget,proto3" json:"device_dmp_target,omitempty"`
	ResourceTargetWhiteList []int32          `protobuf:"varint,4,rep,packed,name=resource_target_white_list,json=resourceTargetWhiteList,proto3" json:"resource_target_white_list,omitempty"`
	ResourceTargetBlackList []int32          `protobuf:"varint,5,rep,packed,name=resource_target_black_list,json=resourceTargetBlackList,proto3" json:"resource_target_black_list,omitempty"`
	ClkFrequencyData        map[string]int32 `protobuf:"bytes,6,rep,name=clk_frequency_data,json=clkFrequencyData,proto3" json:"clk_frequency_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	ImpFrequencyData        map[string]int32 `protobuf:"bytes,7,rep,name=imp_frequency_data,json=impFrequencyData,proto3" json:"imp_frequency_data,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *RuntimeData) Reset() {
	*x = RuntimeData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RuntimeData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RuntimeData) ProtoMessage() {}

func (x *RuntimeData) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RuntimeData.ProtoReflect.Descriptor instead.
func (*RuntimeData) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{2}
}

func (x *RuntimeData) GetCreativeFilterMap() map[int32]int32 {
	if x != nil {
		return x.CreativeFilterMap
	}
	return nil
}

func (x *RuntimeData) GetRecallCreatives() []int32 {
	if x != nil {
		return x.RecallCreatives
	}
	return nil
}

func (x *RuntimeData) GetDeviceDmpTarget() []int32 {
	if x != nil {
		return x.DeviceDmpTarget
	}
	return nil
}

func (x *RuntimeData) GetResourceTargetWhiteList() []int32 {
	if x != nil {
		return x.ResourceTargetWhiteList
	}
	return nil
}

func (x *RuntimeData) GetResourceTargetBlackList() []int32 {
	if x != nil {
		return x.ResourceTargetBlackList
	}
	return nil
}

func (x *RuntimeData) GetClkFrequencyData() map[string]int32 {
	if x != nil {
		return x.ClkFrequencyData
	}
	return nil
}

func (x *RuntimeData) GetImpFrequencyData() map[string]int32 {
	if x != nil {
		return x.ImpFrequencyData
	}
	return nil
}

// 请求信息
type FunnelRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ReqAds []*FunnelRequestAd `protobuf:"bytes,1,rep,name=req_ads,json=reqAds,proto3" json:"req_ads,omitempty"` // 请求广告列表
}

func (x *FunnelRequest) Reset() {
	*x = FunnelRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelRequest) ProtoMessage() {}

func (x *FunnelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelRequest.ProtoReflect.Descriptor instead.
func (*FunnelRequest) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{3}
}

func (x *FunnelRequest) GetReqAds() []*FunnelRequestAd {
	if x != nil {
		return x.ReqAds
	}
	return nil
}

// 请求广告信息
type FunnelRequestAd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idx          int32   `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty"`                                             // 广告位索引
	ImpId        string  `protobuf:"bytes,2,opt,name=imp_id,json=impId,proto3" json:"imp_id,omitempty"`                             // 曝光ID
	AdMatchType  []int64 `protobuf:"varint,3,rep,packed,name=ad_match_type,json=adMatchType,proto3" json:"ad_match_type,omitempty"` // 广告素材ID列表
	BidFloor     int64   `protobuf:"varint,4,opt,name=bid_floor,json=bidFloor,proto3" json:"bid_floor,omitempty"`                   // 竞价标识
	CpcBidFloor  int64   `protobuf:"varint,5,opt,name=cpc_bid_floor,json=cpcBidFloor,proto3" json:"cpc_bid_floor,omitempty"`        // CPC竞价标识
	MediaBidType int32   `protobuf:"varint,6,opt,name=media_bid_type,json=mediaBidType,proto3" json:"media_bid_type,omitempty"`     // 媒体竞价类型
	DealFloor    int64   `protobuf:"varint,7,opt,name=deal_floor,json=dealFloor,proto3" json:"deal_floor,omitempty"`                // 交易标识
	DealId       string  `protobuf:"bytes,8,opt,name=deal_id,json=dealId,proto3" json:"deal_id,omitempty"`                          // 交易ID
}

func (x *FunnelRequestAd) Reset() {
	*x = FunnelRequestAd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelRequestAd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelRequestAd) ProtoMessage() {}

func (x *FunnelRequestAd) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelRequestAd.ProtoReflect.Descriptor instead.
func (*FunnelRequestAd) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{4}
}

func (x *FunnelRequestAd) GetIdx() int32 {
	if x != nil {
		return x.Idx
	}
	return 0
}

func (x *FunnelRequestAd) GetImpId() string {
	if x != nil {
		return x.ImpId
	}
	return ""
}

func (x *FunnelRequestAd) GetAdMatchType() []int64 {
	if x != nil {
		return x.AdMatchType
	}
	return nil
}

func (x *FunnelRequestAd) GetBidFloor() int64 {
	if x != nil {
		return x.BidFloor
	}
	return 0
}

func (x *FunnelRequestAd) GetCpcBidFloor() int64 {
	if x != nil {
		return x.CpcBidFloor
	}
	return 0
}

func (x *FunnelRequestAd) GetMediaBidType() int32 {
	if x != nil {
		return x.MediaBidType
	}
	return 0
}

func (x *FunnelRequestAd) GetDealFloor() int64 {
	if x != nil {
		return x.DealFloor
	}
	return 0
}

func (x *FunnelRequestAd) GetDealId() string {
	if x != nil {
		return x.DealId
	}
	return ""
}

// 响应信息
type FunnelResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RespAds []*FunnelResponseAd `protobuf:"bytes,1,rep,name=resp_ads,json=respAds,proto3" json:"resp_ads,omitempty"` // 响应广告列表
}

func (x *FunnelResponse) Reset() {
	*x = FunnelResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelResponse) ProtoMessage() {}

func (x *FunnelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelResponse.ProtoReflect.Descriptor instead.
func (*FunnelResponse) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{5}
}

func (x *FunnelResponse) GetRespAds() []*FunnelResponseAd {
	if x != nil {
		return x.RespAds
	}
	return nil
}

// 响应广告信息
type FunnelResponseAd struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Idx   int32                 `protobuf:"varint,1,opt,name=idx,proto3" json:"idx,omitempty"`                  // 广告位索引
	AdNum int32                 `protobuf:"varint,2,opt,name=ad_num,json=adNum,proto3" json:"ad_num,omitempty"` // 广告数量
	ImpId int32                 `protobuf:"varint,3,opt,name=imp_id,json=impId,proto3" json:"imp_id,omitempty"` // 曝光ID
	Ads   *FunnelResponseAdInfo `protobuf:"bytes,4,opt,name=ads,proto3" json:"ads,omitempty"`                   // 广告详情
}

func (x *FunnelResponseAd) Reset() {
	*x = FunnelResponseAd{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelResponseAd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelResponseAd) ProtoMessage() {}

func (x *FunnelResponseAd) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelResponseAd.ProtoReflect.Descriptor instead.
func (*FunnelResponseAd) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{6}
}

func (x *FunnelResponseAd) GetIdx() int32 {
	if x != nil {
		return x.Idx
	}
	return 0
}

func (x *FunnelResponseAd) GetAdNum() int32 {
	if x != nil {
		return x.AdNum
	}
	return 0
}

func (x *FunnelResponseAd) GetImpId() int32 {
	if x != nil {
		return x.ImpId
	}
	return 0
}

func (x *FunnelResponseAd) GetAds() *FunnelResponseAdInfo {
	if x != nil {
		return x.Ads
	}
	return nil
}

// 响应广告详细信息
type FunnelResponseAdInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index    int32 `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`                        // idx
	Cid      int32 `protobuf:"varint,2,opt,name=cid,proto3" json:"cid,omitempty"`                            // 创意ID
	Sid      int32 `protobuf:"varint,3,opt,name=sid,proto3" json:"sid,omitempty"`                            // 策略ID
	Pid      int32 `protobuf:"varint,4,opt,name=pid,proto3" json:"pid,omitempty"`                            // 计划ID
	Uid      int32 `protobuf:"varint,5,opt,name=uid,proto3" json:"uid,omitempty"`                            // 用户ID
	Bid      int64 `protobuf:"varint,6,opt,name=bid,proto3" json:"bid,omitempty"`                            // 出价
	Ctr      int64 `protobuf:"varint,7,opt,name=ctr,proto3" json:"ctr,omitempty"`                            // 点击率
	Funneld  int32 `protobuf:"varint,8,opt,name=funneld,proto3" json:"funneld,omitempty"`                    // 漏斗配置ID
	Atr      int64 `protobuf:"varint,9,opt,name=atr,proto3" json:"atr,omitempty"`                            // 转化率
	Adm      int64 `protobuf:"varint,10,opt,name=adm,proto3" json:"adm,omitempty"`                           // 广告素材ID
	BidType  int32 `protobuf:"varint,11,opt,name=bid_type,json=bidType,proto3" json:"bid_type,omitempty"`    // 出价类型
	FinPrice int64 `protobuf:"varint,12,opt,name=fin_price,json=finPrice,proto3" json:"fin_price,omitempty"` // 最终价格
}

func (x *FunnelResponseAdInfo) Reset() {
	*x = FunnelResponseAdInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_funnel_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FunnelResponseAdInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FunnelResponseAdInfo) ProtoMessage() {}

func (x *FunnelResponseAdInfo) ProtoReflect() protoreflect.Message {
	mi := &file_funnel_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FunnelResponseAdInfo.ProtoReflect.Descriptor instead.
func (*FunnelResponseAdInfo) Descriptor() ([]byte, []int) {
	return file_funnel_proto_rawDescGZIP(), []int{7}
}

func (x *FunnelResponseAdInfo) GetIndex() int32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetCid() int32 {
	if x != nil {
		return x.Cid
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetSid() int32 {
	if x != nil {
		return x.Sid
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetUid() int32 {
	if x != nil {
		return x.Uid
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetBid() int64 {
	if x != nil {
		return x.Bid
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetCtr() int64 {
	if x != nil {
		return x.Ctr
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetFunneld() int32 {
	if x != nil {
		return x.Funneld
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetAtr() int64 {
	if x != nil {
		return x.Atr
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetAdm() int64 {
	if x != nil {
		return x.Adm
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetBidType() int32 {
	if x != nil {
		return x.BidType
	}
	return 0
}

func (x *FunnelResponseAdInfo) GetFinPrice() int64 {
	if x != nil {
		return x.FinPrice
	}
	return 0
}

var File_funnel_proto protoreflect.FileDescriptor

var file_funnel_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06,
	0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x22, 0xc7, 0x05, 0x0a, 0x0a, 0x46, 0x75, 0x6e, 0x6e, 0x65,
	0x6c, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x68, 0x6f, 0x75, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x73, 0x65,
	0x61, 0x72, 0x63, 0x68, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x69, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x69, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64,
	0x12, 0x26, 0x0a, 0x0f, 0x61, 0x64, 0x78, 0x5f, 0x65, 0x78, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x61, 0x64, 0x78, 0x45, 0x78,
	0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0b, 0x64, 0x6d, 0x5f, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x64,
	0x6d, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x49, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x70, 0x70, 0x5f,
	0x62, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x70,
	0x70, 0x42, 0x75, 0x6e, 0x64, 0x6c, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x64, 0x66, 0x61, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x64, 0x66, 0x61, 0x12, 0x18, 0x0a, 0x07, 0x69,
	0x64, 0x66, 0x61, 0x6d, 0x64, 0x35, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64,
	0x66, 0x61, 0x6d, 0x64, 0x35, 0x12, 0x18, 0x0a, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x6d, 0x64, 0x35,
	0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x61, 0x69, 0x64, 0x6d, 0x64, 0x35, 0x12,
	0x12, 0x0a, 0x04, 0x67, 0x61, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x67,
	0x61, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x6c, 0x61, 0x74, 0x66, 0x6f, 0x72, 0x6d, 0x12,
	0x0e, 0x0a, 0x02, 0x6f, 0x73, 0x18, 0x10, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x6f, 0x73, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x63,
	0x61, 0x72, 0x72, 0x69, 0x65, 0x72, 0x18, 0x13, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x63, 0x61,
	0x72, 0x72, 0x69, 0x65, 0x72, 0x12, 0x2c, 0x0a, 0x06, 0x74, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x18,
	0x14, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x46,
	0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x69, 0x6d, 0x69, 0x6e, 0x67, 0x52, 0x06, 0x74, 0x69, 0x6d,
	0x69, 0x6e, 0x67, 0x12, 0x2f, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x15,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x46, 0x75,
	0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x52, 0x07, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x18, 0x16, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e,
	0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x52, 0x08,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x0c, 0x72, 0x75, 0x6e, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0b, 0x72, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61,
	0x22, 0x8e, 0x02, 0x0a, 0x0c, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x54, 0x69, 0x6d, 0x69, 0x6e,
	0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x5f,
	0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x75,
	0x73, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6d, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x64, 0x6d, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x63, 0x6c, 0x6b, 0x5f, 0x66, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x63, 0x6c, 0x6b,
	0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6d, 0x70,
	0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x0c, 0x69, 0x6d, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x12, 0x25,
	0x0a, 0x0e, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x70, 0x72, 0x65, 0x64, 0x69, 0x63, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x72, 0x65, 0x6e, 0x64, 0x65,
	0x72, 0x22, 0xbc, 0x05, 0x0a, 0x0b, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x12, 0x5a, 0x0a, 0x13, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x5f, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a,
	0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x44,
	0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x4d, 0x61, 0x70, 0x12, 0x29, 0x0a,
	0x10, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x61, 0x6c, 0x6c, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x65, 0x76, 0x69,
	0x63, 0x65, 0x5f, 0x64, 0x6d, 0x70, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x05, 0x52, 0x0f, 0x64, 0x65, 0x76, 0x69, 0x63, 0x65, 0x44, 0x6d, 0x70, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x77, 0x68, 0x69, 0x74, 0x65, 0x5f, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x04, 0x20, 0x03, 0x28, 0x05, 0x52, 0x17, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x57, 0x68, 0x69, 0x74, 0x65, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x3b, 0x0a, 0x1a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x62, 0x6c, 0x61, 0x63, 0x6b, 0x5f, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x05, 0x20, 0x03, 0x28, 0x05, 0x52, 0x17, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x42, 0x6c, 0x61, 0x63, 0x6b, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x57,
	0x0a, 0x12, 0x63, 0x6c, 0x6b, 0x5f, 0x66, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x75, 0x6e,
	0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6e, 0x74, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e,
	0x43, 0x6c, 0x6b, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10, 0x63, 0x6c, 0x6b, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x12, 0x69, 0x6d, 0x70, 0x5f, 0x66,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x07, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x52, 0x75, 0x6e,
	0x74, 0x69, 0x6d, 0x65, 0x44, 0x61, 0x74, 0x61, 0x2e, 0x49, 0x6d, 0x70, 0x46, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x10,
	0x69, 0x6d, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x61,
	0x1a, 0x44, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x69, 0x76, 0x65, 0x46, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x43, 0x0a, 0x15, 0x43, 0x6c, 0x6b, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x43, 0x0a, 0x15, 0x49,
	0x6d, 0x70, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x41, 0x0a, 0x0d, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x07, 0x72, 0x65, 0x71, 0x5f, 0x61, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x46, 0x75, 0x6e, 0x6e,
	0x65, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x52, 0x06, 0x72, 0x65, 0x71,
	0x41, 0x64, 0x73, 0x22, 0xfd, 0x01, 0x0a, 0x0f, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x41, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x78, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x69, 0x64, 0x78, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64,
	0x12, 0x22, 0x0a, 0x0d, 0x61, 0x64, 0x5f, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x64, 0x4d, 0x61, 0x74, 0x63, 0x68,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f, 0x6f,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x62, 0x69, 0x64, 0x46, 0x6c, 0x6f, 0x6f,
	0x72, 0x12, 0x22, 0x0a, 0x0d, 0x63, 0x70, 0x63, 0x5f, 0x62, 0x69, 0x64, 0x5f, 0x66, 0x6c, 0x6f,
	0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x70, 0x63, 0x42, 0x69, 0x64,
	0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x24, 0x0a, 0x0e, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x62,
	0x69, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x6d,
	0x65, 0x64, 0x69, 0x61, 0x42, 0x69, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x64,
	0x65, 0x61, 0x6c, 0x5f, 0x66, 0x6c, 0x6f, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x09, 0x64, 0x65, 0x61, 0x6c, 0x46, 0x6c, 0x6f, 0x6f, 0x72, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x65,
	0x61, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x61,
	0x6c, 0x49, 0x64, 0x22, 0x45, 0x0a, 0x0e, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x33, 0x0a, 0x08, 0x72, 0x65, 0x73, 0x70, 0x5f, 0x61, 0x64,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c,
	0x2e, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x41,
	0x64, 0x52, 0x07, 0x72, 0x65, 0x73, 0x70, 0x41, 0x64, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x10, 0x46,
	0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x41, 0x64, 0x12,
	0x10, 0x0a, 0x03, 0x69, 0x64, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x69, 0x64,
	0x78, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x64, 0x5f, 0x6e, 0x75, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x61, 0x64, 0x4e, 0x75, 0x6d, 0x12, 0x15, 0x0a, 0x06, 0x69, 0x6d, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6d, 0x70, 0x49, 0x64, 0x12,
	0x2e, 0x0a, 0x03, 0x61, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x66,
	0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x2e, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x61, 0x64, 0x73, 0x22,
	0x8e, 0x02, 0x0a, 0x14, 0x46, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x41, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x10,
	0x0a, 0x03, 0x63, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x63, 0x69, 0x64,
	0x12, 0x10, 0x0a, 0x03, 0x73, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x73,
	0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x70, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x03, 0x70, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x62, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x62, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x74, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x63, 0x74, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x66, 0x75,
	0x6e, 0x6e, 0x65, 0x6c, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x66, 0x75, 0x6e,
	0x6e, 0x65, 0x6c, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x74, 0x72, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x61, 0x74, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x61, 0x64, 0x6d, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x03, 0x61, 0x64, 0x6d, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x69, 0x64, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x69, 0x64, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x69, 0x6e, 0x5f, 0x70, 0x72, 0x69, 0x63, 0x65,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x66, 0x69, 0x6e, 0x50, 0x72, 0x69, 0x63, 0x65,
	0x42, 0x0b, 0x5a, 0x09, 0x2e, 0x2f, 0x3b, 0x66, 0x75, 0x6e, 0x6e, 0x65, 0x6c, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_funnel_proto_rawDescOnce sync.Once
	file_funnel_proto_rawDescData = file_funnel_proto_rawDesc
)

func file_funnel_proto_rawDescGZIP() []byte {
	file_funnel_proto_rawDescOnce.Do(func() {
		file_funnel_proto_rawDescData = protoimpl.X.CompressGZIP(file_funnel_proto_rawDescData)
	})
	return file_funnel_proto_rawDescData
}

var file_funnel_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_funnel_proto_goTypes = []interface{}{
	(*FunnelData)(nil),           // 0: funnel.FunnelData
	(*FunnelTiming)(nil),         // 1: funnel.FunnelTiming
	(*RuntimeData)(nil),          // 2: funnel.RuntimeData
	(*FunnelRequest)(nil),        // 3: funnel.FunnelRequest
	(*FunnelRequestAd)(nil),      // 4: funnel.FunnelRequestAd
	(*FunnelResponse)(nil),       // 5: funnel.FunnelResponse
	(*FunnelResponseAd)(nil),     // 6: funnel.FunnelResponseAd
	(*FunnelResponseAdInfo)(nil), // 7: funnel.FunnelResponseAdInfo
	nil,                          // 8: funnel.RuntimeData.CreativeFilterMapEntry
	nil,                          // 9: funnel.RuntimeData.ClkFrequencyDataEntry
	nil,                          // 10: funnel.RuntimeData.ImpFrequencyDataEntry
}
var file_funnel_proto_depIdxs = []int32{
	1,  // 0: funnel.FunnelData.timing:type_name -> funnel.FunnelTiming
	3,  // 1: funnel.FunnelData.request:type_name -> funnel.FunnelRequest
	5,  // 2: funnel.FunnelData.response:type_name -> funnel.FunnelResponse
	2,  // 3: funnel.FunnelData.runtime_data:type_name -> funnel.RuntimeData
	8,  // 4: funnel.RuntimeData.creative_filter_map:type_name -> funnel.RuntimeData.CreativeFilterMapEntry
	9,  // 5: funnel.RuntimeData.clk_frequency_data:type_name -> funnel.RuntimeData.ClkFrequencyDataEntry
	10, // 6: funnel.RuntimeData.imp_frequency_data:type_name -> funnel.RuntimeData.ImpFrequencyDataEntry
	4,  // 7: funnel.FunnelRequest.req_ads:type_name -> funnel.FunnelRequestAd
	6,  // 8: funnel.FunnelResponse.resp_ads:type_name -> funnel.FunnelResponseAd
	7,  // 9: funnel.FunnelResponseAd.ads:type_name -> funnel.FunnelResponseAdInfo
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_funnel_proto_init() }
func file_funnel_proto_init() {
	if File_funnel_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_funnel_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelTiming); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RuntimeData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelRequestAd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelResponseAd); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_funnel_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FunnelResponseAdInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_funnel_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_funnel_proto_goTypes,
		DependencyIndexes: file_funnel_proto_depIdxs,
		MessageInfos:      file_funnel_proto_msgTypes,
	}.Build()
	File_funnel_proto = out.File
	file_funnel_proto_rawDesc = nil
	file_funnel_proto_goTypes = nil
	file_funnel_proto_depIdxs = nil
}
