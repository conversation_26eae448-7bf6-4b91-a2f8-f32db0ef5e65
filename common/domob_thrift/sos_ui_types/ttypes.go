// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_ui_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/sos_userserver_types"
	"rtb_model_server/common/domob_thrift/user_profile"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = user_profile.GoUnusedProtection__
var _ = sos_userserver_types.GoUnusedProtection__
var GoUnusedProtection__ int

type Sex int64

const (
	Sex_UNKNOWN Sex = 0
	Sex_MAN     Sex = 1
	Sex_WOMAN   Sex = 2
)

func (p Sex) String() string {
	switch p {
	case Sex_UNKNOWN:
		return "Sex_UNKNOWN"
	case Sex_MAN:
		return "Sex_MAN"
	case Sex_WOMAN:
		return "Sex_WOMAN"
	}
	return "<UNSET>"
}

func SexFromString(s string) (Sex, error) {
	switch s {
	case "Sex_UNKNOWN":
		return Sex_UNKNOWN, nil
	case "Sex_MAN":
		return Sex_MAN, nil
	case "Sex_WOMAN":
		return Sex_WOMAN, nil
	}
	return Sex(math.MinInt32 - 1), fmt.Errorf("not a valid Sex string")
}

type AdType int64

const (
	AdType_DOWNLOAD AdType = 1
	AdType_TASK     AdType = 2
	AdType_LANDING  AdType = 3
	AdType_ACTIVITY AdType = 4
)

func (p AdType) String() string {
	switch p {
	case AdType_DOWNLOAD:
		return "AdType_DOWNLOAD"
	case AdType_TASK:
		return "AdType_TASK"
	case AdType_LANDING:
		return "AdType_LANDING"
	case AdType_ACTIVITY:
		return "AdType_ACTIVITY"
	}
	return "<UNSET>"
}

func AdTypeFromString(s string) (AdType, error) {
	switch s {
	case "AdType_DOWNLOAD":
		return AdType_DOWNLOAD, nil
	case "AdType_TASK":
		return AdType_TASK, nil
	case "AdType_LANDING":
		return AdType_LANDING, nil
	case "AdType_ACTIVITY":
		return AdType_ACTIVITY, nil
	}
	return AdType(math.MinInt32 - 1), fmt.Errorf("not a valid AdType string")
}

type JudgeType int64

const (
	JudgeType_ITEMSLIDE      JudgeType = 0
	JudgeType_INSTALLSUCCESS JudgeType = 1
	JudgeType_TASKLAUNCH     JudgeType = 2
	JudgeType_LANDINGSUCCESS JudgeType = 3
)

func (p JudgeType) String() string {
	switch p {
	case JudgeType_ITEMSLIDE:
		return "JudgeType_ITEMSLIDE"
	case JudgeType_INSTALLSUCCESS:
		return "JudgeType_INSTALLSUCCESS"
	case JudgeType_TASKLAUNCH:
		return "JudgeType_TASKLAUNCH"
	case JudgeType_LANDINGSUCCESS:
		return "JudgeType_LANDINGSUCCESS"
	}
	return "<UNSET>"
}

func JudgeTypeFromString(s string) (JudgeType, error) {
	switch s {
	case "JudgeType_ITEMSLIDE":
		return JudgeType_ITEMSLIDE, nil
	case "JudgeType_INSTALLSUCCESS":
		return JudgeType_INSTALLSUCCESS, nil
	case "JudgeType_TASKLAUNCH":
		return JudgeType_TASKLAUNCH, nil
	case "JudgeType_LANDINGSUCCESS":
		return JudgeType_LANDINGSUCCESS, nil
	}
	return JudgeType(math.MinInt32 - 1), fmt.Errorf("not a valid JudgeType string")
}

type SosUIRequest struct {
	Ipb       string `thrift:"ipb,1" json:"ipb"`
	PkgName   string `thrift:"pkg_name,2" json:"pkg_name"`
	PkgVn     string `thrift:"pkg_vn,3" json:"pkg_vn"`
	PkgVc     string `thrift:"pkg_vc,4" json:"pkg_vc"`
	AppName   string `thrift:"app_name,5" json:"app_name"`
	Orid      string `thrift:"orid,6" json:"orid"`
	Channel   string `thrift:"channel,7" json:"channel"`
	UserToken string `thrift:"user_token,8" json:"user_token"`
	// unused field # 9
	Idv         string `thrift:"idv,10" json:"idv"`
	Timestamp   int32  `thrift:"timestamp,11" json:"timestamp"`
	Ov          string `thrift:"ov,12" json:"ov"`
	Hwmodal     string `thrift:"hwmodal,13" json:"hwmodal"`
	Carrier     string `thrift:"carrier,14" json:"carrier"`
	Network     string `thrift:"network,15" json:"network"`
	Sw          string `thrift:"sw,16" json:"sw"`
	Sh          string `thrift:"sh,17" json:"sh"`
	Sd          string `thrift:"sd,18" json:"sd"`
	So          string `thrift:"so,19" json:"so"`
	Coord       string `thrift:"coord,20" json:"coord"`
	CoordAcc    string `thrift:"coord_acc,21" json:"coord_acc"`
	CoordStatus string `thrift:"coord_status,22" json:"coord_status"`
	CoordTs     string `thrift:"coord_ts,23" json:"coord_ts"`
	Ma          string `thrift:"ma,24" json:"ma"`
	// unused field # 25
	Ama string `thrift:"ama,26" json:"ama"`
	An  string `thrift:"an,27" json:"an"`
	// unused field # 28
	// unused field # 29
	Sv string `thrift:"sv,30" json:"sv"`
	C  string `thrift:"c,31" json:"c"`
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	Id             int32 `thrift:"id,37" json:"id"`
	ConsumeOrderid int64 `thrift:"consume_orderid,38" json:"consume_orderid"`
	// unused field # 39
	Cpuinfo     string `thrift:"cpuinfo,40" json:"cpuinfo"`
	Siminfo     string `thrift:"siminfo,41" json:"siminfo"`
	IsRoot      bool   `thrift:"is_root,42" json:"is_root"`
	Memoryinfo  string `thrift:"memoryinfo,43" json:"memoryinfo"`
	Cis         string `thrift:"cis,44" json:"cis"`
	Batteryinfo string `thrift:"batteryinfo,45" json:"batteryinfo"`
	BootTime    string `thrift:"boot_time,46" json:"boot_time"`
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	Sid       string `thrift:"sid,51" json:"sid"`
	DebugMode bool   `thrift:"debug_mode,52" json:"debug_mode"`
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	PhoneNum  int64  `thrift:"phone_num,60" json:"phone_num"`
	Sex       Sex    `thrift:"sex,61" json:"sex"`
	Birthday  int32  `thrift:"birthday,62" json:"birthday"`
	Presenter string `thrift:"presenter,63" json:"presenter"`
	Sign      int32  `thrift:"sign,64" json:"sign"`
	UserName  string `thrift:"user_name,65" json:"user_name"`
	// unused field # 66
	Password       string                             `thrift:"password,67" json:"password"`
	ItemId         int32                              `thrift:"item_id,68" json:"item_id"`
	Account        string                             `thrift:"account,69" json:"account"`
	OldPassword    string                             `thrift:"old_password,70" json:"old_password"`
	NewPassword    string                             `thrift:"new_password,71" json:"new_password"`
	SignType       sos_userserver_types.PhoneSighType `thrift:"sign_type,72" json:"sign_type"`
	AliAccountName string                             `thrift:"ali_account_name,73" json:"ali_account_name"`
	AppConfigType  int32                              `thrift:"app_config_type,74" json:"app_config_type"`
	GiftCode       string                             `thrift:"gift_code,75" json:"gift_code"`
	Bonus          int32                              `thrift:"bonus,76" json:"bonus"`
}

func NewSosUIRequest() *SosUIRequest {
	return &SosUIRequest{
		Sex: math.MinInt32 - 1, // unset sentinal value

		SignType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SosUIRequest) IsSetSex() bool {
	return int64(p.Sex) != math.MinInt32-1
}

func (p *SosUIRequest) IsSetSignType() bool {
	return int64(p.SignType) != math.MinInt32-1
}

func (p *SosUIRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.STRING {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.STRING {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.STRING {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.STRING {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.STRING {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.STRING {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.STRING {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.STRING {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.STRING {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.I32 {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.I64 {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.STRING {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.STRING {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 42:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField42(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 43:
			if fieldTypeId == thrift.STRING {
				if err := p.readField43(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 44:
			if fieldTypeId == thrift.STRING {
				if err := p.readField44(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 45:
			if fieldTypeId == thrift.STRING {
				if err := p.readField45(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 46:
			if fieldTypeId == thrift.STRING {
				if err := p.readField46(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.STRING {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I64 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.I32 {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I32 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 63:
			if fieldTypeId == thrift.STRING {
				if err := p.readField63(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.I32 {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 65:
			if fieldTypeId == thrift.STRING {
				if err := p.readField65(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 67:
			if fieldTypeId == thrift.STRING {
				if err := p.readField67(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 68:
			if fieldTypeId == thrift.I32 {
				if err := p.readField68(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 69:
			if fieldTypeId == thrift.STRING {
				if err := p.readField69(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.STRING {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.STRING {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.I32 {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.STRING {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.I32 {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 75:
			if fieldTypeId == thrift.STRING {
				if err := p.readField75(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 76:
			if fieldTypeId == thrift.I32 {
				if err := p.readField76(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosUIRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Ipb = v
	}
	return nil
}

func (p *SosUIRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PkgName = v
	}
	return nil
}

func (p *SosUIRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PkgVn = v
	}
	return nil
}

func (p *SosUIRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.PkgVc = v
	}
	return nil
}

func (p *SosUIRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *SosUIRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Orid = v
	}
	return nil
}

func (p *SosUIRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Channel = v
	}
	return nil
}

func (p *SosUIRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.UserToken = v
	}
	return nil
}

func (p *SosUIRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *SosUIRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Timestamp = v
	}
	return nil
}

func (p *SosUIRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Ov = v
	}
	return nil
}

func (p *SosUIRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Hwmodal = v
	}
	return nil
}

func (p *SosUIRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.Carrier = v
	}
	return nil
}

func (p *SosUIRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Network = v
	}
	return nil
}

func (p *SosUIRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Sw = v
	}
	return nil
}

func (p *SosUIRequest) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Sh = v
	}
	return nil
}

func (p *SosUIRequest) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.Sd = v
	}
	return nil
}

func (p *SosUIRequest) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.So = v
	}
	return nil
}

func (p *SosUIRequest) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Coord = v
	}
	return nil
}

func (p *SosUIRequest) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CoordAcc = v
	}
	return nil
}

func (p *SosUIRequest) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.CoordStatus = v
	}
	return nil
}

func (p *SosUIRequest) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.CoordTs = v
	}
	return nil
}

func (p *SosUIRequest) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Ma = v
	}
	return nil
}

func (p *SosUIRequest) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.Ama = v
	}
	return nil
}

func (p *SosUIRequest) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.An = v
	}
	return nil
}

func (p *SosUIRequest) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.Sv = v
	}
	return nil
}

func (p *SosUIRequest) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.C = v
	}
	return nil
}

func (p *SosUIRequest) readField37(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 37: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SosUIRequest) readField38(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 38: %s", err)
	} else {
		p.ConsumeOrderid = v
	}
	return nil
}

func (p *SosUIRequest) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.Cpuinfo = v
	}
	return nil
}

func (p *SosUIRequest) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.Siminfo = v
	}
	return nil
}

func (p *SosUIRequest) readField42(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 42: %s", err)
	} else {
		p.IsRoot = v
	}
	return nil
}

func (p *SosUIRequest) readField43(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 43: %s", err)
	} else {
		p.Memoryinfo = v
	}
	return nil
}

func (p *SosUIRequest) readField44(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 44: %s", err)
	} else {
		p.Cis = v
	}
	return nil
}

func (p *SosUIRequest) readField45(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 45: %s", err)
	} else {
		p.Batteryinfo = v
	}
	return nil
}

func (p *SosUIRequest) readField46(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 46: %s", err)
	} else {
		p.BootTime = v
	}
	return nil
}

func (p *SosUIRequest) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.Sid = v
	}
	return nil
}

func (p *SosUIRequest) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.DebugMode = v
	}
	return nil
}

func (p *SosUIRequest) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *SosUIRequest) readField61(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 61: %s", err)
	} else {
		p.Sex = Sex(v)
	}
	return nil
}

func (p *SosUIRequest) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.Birthday = v
	}
	return nil
}

func (p *SosUIRequest) readField63(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 63: %s", err)
	} else {
		p.Presenter = v
	}
	return nil
}

func (p *SosUIRequest) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.Sign = v
	}
	return nil
}

func (p *SosUIRequest) readField65(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 65: %s", err)
	} else {
		p.UserName = v
	}
	return nil
}

func (p *SosUIRequest) readField67(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 67: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *SosUIRequest) readField68(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 68: %s", err)
	} else {
		p.ItemId = v
	}
	return nil
}

func (p *SosUIRequest) readField69(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 69: %s", err)
	} else {
		p.Account = v
	}
	return nil
}

func (p *SosUIRequest) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.OldPassword = v
	}
	return nil
}

func (p *SosUIRequest) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.NewPassword = v
	}
	return nil
}

func (p *SosUIRequest) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.SignType = sos_userserver_types.PhoneSighType(v)
	}
	return nil
}

func (p *SosUIRequest) readField73(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 73: %s", err)
	} else {
		p.AliAccountName = v
	}
	return nil
}

func (p *SosUIRequest) readField74(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 74: %s", err)
	} else {
		p.AppConfigType = v
	}
	return nil
}

func (p *SosUIRequest) readField75(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 75: %s", err)
	} else {
		p.GiftCode = v
	}
	return nil
}

func (p *SosUIRequest) readField76(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 76: %s", err)
	} else {
		p.Bonus = v
	}
	return nil
}

func (p *SosUIRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosUIRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField42(oprot); err != nil {
		return err
	}
	if err := p.writeField43(oprot); err != nil {
		return err
	}
	if err := p.writeField44(oprot); err != nil {
		return err
	}
	if err := p.writeField45(oprot); err != nil {
		return err
	}
	if err := p.writeField46(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField63(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField65(oprot); err != nil {
		return err
	}
	if err := p.writeField67(oprot); err != nil {
		return err
	}
	if err := p.writeField68(oprot); err != nil {
		return err
	}
	if err := p.writeField69(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField75(oprot); err != nil {
		return err
	}
	if err := p.writeField76(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosUIRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ipb", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ipb: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ipb)); err != nil {
		return fmt.Errorf("%T.ipb (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ipb: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_name", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pkg_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgName)); err != nil {
		return fmt.Errorf("%T.pkg_name (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pkg_name: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_vn", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:pkg_vn: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgVn)); err != nil {
		return fmt.Errorf("%T.pkg_vn (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:pkg_vn: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pkg_vc", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:pkg_vc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PkgVc)); err != nil {
		return fmt.Errorf("%T.pkg_vc (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:pkg_vc: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:app_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.app_name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:app_name: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("orid", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:orid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Orid)); err != nil {
		return fmt.Errorf("%T.orid (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:orid: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("channel", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:channel: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Channel)); err != nil {
		return fmt.Errorf("%T.channel (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:channel: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_token", thrift.STRING, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:user_token: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserToken)); err != nil {
		return fmt.Errorf("%T.user_token (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:user_token: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:idv: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("timestamp", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:timestamp: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Timestamp)); err != nil {
		return fmt.Errorf("%T.timestamp (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:timestamp: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ov", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:ov: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ov)); err != nil {
		return fmt.Errorf("%T.ov (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:ov: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("hwmodal", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:hwmodal: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Hwmodal)); err != nil {
		return fmt.Errorf("%T.hwmodal (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:hwmodal: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:carrier: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Carrier)); err != nil {
		return fmt.Errorf("%T.carrier (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:carrier: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("network", thrift.STRING, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:network: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Network)); err != nil {
		return fmt.Errorf("%T.network (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:network: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sw", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:sw: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sw)); err != nil {
		return fmt.Errorf("%T.sw (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:sw: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sh", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:sh: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sh)); err != nil {
		return fmt.Errorf("%T.sh (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:sh: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sd", thrift.STRING, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:sd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sd)); err != nil {
		return fmt.Errorf("%T.sd (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:sd: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("so", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:so: %s", p, err)
	}
	if err := oprot.WriteString(string(p.So)); err != nil {
		return fmt.Errorf("%T.so (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:so: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:coord: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Coord)); err != nil {
		return fmt.Errorf("%T.coord (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:coord: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_acc", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:coord_acc: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordAcc)); err != nil {
		return fmt.Errorf("%T.coord_acc (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:coord_acc: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_status", thrift.STRING, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:coord_status: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordStatus)); err != nil {
		return fmt.Errorf("%T.coord_status (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:coord_status: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("coord_ts", thrift.STRING, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:coord_ts: %s", p, err)
	}
	if err := oprot.WriteString(string(p.CoordTs)); err != nil {
		return fmt.Errorf("%T.coord_ts (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:coord_ts: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ma", thrift.STRING, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:ma: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ma)); err != nil {
		return fmt.Errorf("%T.ma (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:ma: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ama", thrift.STRING, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:ama: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ama)); err != nil {
		return fmt.Errorf("%T.ama (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:ama: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("an", thrift.STRING, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:an: %s", p, err)
	}
	if err := oprot.WriteString(string(p.An)); err != nil {
		return fmt.Errorf("%T.an (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:an: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sv", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:sv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sv)); err != nil {
		return fmt.Errorf("%T.sv (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:sv: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("c", thrift.STRING, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:c: %s", p, err)
	}
	if err := oprot.WriteString(string(p.C)); err != nil {
		return fmt.Errorf("%T.c (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:c: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField37(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 37); err != nil {
		return fmt.Errorf("%T write field begin error 37:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (37) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 37:id: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField38(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("consume_orderid", thrift.I64, 38); err != nil {
		return fmt.Errorf("%T write field begin error 38:consume_orderid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ConsumeOrderid)); err != nil {
		return fmt.Errorf("%T.consume_orderid (38) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 38:consume_orderid: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpuinfo", thrift.STRING, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:cpuinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cpuinfo)); err != nil {
		return fmt.Errorf("%T.cpuinfo (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:cpuinfo: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("siminfo", thrift.STRING, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:siminfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Siminfo)); err != nil {
		return fmt.Errorf("%T.siminfo (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:siminfo: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField42(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_root", thrift.BOOL, 42); err != nil {
		return fmt.Errorf("%T write field begin error 42:is_root: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsRoot)); err != nil {
		return fmt.Errorf("%T.is_root (42) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 42:is_root: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField43(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("memoryinfo", thrift.STRING, 43); err != nil {
		return fmt.Errorf("%T write field begin error 43:memoryinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Memoryinfo)); err != nil {
		return fmt.Errorf("%T.memoryinfo (43) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 43:memoryinfo: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField44(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cis", thrift.STRING, 44); err != nil {
		return fmt.Errorf("%T write field begin error 44:cis: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Cis)); err != nil {
		return fmt.Errorf("%T.cis (44) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 44:cis: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField45(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("batteryinfo", thrift.STRING, 45); err != nil {
		return fmt.Errorf("%T write field begin error 45:batteryinfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Batteryinfo)); err != nil {
		return fmt.Errorf("%T.batteryinfo (45) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 45:batteryinfo: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField46(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("boot_time", thrift.STRING, 46); err != nil {
		return fmt.Errorf("%T write field begin error 46:boot_time: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BootTime)); err != nil {
		return fmt.Errorf("%T.boot_time (46) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 46:boot_time: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.STRING, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:sid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:sid: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("debug_mode", thrift.BOOL, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:debug_mode: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.DebugMode)); err != nil {
		return fmt.Errorf("%T.debug_mode (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:debug_mode: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.I64, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:phone_num: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:phone_num: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField61(oprot thrift.TProtocol) (err error) {
	if p.IsSetSex() {
		if err := oprot.WriteFieldBegin("sex", thrift.I32, 61); err != nil {
			return fmt.Errorf("%T write field begin error 61:sex: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Sex)); err != nil {
			return fmt.Errorf("%T.sex (61) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 61:sex: %s", p, err)
		}
	}
	return err
}

func (p *SosUIRequest) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("birthday", thrift.I32, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:birthday: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Birthday)); err != nil {
		return fmt.Errorf("%T.birthday (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:birthday: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField63(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("presenter", thrift.STRING, 63); err != nil {
		return fmt.Errorf("%T write field begin error 63:presenter: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Presenter)); err != nil {
		return fmt.Errorf("%T.presenter (63) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 63:presenter: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign", thrift.I32, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:sign: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sign)); err != nil {
		return fmt.Errorf("%T.sign (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:sign: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField65(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("user_name", thrift.STRING, 65); err != nil {
		return fmt.Errorf("%T write field begin error 65:user_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.UserName)); err != nil {
		return fmt.Errorf("%T.user_name (65) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 65:user_name: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField67(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 67); err != nil {
		return fmt.Errorf("%T write field begin error 67:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (67) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 67:password: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField68(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("item_id", thrift.I32, 68); err != nil {
		return fmt.Errorf("%T write field begin error 68:item_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ItemId)); err != nil {
		return fmt.Errorf("%T.item_id (68) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 68:item_id: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField69(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("account", thrift.STRING, 69); err != nil {
		return fmt.Errorf("%T write field begin error 69:account: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Account)); err != nil {
		return fmt.Errorf("%T.account (69) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 69:account: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("old_password", thrift.STRING, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:old_password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.OldPassword)); err != nil {
		return fmt.Errorf("%T.old_password (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:old_password: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("new_password", thrift.STRING, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:new_password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.NewPassword)); err != nil {
		return fmt.Errorf("%T.new_password (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:new_password: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField72(oprot thrift.TProtocol) (err error) {
	if p.IsSetSignType() {
		if err := oprot.WriteFieldBegin("sign_type", thrift.I32, 72); err != nil {
			return fmt.Errorf("%T write field begin error 72:sign_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SignType)); err != nil {
			return fmt.Errorf("%T.sign_type (72) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 72:sign_type: %s", p, err)
		}
	}
	return err
}

func (p *SosUIRequest) writeField73(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ali_account_name", thrift.STRING, 73); err != nil {
		return fmt.Errorf("%T write field begin error 73:ali_account_name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AliAccountName)); err != nil {
		return fmt.Errorf("%T.ali_account_name (73) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 73:ali_account_name: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField74(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_config_type", thrift.I32, 74); err != nil {
		return fmt.Errorf("%T write field begin error 74:app_config_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppConfigType)); err != nil {
		return fmt.Errorf("%T.app_config_type (74) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 74:app_config_type: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField75(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gift_code", thrift.STRING, 75); err != nil {
		return fmt.Errorf("%T write field begin error 75:gift_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.GiftCode)); err != nil {
		return fmt.Errorf("%T.gift_code (75) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 75:gift_code: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) writeField76(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bonus", thrift.I32, 76); err != nil {
		return fmt.Errorf("%T write field begin error 76:bonus: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Bonus)); err != nil {
		return fmt.Errorf("%T.bonus (76) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 76:bonus: %s", p, err)
	}
	return err
}

func (p *SosUIRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosUIRequest(%+v)", *p)
}

type SosUIProccessedRequest struct {
	Req      *SosUIRequest `thrift:"req,1" json:"req"`
	Imei     string        `thrift:"imei,2" json:"imei"`
	ClientIp string        `thrift:"client_ip,3" json:"client_ip"`
	Login    bool          `thrift:"login,4" json:"login"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	AccessCode  common.AccessTypeCode   `thrift:"access_code,10" json:"access_code"`
	OsCode      common.OSCode           `thrift:"os_code,11" json:"os_code"`
	Geo         *user_profile.GeoResult `thrift:"geo,12" json:"geo"`
	ServerTime  common.TimeInt          `thrift:"server_time,13" json:"server_time"`
	CarrierCode common.CarrierCode      `thrift:"carrier_code,14" json:"carrier_code"`
	Userid      int32                   `thrift:"userid,15" json:"userid"`
	Deviceid    int32                   `thrift:"deviceid,16" json:"deviceid"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Capability []string `thrift:"capability,20" json:"capability"`
}

func NewSosUIProccessedRequest() *SosUIProccessedRequest {
	return &SosUIProccessedRequest{
		AccessCode: math.MinInt32 - 1, // unset sentinal value

		CarrierCode: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SosUIProccessedRequest) IsSetAccessCode() bool {
	return int64(p.AccessCode) != math.MinInt32-1
}

func (p *SosUIProccessedRequest) IsSetCarrierCode() bool {
	return int64(p.CarrierCode) != math.MinInt32-1
}

func (p *SosUIProccessedRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I64 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.LIST {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField1(iprot thrift.TProtocol) error {
	p.Req = NewSosUIRequest()
	if err := p.Req.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Req)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Imei = v
	}
	return nil
}

func (p *SosUIProccessedRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.ClientIp = v
	}
	return nil
}

func (p *SosUIProccessedRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Login = v
	}
	return nil
}

func (p *SosUIProccessedRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.AccessCode = common.AccessTypeCode(v)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.OsCode = common.OSCode(v)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField12(iprot thrift.TProtocol) error {
	p.Geo = user_profile.NewGeoResult()
	if err := p.Geo.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Geo)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ServerTime = common.TimeInt(v)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CarrierCode = common.CarrierCode(v)
	}
	return nil
}

func (p *SosUIProccessedRequest) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *SosUIProccessedRequest) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Deviceid = v
	}
	return nil
}

func (p *SosUIProccessedRequest) readField20(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Capability = make([]string, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = v
		}
		p.Capability = append(p.Capability, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SosUIProccessedRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosUIProccessedRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosUIProccessedRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Req != nil {
		if err := oprot.WriteFieldBegin("req", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:req: %s", p, err)
		}
		if err := p.Req.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Req)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:req: %s", p, err)
		}
	}
	return err
}

func (p *SosUIProccessedRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imei", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:imei: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Imei)); err != nil {
		return fmt.Errorf("%T.imei (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:imei: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("client_ip", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:client_ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClientIp)); err != nil {
		return fmt.Errorf("%T.client_ip (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:client_ip: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("login", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:login: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Login)); err != nil {
		return fmt.Errorf("%T.login (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:login: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("access_code", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:access_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AccessCode)); err != nil {
		return fmt.Errorf("%T.access_code (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:access_code: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("os_code", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:os_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.OsCode)); err != nil {
		return fmt.Errorf("%T.os_code (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:os_code: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if p.Geo != nil {
		if err := oprot.WriteFieldBegin("geo", thrift.STRUCT, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:geo: %s", p, err)
		}
		if err := p.Geo.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Geo)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:geo: %s", p, err)
		}
	}
	return err
}

func (p *SosUIProccessedRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("server_time", thrift.I64, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:server_time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ServerTime)); err != nil {
		return fmt.Errorf("%T.server_time (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:server_time: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("carrier_code", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:carrier_code: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CarrierCode)); err != nil {
		return fmt.Errorf("%T.carrier_code (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:carrier_code: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:userid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:userid: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceid", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:deviceid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deviceid)); err != nil {
		return fmt.Errorf("%T.deviceid (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:deviceid: %s", p, err)
	}
	return err
}

func (p *SosUIProccessedRequest) writeField20(oprot thrift.TProtocol) (err error) {
	if p.Capability != nil {
		if err := oprot.WriteFieldBegin("capability", thrift.LIST, 20); err != nil {
			return fmt.Errorf("%T write field begin error 20:capability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRING, len(p.Capability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Capability {
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 20:capability: %s", p, err)
		}
	}
	return err
}

func (p *SosUIProccessedRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosUIProccessedRequest(%+v)", *p)
}

type SosTracker struct {
	Preq        *SosUIProccessedRequest `thrift:"preq,1" json:"preq"`
	Pid         int32                   `thrift:"pid,2" json:"pid"`
	Cid         int32                   `thrift:"cid,3" json:"cid"`
	Point       float64                 `thrift:"point,4" json:"point"`
	Price       float64                 `thrift:"price,5" json:"price"`
	Action      int32                   `thrift:"action,6" json:"action"`
	AdType      AdType                  `thrift:"ad_type,7" json:"ad_type"`
	SpPrice     float64                 `thrift:"sp_price,8" json:"sp_price"`
	Appid       string                  `thrift:"appid,9" json:"appid"`
	IsSuperTask bool                    `thrift:"is_super_task,10" json:"is_super_task"`
	ExtraPrice  int64                   `thrift:"extra_price,11" json:"extra_price"`
}

func NewSosTracker() *SosTracker {
	return &SosTracker{
		AdType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SosTracker) IsSetAdType() bool {
	return int64(p.AdType) != math.MinInt32-1
}

func (p *SosTracker) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SosTracker) readField1(iprot thrift.TProtocol) error {
	p.Preq = NewSosUIProccessedRequest()
	if err := p.Preq.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Preq)
	}
	return nil
}

func (p *SosTracker) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = v
	}
	return nil
}

func (p *SosTracker) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *SosTracker) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *SosTracker) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Price = v
	}
	return nil
}

func (p *SosTracker) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Action = v
	}
	return nil
}

func (p *SosTracker) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdType = AdType(v)
	}
	return nil
}

func (p *SosTracker) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.SpPrice = v
	}
	return nil
}

func (p *SosTracker) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Appid = v
	}
	return nil
}

func (p *SosTracker) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.IsSuperTask = v
	}
	return nil
}

func (p *SosTracker) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ExtraPrice = v
	}
	return nil
}

func (p *SosTracker) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SosTracker"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SosTracker) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Preq != nil {
		if err := oprot.WriteFieldBegin("preq", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:preq: %s", p, err)
		}
		if err := p.Preq.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Preq)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:preq: %s", p, err)
		}
	}
	return err
}

func (p *SosTracker) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:cid: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.DOUBLE, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:point: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("price", thrift.DOUBLE, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:price: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Price)); err != nil {
		return fmt.Errorf("%T.price (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:price: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("action", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:action: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Action)); err != nil {
		return fmt.Errorf("%T.action (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:action: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField7(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdType() {
		if err := oprot.WriteFieldBegin("ad_type", thrift.I32, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:ad_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdType)); err != nil {
			return fmt.Errorf("%T.ad_type (7) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:ad_type: %s", p, err)
		}
	}
	return err
}

func (p *SosTracker) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sp_price", thrift.DOUBLE, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:sp_price: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.SpPrice)); err != nil {
		return fmt.Errorf("%T.sp_price (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:sp_price: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appid", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:appid: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Appid)); err != nil {
		return fmt.Errorf("%T.appid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:appid: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("is_super_task", thrift.BOOL, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:is_super_task: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsSuperTask)); err != nil {
		return fmt.Errorf("%T.is_super_task (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:is_super_task: %s", p, err)
	}
	return err
}

func (p *SosTracker) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extra_price", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:extra_price: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ExtraPrice)); err != nil {
		return fmt.Errorf("%T.extra_price (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:extra_price: %s", p, err)
	}
	return err
}

func (p *SosTracker) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SosTracker(%+v)", *p)
}

type Condition struct {
	JudgeType JudgeType `thrift:"judge_type,1" json:"judge_type"`
	Tr        string    `thrift:"tr,2" json:"tr"`
	Idv       string    `thrift:"idv,3" json:"idv"`
}

func NewCondition() *Condition {
	return &Condition{
		JudgeType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *Condition) IsSetJudgeType() bool {
	return int64(p.JudgeType) != math.MinInt32-1
}

func (p *Condition) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *Condition) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.JudgeType = JudgeType(v)
	}
	return nil
}

func (p *Condition) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Tr = v
	}
	return nil
}

func (p *Condition) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Idv = v
	}
	return nil
}

func (p *Condition) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("Condition"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *Condition) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetJudgeType() {
		if err := oprot.WriteFieldBegin("judge_type", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:judge_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.JudgeType)); err != nil {
			return fmt.Errorf("%T.judge_type (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:judge_type: %s", p, err)
		}
	}
	return err
}

func (p *Condition) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("tr", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:tr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Tr)); err != nil {
		return fmt.Errorf("%T.tr (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:tr: %s", p, err)
	}
	return err
}

func (p *Condition) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idv", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:idv: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Idv)); err != nil {
		return fmt.Errorf("%T.idv (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:idv: %s", p, err)
	}
	return err
}

func (p *Condition) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("Condition(%+v)", *p)
}

type RegisterRequest struct {
	PhoneNum string `thrift:"phone_num,1" json:"phone_num"`
	Sex      Sex    `thrift:"sex,2" json:"sex"`
	Birthday int32  `thrift:"birthday,3" json:"birthday"`
	Sign     string `thrift:"sign,4" json:"sign"`
	Password string `thrift:"password,5" json:"password"`
}

func NewRegisterRequest() *RegisterRequest {
	return &RegisterRequest{
		Sex: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *RegisterRequest) IsSetSex() bool {
	return int64(p.Sex) != math.MinInt32-1
}

func (p *RegisterRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RegisterRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.PhoneNum = v
	}
	return nil
}

func (p *RegisterRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Sex = Sex(v)
	}
	return nil
}

func (p *RegisterRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Birthday = v
	}
	return nil
}

func (p *RegisterRequest) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Sign = v
	}
	return nil
}

func (p *RegisterRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Password = v
	}
	return nil
}

func (p *RegisterRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("RegisterRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RegisterRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("phone_num", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:phone_num: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PhoneNum)); err != nil {
		return fmt.Errorf("%T.phone_num (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:phone_num: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetSex() {
		if err := oprot.WriteFieldBegin("sex", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:sex: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Sex)); err != nil {
			return fmt.Errorf("%T.sex (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:sex: %s", p, err)
		}
	}
	return err
}

func (p *RegisterRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("birthday", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:birthday: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Birthday)); err != nil {
		return fmt.Errorf("%T.birthday (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:birthday: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sign", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sign: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Sign)); err != nil {
		return fmt.Errorf("%T.sign (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sign: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("password", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:password: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Password)); err != nil {
		return fmt.Errorf("%T.password (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:password: %s", p, err)
	}
	return err
}

func (p *RegisterRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RegisterRequest(%+v)", *p)
}
