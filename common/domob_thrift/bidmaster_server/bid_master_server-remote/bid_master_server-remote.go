// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"bidmaster_server"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.Stderr, "  i32 addSponsor(RequestHeader header, SponsorProfile sponsor)")
	fmt.Fprintln(os.Stderr, "  i32 addSponsorWithPassword(RequestHeader header, SponsorProfile sponsor, string password)")
	fmt.Fprintln(os.<PERSON>derr, "  void editSponsor(RequestHeader header, i32 agentUid, SponsorProfile sponsor)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchSponsorsByParams(RequestHeader header, SponsorParams params)")
	fmt.Fprintln(os.Stderr, "   getSponsorsByUids(RequestHeader header,  uids)")
	fmt.Fprintln(os.Stderr, "  void innerApproveSponsor(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  void editSponsorAuditMaterials(RequestHeader header, i32 id,  materials)")
	fmt.Fprintln(os.Stderr, "  void innerApproveAuditMaterials(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  i32 addAdOrder(RequestHeader header, i32 agentUid, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "  void editAdOrder(RequestHeader header, i32 agentUid, AdOrder adOrder)")
	fmt.Fprintln(os.Stderr, "   getAdOrdersByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdOrdersByParams(RequestHeader header, AdOrderParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdOrdersByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdOrdersByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdOrderByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdPromotion(RequestHeader header, i32 sponsorId, AdPromotion promotion)")
	fmt.Fprintln(os.Stderr, "  void editAdPromotion(RequestHeader header, i32 sponsorId, AdPromotion promotion)")
	fmt.Fprintln(os.Stderr, "   getAdPromotionsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdPromotionByParams(RequestHeader header, AdPromotionParams params)")
	fmt.Fprintln(os.Stderr, "  void deleteAdPromotionsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdCampaign(RequestHeader header, i32 agentUid, AdCampaign campaign)")
	fmt.Fprintln(os.Stderr, "  void editAdCampaign(RequestHeader header, i32 agentUid, AdCampaign campaign)")
	fmt.Fprintln(os.Stderr, "   getAdCampaignsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdCampaignByParams(RequestHeader header, AdCampaignParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCampaignsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCampaignsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCampaignsByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "  void editCampaignActualDailyBudget(RequestHeader header, i32 campaignId, i64 actualDailyBudget)")
	fmt.Fprintln(os.Stderr, "  i32 addAdStrategy(RequestHeader header, i32 agentUid, AdStrategy strategy)")
	fmt.Fprintln(os.Stderr, "  void editAdStrategy(RequestHeader header, i32 agentUid, AdStrategy strategy)")
	fmt.Fprintln(os.Stderr, "   getAdStrategiesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdStrategyByParams(RequestHeader header, AdStrategyParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdStrategiesByIds(RequestHeader header, i32 sponsorId, i32 campaignId,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addAdCreative(RequestHeader header, i32 agentUid, AdCreative creative)")
	fmt.Fprintln(os.Stderr, "  void editAdCreative(RequestHeader header, i32 agentUid, AdCreative creative)")
	fmt.Fprintln(os.Stderr, "   getAdCreativesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdCreativeByParams(RequestHeader header, AdCreativeParams params)")
	fmt.Fprintln(os.Stderr, "  void pauseAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void resumeAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void deleteAdCreativesByIds(RequestHeader header, i32 sponsorId, i32 campaignId, i32 strategyId,  ids)")
	fmt.Fprintln(os.Stderr, "  void innerApproveAdCreatives(RequestHeader header, i32 act,  approveInfo)")
	fmt.Fprintln(os.Stderr, "  void editCampaignMinProfitRatio(RequestHeader header,  campaignIds, i32 minProfitRatio)")
	fmt.Fprintln(os.Stderr, "   getOrderIdsByProjectIds(RequestHeader header,  projectIds)")
	fmt.Fprintln(os.Stderr, "  void debugAdTracking(RequestHeader header, i32 sponsorId, i32 promotionId, string deviceId, i32 adTrackingId)")
	fmt.Fprintln(os.Stderr, "  i32 addAdExchangeDmp(RequestHeader header, i32 sponsorId, AdExchangeDmp adExchangeDmp)")
	fmt.Fprintln(os.Stderr, "  void editAdExchangeDmp(RequestHeader header, i32 sponsorId, AdExchangeDmp adExchangeDmp)")
	fmt.Fprintln(os.Stderr, "  void deleteAdExchangeDmpByIds(RequestHeader header, i32 sponsorId,  ids)")
	fmt.Fprintln(os.Stderr, "   getAdExchangeDmpByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdExchangeDmpByParams(RequestHeader header, AdExchangeDmpParams params)")
	fmt.Fprintln(os.Stderr, "   searchAdSuggestPrice(RequestHeader header, i32 exchangeId, i32 platform)")
	fmt.Fprintln(os.Stderr, "  i32 addAdTracking(RequestHeader header, AdTracking adTracking)")
	fmt.Fprintln(os.Stderr, "  void editAdTracking(RequestHeader header, AdTracking adTracking)")
	fmt.Fprintln(os.Stderr, "   getAdTrackingsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  QueryResult searchAdTrackingByParams(RequestHeader header, AdTrackingParams params)")
	fmt.Fprintln(os.Stderr, "  void deleteAdTrackingByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  i32 addDeviceFile(RequestHeader header, DeviceFile deviceFile)")
	fmt.Fprintln(os.Stderr, "   getDeviceFilesByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  AliyunOss getAliyunOssToken(RequestHeader header)")
	fmt.Fprintln(os.Stderr, "  BaiduBos getBaiduBosToken(RequestHeader header)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := bidmaster_server.NewBidMasterServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "addSponsor":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddSponsor requires 2 args")
			flag.Usage()
		}
		arg301 := flag.Arg(1)
		mbTrans302 := thrift.NewTMemoryBufferLen(len(arg301))
		defer mbTrans302.Close()
		_, err303 := mbTrans302.WriteString(arg301)
		if err303 != nil {
			Usage()
			return
		}
		factory304 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt305 := factory304.GetProtocol(mbTrans302)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err306 := argvalue0.Read(jsProt305)
		if err306 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg307 := flag.Arg(2)
		mbTrans308 := thrift.NewTMemoryBufferLen(len(arg307))
		defer mbTrans308.Close()
		_, err309 := mbTrans308.WriteString(arg307)
		if err309 != nil {
			Usage()
			return
		}
		factory310 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt311 := factory310.GetProtocol(mbTrans308)
		argvalue1 := bidmaster_server.NewSponsorProfile()
		err312 := argvalue1.Read(jsProt311)
		if err312 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddSponsor(value0, value1))
		fmt.Print("\n")
		break
	case "addSponsorWithPassword":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddSponsorWithPassword requires 3 args")
			flag.Usage()
		}
		arg313 := flag.Arg(1)
		mbTrans314 := thrift.NewTMemoryBufferLen(len(arg313))
		defer mbTrans314.Close()
		_, err315 := mbTrans314.WriteString(arg313)
		if err315 != nil {
			Usage()
			return
		}
		factory316 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt317 := factory316.GetProtocol(mbTrans314)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err318 := argvalue0.Read(jsProt317)
		if err318 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg319 := flag.Arg(2)
		mbTrans320 := thrift.NewTMemoryBufferLen(len(arg319))
		defer mbTrans320.Close()
		_, err321 := mbTrans320.WriteString(arg319)
		if err321 != nil {
			Usage()
			return
		}
		factory322 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt323 := factory322.GetProtocol(mbTrans320)
		argvalue1 := bidmaster_server.NewSponsorProfile()
		err324 := argvalue1.Read(jsProt323)
		if err324 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		argvalue2 := flag.Arg(3)
		value2 := argvalue2
		fmt.Print(client.AddSponsorWithPassword(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editSponsor":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSponsor requires 3 args")
			flag.Usage()
		}
		arg326 := flag.Arg(1)
		mbTrans327 := thrift.NewTMemoryBufferLen(len(arg326))
		defer mbTrans327.Close()
		_, err328 := mbTrans327.WriteString(arg326)
		if err328 != nil {
			Usage()
			return
		}
		factory329 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt330 := factory329.GetProtocol(mbTrans327)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err331 := argvalue0.Read(jsProt330)
		if err331 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err332 := (strconv.Atoi(flag.Arg(2)))
		if err332 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg333 := flag.Arg(3)
		mbTrans334 := thrift.NewTMemoryBufferLen(len(arg333))
		defer mbTrans334.Close()
		_, err335 := mbTrans334.WriteString(arg333)
		if err335 != nil {
			Usage()
			return
		}
		factory336 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt337 := factory336.GetProtocol(mbTrans334)
		argvalue2 := bidmaster_server.NewSponsorProfile()
		err338 := argvalue2.Read(jsProt337)
		if err338 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditSponsor(value0, value1, value2))
		fmt.Print("\n")
		break
	case "searchSponsorsByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchSponsorsByParams requires 2 args")
			flag.Usage()
		}
		arg339 := flag.Arg(1)
		mbTrans340 := thrift.NewTMemoryBufferLen(len(arg339))
		defer mbTrans340.Close()
		_, err341 := mbTrans340.WriteString(arg339)
		if err341 != nil {
			Usage()
			return
		}
		factory342 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt343 := factory342.GetProtocol(mbTrans340)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err344 := argvalue0.Read(jsProt343)
		if err344 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg345 := flag.Arg(2)
		mbTrans346 := thrift.NewTMemoryBufferLen(len(arg345))
		defer mbTrans346.Close()
		_, err347 := mbTrans346.WriteString(arg345)
		if err347 != nil {
			Usage()
			return
		}
		factory348 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt349 := factory348.GetProtocol(mbTrans346)
		argvalue1 := bidmaster_server.NewSponsorParams()
		err350 := argvalue1.Read(jsProt349)
		if err350 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchSponsorsByParams(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorsByUids":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorsByUids requires 2 args")
			flag.Usage()
		}
		arg351 := flag.Arg(1)
		mbTrans352 := thrift.NewTMemoryBufferLen(len(arg351))
		defer mbTrans352.Close()
		_, err353 := mbTrans352.WriteString(arg351)
		if err353 != nil {
			Usage()
			return
		}
		factory354 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt355 := factory354.GetProtocol(mbTrans352)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err356 := argvalue0.Read(jsProt355)
		if err356 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg357 := flag.Arg(2)
		mbTrans358 := thrift.NewTMemoryBufferLen(len(arg357))
		defer mbTrans358.Close()
		_, err359 := mbTrans358.WriteString(arg357)
		if err359 != nil {
			Usage()
			return
		}
		factory360 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt361 := factory360.GetProtocol(mbTrans358)
		containerStruct1 := bidmaster_server.NewGetSponsorsByUidsArgs()
		err362 := containerStruct1.ReadField2(jsProt361)
		if err362 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Uids
		value1 := argvalue1
		fmt.Print(client.GetSponsorsByUids(value0, value1))
		fmt.Print("\n")
		break
	case "innerApproveSponsor":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveSponsor requires 3 args")
			flag.Usage()
		}
		arg363 := flag.Arg(1)
		mbTrans364 := thrift.NewTMemoryBufferLen(len(arg363))
		defer mbTrans364.Close()
		_, err365 := mbTrans364.WriteString(arg363)
		if err365 != nil {
			Usage()
			return
		}
		factory366 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt367 := factory366.GetProtocol(mbTrans364)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err368 := argvalue0.Read(jsProt367)
		if err368 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err369 := (strconv.Atoi(flag.Arg(2)))
		if err369 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg370 := flag.Arg(3)
		mbTrans371 := thrift.NewTMemoryBufferLen(len(arg370))
		defer mbTrans371.Close()
		_, err372 := mbTrans371.WriteString(arg370)
		if err372 != nil {
			Usage()
			return
		}
		factory373 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt374 := factory373.GetProtocol(mbTrans371)
		containerStruct2 := bidmaster_server.NewInnerApproveSponsorArgs()
		err375 := containerStruct2.ReadField3(jsProt374)
		if err375 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveSponsor(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editSponsorAuditMaterials":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditSponsorAuditMaterials requires 3 args")
			flag.Usage()
		}
		arg376 := flag.Arg(1)
		mbTrans377 := thrift.NewTMemoryBufferLen(len(arg376))
		defer mbTrans377.Close()
		_, err378 := mbTrans377.WriteString(arg376)
		if err378 != nil {
			Usage()
			return
		}
		factory379 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt380 := factory379.GetProtocol(mbTrans377)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err381 := argvalue0.Read(jsProt380)
		if err381 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err382 := (strconv.Atoi(flag.Arg(2)))
		if err382 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg383 := flag.Arg(3)
		mbTrans384 := thrift.NewTMemoryBufferLen(len(arg383))
		defer mbTrans384.Close()
		_, err385 := mbTrans384.WriteString(arg383)
		if err385 != nil {
			Usage()
			return
		}
		factory386 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt387 := factory386.GetProtocol(mbTrans384)
		containerStruct2 := bidmaster_server.NewEditSponsorAuditMaterialsArgs()
		err388 := containerStruct2.ReadField3(jsProt387)
		if err388 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Materials
		value2 := argvalue2
		fmt.Print(client.EditSponsorAuditMaterials(value0, value1, value2))
		fmt.Print("\n")
		break
	case "innerApproveAuditMaterials":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveAuditMaterials requires 3 args")
			flag.Usage()
		}
		arg389 := flag.Arg(1)
		mbTrans390 := thrift.NewTMemoryBufferLen(len(arg389))
		defer mbTrans390.Close()
		_, err391 := mbTrans390.WriteString(arg389)
		if err391 != nil {
			Usage()
			return
		}
		factory392 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt393 := factory392.GetProtocol(mbTrans390)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err394 := argvalue0.Read(jsProt393)
		if err394 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err395 := (strconv.Atoi(flag.Arg(2)))
		if err395 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg396 := flag.Arg(3)
		mbTrans397 := thrift.NewTMemoryBufferLen(len(arg396))
		defer mbTrans397.Close()
		_, err398 := mbTrans397.WriteString(arg396)
		if err398 != nil {
			Usage()
			return
		}
		factory399 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt400 := factory399.GetProtocol(mbTrans397)
		containerStruct2 := bidmaster_server.NewInnerApproveAuditMaterialsArgs()
		err401 := containerStruct2.ReadField3(jsProt400)
		if err401 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveAuditMaterials(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdOrder requires 3 args")
			flag.Usage()
		}
		arg402 := flag.Arg(1)
		mbTrans403 := thrift.NewTMemoryBufferLen(len(arg402))
		defer mbTrans403.Close()
		_, err404 := mbTrans403.WriteString(arg402)
		if err404 != nil {
			Usage()
			return
		}
		factory405 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt406 := factory405.GetProtocol(mbTrans403)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err407 := argvalue0.Read(jsProt406)
		if err407 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err408 := (strconv.Atoi(flag.Arg(2)))
		if err408 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg409 := flag.Arg(3)
		mbTrans410 := thrift.NewTMemoryBufferLen(len(arg409))
		defer mbTrans410.Close()
		_, err411 := mbTrans410.WriteString(arg409)
		if err411 != nil {
			Usage()
			return
		}
		factory412 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt413 := factory412.GetProtocol(mbTrans410)
		argvalue2 := bidmaster_server.NewAdOrder()
		err414 := argvalue2.Read(jsProt413)
		if err414 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdOrder":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdOrder requires 3 args")
			flag.Usage()
		}
		arg415 := flag.Arg(1)
		mbTrans416 := thrift.NewTMemoryBufferLen(len(arg415))
		defer mbTrans416.Close()
		_, err417 := mbTrans416.WriteString(arg415)
		if err417 != nil {
			Usage()
			return
		}
		factory418 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt419 := factory418.GetProtocol(mbTrans416)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err420 := argvalue0.Read(jsProt419)
		if err420 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err421 := (strconv.Atoi(flag.Arg(2)))
		if err421 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg422 := flag.Arg(3)
		mbTrans423 := thrift.NewTMemoryBufferLen(len(arg422))
		defer mbTrans423.Close()
		_, err424 := mbTrans423.WriteString(arg422)
		if err424 != nil {
			Usage()
			return
		}
		factory425 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt426 := factory425.GetProtocol(mbTrans423)
		argvalue2 := bidmaster_server.NewAdOrder()
		err427 := argvalue2.Read(jsProt426)
		if err427 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdOrder(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdOrdersByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdOrdersByIds requires 2 args")
			flag.Usage()
		}
		arg428 := flag.Arg(1)
		mbTrans429 := thrift.NewTMemoryBufferLen(len(arg428))
		defer mbTrans429.Close()
		_, err430 := mbTrans429.WriteString(arg428)
		if err430 != nil {
			Usage()
			return
		}
		factory431 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt432 := factory431.GetProtocol(mbTrans429)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err433 := argvalue0.Read(jsProt432)
		if err433 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg434 := flag.Arg(2)
		mbTrans435 := thrift.NewTMemoryBufferLen(len(arg434))
		defer mbTrans435.Close()
		_, err436 := mbTrans435.WriteString(arg434)
		if err436 != nil {
			Usage()
			return
		}
		factory437 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt438 := factory437.GetProtocol(mbTrans435)
		containerStruct1 := bidmaster_server.NewGetAdOrdersByIdsArgs()
		err439 := containerStruct1.ReadField2(jsProt438)
		if err439 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdOrdersByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdOrdersByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdOrdersByParams requires 2 args")
			flag.Usage()
		}
		arg440 := flag.Arg(1)
		mbTrans441 := thrift.NewTMemoryBufferLen(len(arg440))
		defer mbTrans441.Close()
		_, err442 := mbTrans441.WriteString(arg440)
		if err442 != nil {
			Usage()
			return
		}
		factory443 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt444 := factory443.GetProtocol(mbTrans441)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err445 := argvalue0.Read(jsProt444)
		if err445 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg446 := flag.Arg(2)
		mbTrans447 := thrift.NewTMemoryBufferLen(len(arg446))
		defer mbTrans447.Close()
		_, err448 := mbTrans447.WriteString(arg446)
		if err448 != nil {
			Usage()
			return
		}
		factory449 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt450 := factory449.GetProtocol(mbTrans447)
		argvalue1 := bidmaster_server.NewAdOrderParams()
		err451 := argvalue1.Read(jsProt450)
		if err451 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdOrdersByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdOrdersByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseAdOrdersByIds requires 3 args")
			flag.Usage()
		}
		arg452 := flag.Arg(1)
		mbTrans453 := thrift.NewTMemoryBufferLen(len(arg452))
		defer mbTrans453.Close()
		_, err454 := mbTrans453.WriteString(arg452)
		if err454 != nil {
			Usage()
			return
		}
		factory455 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt456 := factory455.GetProtocol(mbTrans453)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err457 := argvalue0.Read(jsProt456)
		if err457 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err458 := (strconv.Atoi(flag.Arg(2)))
		if err458 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg459 := flag.Arg(3)
		mbTrans460 := thrift.NewTMemoryBufferLen(len(arg459))
		defer mbTrans460.Close()
		_, err461 := mbTrans460.WriteString(arg459)
		if err461 != nil {
			Usage()
			return
		}
		factory462 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt463 := factory462.GetProtocol(mbTrans460)
		containerStruct2 := bidmaster_server.NewPauseAdOrdersByIdsArgs()
		err464 := containerStruct2.ReadField3(jsProt463)
		if err464 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.PauseAdOrdersByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeAdOrdersByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeAdOrdersByIds requires 3 args")
			flag.Usage()
		}
		arg465 := flag.Arg(1)
		mbTrans466 := thrift.NewTMemoryBufferLen(len(arg465))
		defer mbTrans466.Close()
		_, err467 := mbTrans466.WriteString(arg465)
		if err467 != nil {
			Usage()
			return
		}
		factory468 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt469 := factory468.GetProtocol(mbTrans466)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err470 := argvalue0.Read(jsProt469)
		if err470 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err471 := (strconv.Atoi(flag.Arg(2)))
		if err471 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg472 := flag.Arg(3)
		mbTrans473 := thrift.NewTMemoryBufferLen(len(arg472))
		defer mbTrans473.Close()
		_, err474 := mbTrans473.WriteString(arg472)
		if err474 != nil {
			Usage()
			return
		}
		factory475 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt476 := factory475.GetProtocol(mbTrans473)
		containerStruct2 := bidmaster_server.NewResumeAdOrdersByIdsArgs()
		err477 := containerStruct2.ReadField3(jsProt476)
		if err477 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ResumeAdOrdersByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdOrderByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdOrderByIds requires 3 args")
			flag.Usage()
		}
		arg478 := flag.Arg(1)
		mbTrans479 := thrift.NewTMemoryBufferLen(len(arg478))
		defer mbTrans479.Close()
		_, err480 := mbTrans479.WriteString(arg478)
		if err480 != nil {
			Usage()
			return
		}
		factory481 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt482 := factory481.GetProtocol(mbTrans479)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err483 := argvalue0.Read(jsProt482)
		if err483 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err484 := (strconv.Atoi(flag.Arg(2)))
		if err484 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg485 := flag.Arg(3)
		mbTrans486 := thrift.NewTMemoryBufferLen(len(arg485))
		defer mbTrans486.Close()
		_, err487 := mbTrans486.WriteString(arg485)
		if err487 != nil {
			Usage()
			return
		}
		factory488 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt489 := factory488.GetProtocol(mbTrans486)
		containerStruct2 := bidmaster_server.NewDeleteAdOrderByIdsArgs()
		err490 := containerStruct2.ReadField3(jsProt489)
		if err490 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdOrderByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdPromotion":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdPromotion requires 3 args")
			flag.Usage()
		}
		arg491 := flag.Arg(1)
		mbTrans492 := thrift.NewTMemoryBufferLen(len(arg491))
		defer mbTrans492.Close()
		_, err493 := mbTrans492.WriteString(arg491)
		if err493 != nil {
			Usage()
			return
		}
		factory494 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt495 := factory494.GetProtocol(mbTrans492)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err496 := argvalue0.Read(jsProt495)
		if err496 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err497 := (strconv.Atoi(flag.Arg(2)))
		if err497 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg498 := flag.Arg(3)
		mbTrans499 := thrift.NewTMemoryBufferLen(len(arg498))
		defer mbTrans499.Close()
		_, err500 := mbTrans499.WriteString(arg498)
		if err500 != nil {
			Usage()
			return
		}
		factory501 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt502 := factory501.GetProtocol(mbTrans499)
		argvalue2 := bidmaster_server.NewAdPromotion()
		err503 := argvalue2.Read(jsProt502)
		if err503 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdPromotion(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdPromotion":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdPromotion requires 3 args")
			flag.Usage()
		}
		arg504 := flag.Arg(1)
		mbTrans505 := thrift.NewTMemoryBufferLen(len(arg504))
		defer mbTrans505.Close()
		_, err506 := mbTrans505.WriteString(arg504)
		if err506 != nil {
			Usage()
			return
		}
		factory507 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt508 := factory507.GetProtocol(mbTrans505)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err509 := argvalue0.Read(jsProt508)
		if err509 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err510 := (strconv.Atoi(flag.Arg(2)))
		if err510 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg511 := flag.Arg(3)
		mbTrans512 := thrift.NewTMemoryBufferLen(len(arg511))
		defer mbTrans512.Close()
		_, err513 := mbTrans512.WriteString(arg511)
		if err513 != nil {
			Usage()
			return
		}
		factory514 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt515 := factory514.GetProtocol(mbTrans512)
		argvalue2 := bidmaster_server.NewAdPromotion()
		err516 := argvalue2.Read(jsProt515)
		if err516 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdPromotion(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdPromotionsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdPromotionsByIds requires 2 args")
			flag.Usage()
		}
		arg517 := flag.Arg(1)
		mbTrans518 := thrift.NewTMemoryBufferLen(len(arg517))
		defer mbTrans518.Close()
		_, err519 := mbTrans518.WriteString(arg517)
		if err519 != nil {
			Usage()
			return
		}
		factory520 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt521 := factory520.GetProtocol(mbTrans518)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err522 := argvalue0.Read(jsProt521)
		if err522 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg523 := flag.Arg(2)
		mbTrans524 := thrift.NewTMemoryBufferLen(len(arg523))
		defer mbTrans524.Close()
		_, err525 := mbTrans524.WriteString(arg523)
		if err525 != nil {
			Usage()
			return
		}
		factory526 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt527 := factory526.GetProtocol(mbTrans524)
		containerStruct1 := bidmaster_server.NewGetAdPromotionsByIdsArgs()
		err528 := containerStruct1.ReadField2(jsProt527)
		if err528 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdPromotionsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdPromotionByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdPromotionByParams requires 2 args")
			flag.Usage()
		}
		arg529 := flag.Arg(1)
		mbTrans530 := thrift.NewTMemoryBufferLen(len(arg529))
		defer mbTrans530.Close()
		_, err531 := mbTrans530.WriteString(arg529)
		if err531 != nil {
			Usage()
			return
		}
		factory532 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt533 := factory532.GetProtocol(mbTrans530)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err534 := argvalue0.Read(jsProt533)
		if err534 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg535 := flag.Arg(2)
		mbTrans536 := thrift.NewTMemoryBufferLen(len(arg535))
		defer mbTrans536.Close()
		_, err537 := mbTrans536.WriteString(arg535)
		if err537 != nil {
			Usage()
			return
		}
		factory538 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt539 := factory538.GetProtocol(mbTrans536)
		argvalue1 := bidmaster_server.NewAdPromotionParams()
		err540 := argvalue1.Read(jsProt539)
		if err540 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdPromotionByParams(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdPromotionsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdPromotionsByIds requires 3 args")
			flag.Usage()
		}
		arg541 := flag.Arg(1)
		mbTrans542 := thrift.NewTMemoryBufferLen(len(arg541))
		defer mbTrans542.Close()
		_, err543 := mbTrans542.WriteString(arg541)
		if err543 != nil {
			Usage()
			return
		}
		factory544 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt545 := factory544.GetProtocol(mbTrans542)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err546 := argvalue0.Read(jsProt545)
		if err546 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err547 := (strconv.Atoi(flag.Arg(2)))
		if err547 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg548 := flag.Arg(3)
		mbTrans549 := thrift.NewTMemoryBufferLen(len(arg548))
		defer mbTrans549.Close()
		_, err550 := mbTrans549.WriteString(arg548)
		if err550 != nil {
			Usage()
			return
		}
		factory551 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt552 := factory551.GetProtocol(mbTrans549)
		containerStruct2 := bidmaster_server.NewDeleteAdPromotionsByIdsArgs()
		err553 := containerStruct2.ReadField3(jsProt552)
		if err553 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdPromotionsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdCampaign":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdCampaign requires 3 args")
			flag.Usage()
		}
		arg554 := flag.Arg(1)
		mbTrans555 := thrift.NewTMemoryBufferLen(len(arg554))
		defer mbTrans555.Close()
		_, err556 := mbTrans555.WriteString(arg554)
		if err556 != nil {
			Usage()
			return
		}
		factory557 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt558 := factory557.GetProtocol(mbTrans555)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err559 := argvalue0.Read(jsProt558)
		if err559 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err560 := (strconv.Atoi(flag.Arg(2)))
		if err560 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg561 := flag.Arg(3)
		mbTrans562 := thrift.NewTMemoryBufferLen(len(arg561))
		defer mbTrans562.Close()
		_, err563 := mbTrans562.WriteString(arg561)
		if err563 != nil {
			Usage()
			return
		}
		factory564 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt565 := factory564.GetProtocol(mbTrans562)
		argvalue2 := bidmaster_server.NewAdCampaign()
		err566 := argvalue2.Read(jsProt565)
		if err566 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdCampaign(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdCampaign":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdCampaign requires 3 args")
			flag.Usage()
		}
		arg567 := flag.Arg(1)
		mbTrans568 := thrift.NewTMemoryBufferLen(len(arg567))
		defer mbTrans568.Close()
		_, err569 := mbTrans568.WriteString(arg567)
		if err569 != nil {
			Usage()
			return
		}
		factory570 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt571 := factory570.GetProtocol(mbTrans568)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err572 := argvalue0.Read(jsProt571)
		if err572 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err573 := (strconv.Atoi(flag.Arg(2)))
		if err573 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg574 := flag.Arg(3)
		mbTrans575 := thrift.NewTMemoryBufferLen(len(arg574))
		defer mbTrans575.Close()
		_, err576 := mbTrans575.WriteString(arg574)
		if err576 != nil {
			Usage()
			return
		}
		factory577 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt578 := factory577.GetProtocol(mbTrans575)
		argvalue2 := bidmaster_server.NewAdCampaign()
		err579 := argvalue2.Read(jsProt578)
		if err579 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdCampaign(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdCampaignsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCampaignsByIds requires 2 args")
			flag.Usage()
		}
		arg580 := flag.Arg(1)
		mbTrans581 := thrift.NewTMemoryBufferLen(len(arg580))
		defer mbTrans581.Close()
		_, err582 := mbTrans581.WriteString(arg580)
		if err582 != nil {
			Usage()
			return
		}
		factory583 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt584 := factory583.GetProtocol(mbTrans581)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err585 := argvalue0.Read(jsProt584)
		if err585 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg586 := flag.Arg(2)
		mbTrans587 := thrift.NewTMemoryBufferLen(len(arg586))
		defer mbTrans587.Close()
		_, err588 := mbTrans587.WriteString(arg586)
		if err588 != nil {
			Usage()
			return
		}
		factory589 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt590 := factory589.GetProtocol(mbTrans587)
		containerStruct1 := bidmaster_server.NewGetAdCampaignsByIdsArgs()
		err591 := containerStruct1.ReadField2(jsProt590)
		if err591 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdCampaignsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdCampaignByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdCampaignByParams requires 2 args")
			flag.Usage()
		}
		arg592 := flag.Arg(1)
		mbTrans593 := thrift.NewTMemoryBufferLen(len(arg592))
		defer mbTrans593.Close()
		_, err594 := mbTrans593.WriteString(arg592)
		if err594 != nil {
			Usage()
			return
		}
		factory595 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt596 := factory595.GetProtocol(mbTrans593)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err597 := argvalue0.Read(jsProt596)
		if err597 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg598 := flag.Arg(2)
		mbTrans599 := thrift.NewTMemoryBufferLen(len(arg598))
		defer mbTrans599.Close()
		_, err600 := mbTrans599.WriteString(arg598)
		if err600 != nil {
			Usage()
			return
		}
		factory601 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt602 := factory601.GetProtocol(mbTrans599)
		argvalue1 := bidmaster_server.NewAdCampaignParams()
		err603 := argvalue1.Read(jsProt602)
		if err603 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdCampaignByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "PauseAdCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg604 := flag.Arg(1)
		mbTrans605 := thrift.NewTMemoryBufferLen(len(arg604))
		defer mbTrans605.Close()
		_, err606 := mbTrans605.WriteString(arg604)
		if err606 != nil {
			Usage()
			return
		}
		factory607 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt608 := factory607.GetProtocol(mbTrans605)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err609 := argvalue0.Read(jsProt608)
		if err609 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err610 := (strconv.Atoi(flag.Arg(2)))
		if err610 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg611 := flag.Arg(3)
		mbTrans612 := thrift.NewTMemoryBufferLen(len(arg611))
		defer mbTrans612.Close()
		_, err613 := mbTrans612.WriteString(arg611)
		if err613 != nil {
			Usage()
			return
		}
		factory614 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt615 := factory614.GetProtocol(mbTrans612)
		containerStruct2 := bidmaster_server.NewPauseAdCampaignsByIdsArgs()
		err616 := containerStruct2.ReadField3(jsProt615)
		if err616 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.PauseAdCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "resumeAdCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "ResumeAdCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg617 := flag.Arg(1)
		mbTrans618 := thrift.NewTMemoryBufferLen(len(arg617))
		defer mbTrans618.Close()
		_, err619 := mbTrans618.WriteString(arg617)
		if err619 != nil {
			Usage()
			return
		}
		factory620 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt621 := factory620.GetProtocol(mbTrans618)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err622 := argvalue0.Read(jsProt621)
		if err622 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err623 := (strconv.Atoi(flag.Arg(2)))
		if err623 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg624 := flag.Arg(3)
		mbTrans625 := thrift.NewTMemoryBufferLen(len(arg624))
		defer mbTrans625.Close()
		_, err626 := mbTrans625.WriteString(arg624)
		if err626 != nil {
			Usage()
			return
		}
		factory627 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt628 := factory627.GetProtocol(mbTrans625)
		containerStruct2 := bidmaster_server.NewResumeAdCampaignsByIdsArgs()
		err629 := containerStruct2.ReadField3(jsProt628)
		if err629 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.ResumeAdCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdCampaignsByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdCampaignsByIds requires 3 args")
			flag.Usage()
		}
		arg630 := flag.Arg(1)
		mbTrans631 := thrift.NewTMemoryBufferLen(len(arg630))
		defer mbTrans631.Close()
		_, err632 := mbTrans631.WriteString(arg630)
		if err632 != nil {
			Usage()
			return
		}
		factory633 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt634 := factory633.GetProtocol(mbTrans631)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err635 := argvalue0.Read(jsProt634)
		if err635 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err636 := (strconv.Atoi(flag.Arg(2)))
		if err636 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg637 := flag.Arg(3)
		mbTrans638 := thrift.NewTMemoryBufferLen(len(arg637))
		defer mbTrans638.Close()
		_, err639 := mbTrans638.WriteString(arg637)
		if err639 != nil {
			Usage()
			return
		}
		factory640 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt641 := factory640.GetProtocol(mbTrans638)
		containerStruct2 := bidmaster_server.NewDeleteAdCampaignsByIdsArgs()
		err642 := containerStruct2.ReadField3(jsProt641)
		if err642 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdCampaignsByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editCampaignActualDailyBudget":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCampaignActualDailyBudget requires 3 args")
			flag.Usage()
		}
		arg643 := flag.Arg(1)
		mbTrans644 := thrift.NewTMemoryBufferLen(len(arg643))
		defer mbTrans644.Close()
		_, err645 := mbTrans644.WriteString(arg643)
		if err645 != nil {
			Usage()
			return
		}
		factory646 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt647 := factory646.GetProtocol(mbTrans644)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err648 := argvalue0.Read(jsProt647)
		if err648 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err649 := (strconv.Atoi(flag.Arg(2)))
		if err649 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err650 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err650 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditCampaignActualDailyBudget(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdStrategy requires 3 args")
			flag.Usage()
		}
		arg651 := flag.Arg(1)
		mbTrans652 := thrift.NewTMemoryBufferLen(len(arg651))
		defer mbTrans652.Close()
		_, err653 := mbTrans652.WriteString(arg651)
		if err653 != nil {
			Usage()
			return
		}
		factory654 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt655 := factory654.GetProtocol(mbTrans652)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err656 := argvalue0.Read(jsProt655)
		if err656 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err657 := (strconv.Atoi(flag.Arg(2)))
		if err657 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg658 := flag.Arg(3)
		mbTrans659 := thrift.NewTMemoryBufferLen(len(arg658))
		defer mbTrans659.Close()
		_, err660 := mbTrans659.WriteString(arg658)
		if err660 != nil {
			Usage()
			return
		}
		factory661 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt662 := factory661.GetProtocol(mbTrans659)
		argvalue2 := bidmaster_server.NewAdStrategy()
		err663 := argvalue2.Read(jsProt662)
		if err663 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdStrategy":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdStrategy requires 3 args")
			flag.Usage()
		}
		arg664 := flag.Arg(1)
		mbTrans665 := thrift.NewTMemoryBufferLen(len(arg664))
		defer mbTrans665.Close()
		_, err666 := mbTrans665.WriteString(arg664)
		if err666 != nil {
			Usage()
			return
		}
		factory667 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt668 := factory667.GetProtocol(mbTrans665)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err669 := argvalue0.Read(jsProt668)
		if err669 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err670 := (strconv.Atoi(flag.Arg(2)))
		if err670 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg671 := flag.Arg(3)
		mbTrans672 := thrift.NewTMemoryBufferLen(len(arg671))
		defer mbTrans672.Close()
		_, err673 := mbTrans672.WriteString(arg671)
		if err673 != nil {
			Usage()
			return
		}
		factory674 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt675 := factory674.GetProtocol(mbTrans672)
		argvalue2 := bidmaster_server.NewAdStrategy()
		err676 := argvalue2.Read(jsProt675)
		if err676 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdStrategy(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdStrategiesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdStrategiesByIds requires 2 args")
			flag.Usage()
		}
		arg677 := flag.Arg(1)
		mbTrans678 := thrift.NewTMemoryBufferLen(len(arg677))
		defer mbTrans678.Close()
		_, err679 := mbTrans678.WriteString(arg677)
		if err679 != nil {
			Usage()
			return
		}
		factory680 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt681 := factory680.GetProtocol(mbTrans678)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err682 := argvalue0.Read(jsProt681)
		if err682 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg683 := flag.Arg(2)
		mbTrans684 := thrift.NewTMemoryBufferLen(len(arg683))
		defer mbTrans684.Close()
		_, err685 := mbTrans684.WriteString(arg683)
		if err685 != nil {
			Usage()
			return
		}
		factory686 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt687 := factory686.GetProtocol(mbTrans684)
		containerStruct1 := bidmaster_server.NewGetAdStrategiesByIdsArgs()
		err688 := containerStruct1.ReadField2(jsProt687)
		if err688 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdStrategiesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdStrategyByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdStrategyByParams requires 2 args")
			flag.Usage()
		}
		arg689 := flag.Arg(1)
		mbTrans690 := thrift.NewTMemoryBufferLen(len(arg689))
		defer mbTrans690.Close()
		_, err691 := mbTrans690.WriteString(arg689)
		if err691 != nil {
			Usage()
			return
		}
		factory692 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt693 := factory692.GetProtocol(mbTrans690)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err694 := argvalue0.Read(jsProt693)
		if err694 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg695 := flag.Arg(2)
		mbTrans696 := thrift.NewTMemoryBufferLen(len(arg695))
		defer mbTrans696.Close()
		_, err697 := mbTrans696.WriteString(arg695)
		if err697 != nil {
			Usage()
			return
		}
		factory698 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt699 := factory698.GetProtocol(mbTrans696)
		argvalue1 := bidmaster_server.NewAdStrategyParams()
		err700 := argvalue1.Read(jsProt699)
		if err700 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdStrategyByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdStrategiesByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "PauseAdStrategiesByIds requires 4 args")
			flag.Usage()
		}
		arg701 := flag.Arg(1)
		mbTrans702 := thrift.NewTMemoryBufferLen(len(arg701))
		defer mbTrans702.Close()
		_, err703 := mbTrans702.WriteString(arg701)
		if err703 != nil {
			Usage()
			return
		}
		factory704 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt705 := factory704.GetProtocol(mbTrans702)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err706 := argvalue0.Read(jsProt705)
		if err706 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err707 := (strconv.Atoi(flag.Arg(2)))
		if err707 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err708 := (strconv.Atoi(flag.Arg(3)))
		if err708 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg709 := flag.Arg(4)
		mbTrans710 := thrift.NewTMemoryBufferLen(len(arg709))
		defer mbTrans710.Close()
		_, err711 := mbTrans710.WriteString(arg709)
		if err711 != nil {
			Usage()
			return
		}
		factory712 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt713 := factory712.GetProtocol(mbTrans710)
		containerStruct3 := bidmaster_server.NewPauseAdStrategiesByIdsArgs()
		err714 := containerStruct3.ReadField4(jsProt713)
		if err714 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.PauseAdStrategiesByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "resumeAdStrategiesByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "ResumeAdStrategiesByIds requires 4 args")
			flag.Usage()
		}
		arg715 := flag.Arg(1)
		mbTrans716 := thrift.NewTMemoryBufferLen(len(arg715))
		defer mbTrans716.Close()
		_, err717 := mbTrans716.WriteString(arg715)
		if err717 != nil {
			Usage()
			return
		}
		factory718 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt719 := factory718.GetProtocol(mbTrans716)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err720 := argvalue0.Read(jsProt719)
		if err720 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err721 := (strconv.Atoi(flag.Arg(2)))
		if err721 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err722 := (strconv.Atoi(flag.Arg(3)))
		if err722 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg723 := flag.Arg(4)
		mbTrans724 := thrift.NewTMemoryBufferLen(len(arg723))
		defer mbTrans724.Close()
		_, err725 := mbTrans724.WriteString(arg723)
		if err725 != nil {
			Usage()
			return
		}
		factory726 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt727 := factory726.GetProtocol(mbTrans724)
		containerStruct3 := bidmaster_server.NewResumeAdStrategiesByIdsArgs()
		err728 := containerStruct3.ReadField4(jsProt727)
		if err728 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.ResumeAdStrategiesByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "deleteAdStrategiesByIds":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "DeleteAdStrategiesByIds requires 4 args")
			flag.Usage()
		}
		arg729 := flag.Arg(1)
		mbTrans730 := thrift.NewTMemoryBufferLen(len(arg729))
		defer mbTrans730.Close()
		_, err731 := mbTrans730.WriteString(arg729)
		if err731 != nil {
			Usage()
			return
		}
		factory732 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt733 := factory732.GetProtocol(mbTrans730)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err734 := argvalue0.Read(jsProt733)
		if err734 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err735 := (strconv.Atoi(flag.Arg(2)))
		if err735 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err736 := (strconv.Atoi(flag.Arg(3)))
		if err736 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		arg737 := flag.Arg(4)
		mbTrans738 := thrift.NewTMemoryBufferLen(len(arg737))
		defer mbTrans738.Close()
		_, err739 := mbTrans738.WriteString(arg737)
		if err739 != nil {
			Usage()
			return
		}
		factory740 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt741 := factory740.GetProtocol(mbTrans738)
		containerStruct3 := bidmaster_server.NewDeleteAdStrategiesByIdsArgs()
		err742 := containerStruct3.ReadField4(jsProt741)
		if err742 != nil {
			Usage()
			return
		}
		argvalue3 := containerStruct3.Ids
		value3 := argvalue3
		fmt.Print(client.DeleteAdStrategiesByIds(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "addAdCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdCreative requires 3 args")
			flag.Usage()
		}
		arg743 := flag.Arg(1)
		mbTrans744 := thrift.NewTMemoryBufferLen(len(arg743))
		defer mbTrans744.Close()
		_, err745 := mbTrans744.WriteString(arg743)
		if err745 != nil {
			Usage()
			return
		}
		factory746 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt747 := factory746.GetProtocol(mbTrans744)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err748 := argvalue0.Read(jsProt747)
		if err748 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err749 := (strconv.Atoi(flag.Arg(2)))
		if err749 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg750 := flag.Arg(3)
		mbTrans751 := thrift.NewTMemoryBufferLen(len(arg750))
		defer mbTrans751.Close()
		_, err752 := mbTrans751.WriteString(arg750)
		if err752 != nil {
			Usage()
			return
		}
		factory753 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt754 := factory753.GetProtocol(mbTrans751)
		argvalue2 := bidmaster_server.NewAdCreative()
		err755 := argvalue2.Read(jsProt754)
		if err755 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdCreative":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdCreative requires 3 args")
			flag.Usage()
		}
		arg756 := flag.Arg(1)
		mbTrans757 := thrift.NewTMemoryBufferLen(len(arg756))
		defer mbTrans757.Close()
		_, err758 := mbTrans757.WriteString(arg756)
		if err758 != nil {
			Usage()
			return
		}
		factory759 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt760 := factory759.GetProtocol(mbTrans757)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err761 := argvalue0.Read(jsProt760)
		if err761 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err762 := (strconv.Atoi(flag.Arg(2)))
		if err762 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg763 := flag.Arg(3)
		mbTrans764 := thrift.NewTMemoryBufferLen(len(arg763))
		defer mbTrans764.Close()
		_, err765 := mbTrans764.WriteString(arg763)
		if err765 != nil {
			Usage()
			return
		}
		factory766 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt767 := factory766.GetProtocol(mbTrans764)
		argvalue2 := bidmaster_server.NewAdCreative()
		err768 := argvalue2.Read(jsProt767)
		if err768 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdCreative(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdCreativesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdCreativesByIds requires 2 args")
			flag.Usage()
		}
		arg769 := flag.Arg(1)
		mbTrans770 := thrift.NewTMemoryBufferLen(len(arg769))
		defer mbTrans770.Close()
		_, err771 := mbTrans770.WriteString(arg769)
		if err771 != nil {
			Usage()
			return
		}
		factory772 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt773 := factory772.GetProtocol(mbTrans770)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err774 := argvalue0.Read(jsProt773)
		if err774 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg775 := flag.Arg(2)
		mbTrans776 := thrift.NewTMemoryBufferLen(len(arg775))
		defer mbTrans776.Close()
		_, err777 := mbTrans776.WriteString(arg775)
		if err777 != nil {
			Usage()
			return
		}
		factory778 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt779 := factory778.GetProtocol(mbTrans776)
		containerStruct1 := bidmaster_server.NewGetAdCreativesByIdsArgs()
		err780 := containerStruct1.ReadField2(jsProt779)
		if err780 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdCreativesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdCreativeByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdCreativeByParams requires 2 args")
			flag.Usage()
		}
		arg781 := flag.Arg(1)
		mbTrans782 := thrift.NewTMemoryBufferLen(len(arg781))
		defer mbTrans782.Close()
		_, err783 := mbTrans782.WriteString(arg781)
		if err783 != nil {
			Usage()
			return
		}
		factory784 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt785 := factory784.GetProtocol(mbTrans782)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err786 := argvalue0.Read(jsProt785)
		if err786 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg787 := flag.Arg(2)
		mbTrans788 := thrift.NewTMemoryBufferLen(len(arg787))
		defer mbTrans788.Close()
		_, err789 := mbTrans788.WriteString(arg787)
		if err789 != nil {
			Usage()
			return
		}
		factory790 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt791 := factory790.GetProtocol(mbTrans788)
		argvalue1 := bidmaster_server.NewAdCreativeParams()
		err792 := argvalue1.Read(jsProt791)
		if err792 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdCreativeByParams(value0, value1))
		fmt.Print("\n")
		break
	case "pauseAdCreativesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "PauseAdCreativesByIds requires 5 args")
			flag.Usage()
		}
		arg793 := flag.Arg(1)
		mbTrans794 := thrift.NewTMemoryBufferLen(len(arg793))
		defer mbTrans794.Close()
		_, err795 := mbTrans794.WriteString(arg793)
		if err795 != nil {
			Usage()
			return
		}
		factory796 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt797 := factory796.GetProtocol(mbTrans794)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err798 := argvalue0.Read(jsProt797)
		if err798 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err799 := (strconv.Atoi(flag.Arg(2)))
		if err799 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err800 := (strconv.Atoi(flag.Arg(3)))
		if err800 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err801 := (strconv.Atoi(flag.Arg(4)))
		if err801 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg802 := flag.Arg(5)
		mbTrans803 := thrift.NewTMemoryBufferLen(len(arg802))
		defer mbTrans803.Close()
		_, err804 := mbTrans803.WriteString(arg802)
		if err804 != nil {
			Usage()
			return
		}
		factory805 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt806 := factory805.GetProtocol(mbTrans803)
		containerStruct4 := bidmaster_server.NewPauseAdCreativesByIdsArgs()
		err807 := containerStruct4.ReadField5(jsProt806)
		if err807 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.PauseAdCreativesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "resumeAdCreativesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "ResumeAdCreativesByIds requires 5 args")
			flag.Usage()
		}
		arg808 := flag.Arg(1)
		mbTrans809 := thrift.NewTMemoryBufferLen(len(arg808))
		defer mbTrans809.Close()
		_, err810 := mbTrans809.WriteString(arg808)
		if err810 != nil {
			Usage()
			return
		}
		factory811 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt812 := factory811.GetProtocol(mbTrans809)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err813 := argvalue0.Read(jsProt812)
		if err813 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err814 := (strconv.Atoi(flag.Arg(2)))
		if err814 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err815 := (strconv.Atoi(flag.Arg(3)))
		if err815 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err816 := (strconv.Atoi(flag.Arg(4)))
		if err816 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg817 := flag.Arg(5)
		mbTrans818 := thrift.NewTMemoryBufferLen(len(arg817))
		defer mbTrans818.Close()
		_, err819 := mbTrans818.WriteString(arg817)
		if err819 != nil {
			Usage()
			return
		}
		factory820 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt821 := factory820.GetProtocol(mbTrans818)
		containerStruct4 := bidmaster_server.NewResumeAdCreativesByIdsArgs()
		err822 := containerStruct4.ReadField5(jsProt821)
		if err822 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.ResumeAdCreativesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "deleteAdCreativesByIds":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DeleteAdCreativesByIds requires 5 args")
			flag.Usage()
		}
		arg823 := flag.Arg(1)
		mbTrans824 := thrift.NewTMemoryBufferLen(len(arg823))
		defer mbTrans824.Close()
		_, err825 := mbTrans824.WriteString(arg823)
		if err825 != nil {
			Usage()
			return
		}
		factory826 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt827 := factory826.GetProtocol(mbTrans824)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err828 := argvalue0.Read(jsProt827)
		if err828 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err829 := (strconv.Atoi(flag.Arg(2)))
		if err829 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err830 := (strconv.Atoi(flag.Arg(3)))
		if err830 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err831 := (strconv.Atoi(flag.Arg(4)))
		if err831 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		arg832 := flag.Arg(5)
		mbTrans833 := thrift.NewTMemoryBufferLen(len(arg832))
		defer mbTrans833.Close()
		_, err834 := mbTrans833.WriteString(arg832)
		if err834 != nil {
			Usage()
			return
		}
		factory835 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt836 := factory835.GetProtocol(mbTrans833)
		containerStruct4 := bidmaster_server.NewDeleteAdCreativesByIdsArgs()
		err837 := containerStruct4.ReadField5(jsProt836)
		if err837 != nil {
			Usage()
			return
		}
		argvalue4 := containerStruct4.Ids
		value4 := argvalue4
		fmt.Print(client.DeleteAdCreativesByIds(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "innerApproveAdCreatives":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "InnerApproveAdCreatives requires 3 args")
			flag.Usage()
		}
		arg838 := flag.Arg(1)
		mbTrans839 := thrift.NewTMemoryBufferLen(len(arg838))
		defer mbTrans839.Close()
		_, err840 := mbTrans839.WriteString(arg838)
		if err840 != nil {
			Usage()
			return
		}
		factory841 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt842 := factory841.GetProtocol(mbTrans839)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err843 := argvalue0.Read(jsProt842)
		if err843 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err844 := (strconv.Atoi(flag.Arg(2)))
		if err844 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg845 := flag.Arg(3)
		mbTrans846 := thrift.NewTMemoryBufferLen(len(arg845))
		defer mbTrans846.Close()
		_, err847 := mbTrans846.WriteString(arg845)
		if err847 != nil {
			Usage()
			return
		}
		factory848 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt849 := factory848.GetProtocol(mbTrans846)
		containerStruct2 := bidmaster_server.NewInnerApproveAdCreativesArgs()
		err850 := containerStruct2.ReadField3(jsProt849)
		if err850 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.ApproveInfo
		value2 := argvalue2
		fmt.Print(client.InnerApproveAdCreatives(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editCampaignMinProfitRatio":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditCampaignMinProfitRatio requires 3 args")
			flag.Usage()
		}
		arg851 := flag.Arg(1)
		mbTrans852 := thrift.NewTMemoryBufferLen(len(arg851))
		defer mbTrans852.Close()
		_, err853 := mbTrans852.WriteString(arg851)
		if err853 != nil {
			Usage()
			return
		}
		factory854 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt855 := factory854.GetProtocol(mbTrans852)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err856 := argvalue0.Read(jsProt855)
		if err856 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg857 := flag.Arg(2)
		mbTrans858 := thrift.NewTMemoryBufferLen(len(arg857))
		defer mbTrans858.Close()
		_, err859 := mbTrans858.WriteString(arg857)
		if err859 != nil {
			Usage()
			return
		}
		factory860 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt861 := factory860.GetProtocol(mbTrans858)
		containerStruct1 := bidmaster_server.NewEditCampaignMinProfitRatioArgs()
		err862 := containerStruct1.ReadField2(jsProt861)
		if err862 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.CampaignIds
		value1 := argvalue1
		tmp2, err863 := (strconv.Atoi(flag.Arg(3)))
		if err863 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.EditCampaignMinProfitRatio(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getOrderIdsByProjectIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetOrderIdsByProjectIds requires 2 args")
			flag.Usage()
		}
		arg864 := flag.Arg(1)
		mbTrans865 := thrift.NewTMemoryBufferLen(len(arg864))
		defer mbTrans865.Close()
		_, err866 := mbTrans865.WriteString(arg864)
		if err866 != nil {
			Usage()
			return
		}
		factory867 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt868 := factory867.GetProtocol(mbTrans865)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err869 := argvalue0.Read(jsProt868)
		if err869 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg870 := flag.Arg(2)
		mbTrans871 := thrift.NewTMemoryBufferLen(len(arg870))
		defer mbTrans871.Close()
		_, err872 := mbTrans871.WriteString(arg870)
		if err872 != nil {
			Usage()
			return
		}
		factory873 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt874 := factory873.GetProtocol(mbTrans871)
		containerStruct1 := bidmaster_server.NewGetOrderIdsByProjectIdsArgs()
		err875 := containerStruct1.ReadField2(jsProt874)
		if err875 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.ProjectIds
		value1 := argvalue1
		fmt.Print(client.GetOrderIdsByProjectIds(value0, value1))
		fmt.Print("\n")
		break
	case "debugAdTracking":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "DebugAdTracking requires 5 args")
			flag.Usage()
		}
		arg876 := flag.Arg(1)
		mbTrans877 := thrift.NewTMemoryBufferLen(len(arg876))
		defer mbTrans877.Close()
		_, err878 := mbTrans877.WriteString(arg876)
		if err878 != nil {
			Usage()
			return
		}
		factory879 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt880 := factory879.GetProtocol(mbTrans877)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err881 := argvalue0.Read(jsProt880)
		if err881 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err882 := (strconv.Atoi(flag.Arg(2)))
		if err882 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err883 := (strconv.Atoi(flag.Arg(3)))
		if err883 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3 := flag.Arg(4)
		value3 := argvalue3
		tmp4, err885 := (strconv.Atoi(flag.Arg(5)))
		if err885 != nil {
			Usage()
			return
		}
		argvalue4 := int32(tmp4)
		value4 := argvalue4
		fmt.Print(client.DebugAdTracking(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "addAdExchangeDmp":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "AddAdExchangeDmp requires 3 args")
			flag.Usage()
		}
		arg886 := flag.Arg(1)
		mbTrans887 := thrift.NewTMemoryBufferLen(len(arg886))
		defer mbTrans887.Close()
		_, err888 := mbTrans887.WriteString(arg886)
		if err888 != nil {
			Usage()
			return
		}
		factory889 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt890 := factory889.GetProtocol(mbTrans887)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err891 := argvalue0.Read(jsProt890)
		if err891 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err892 := (strconv.Atoi(flag.Arg(2)))
		if err892 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg893 := flag.Arg(3)
		mbTrans894 := thrift.NewTMemoryBufferLen(len(arg893))
		defer mbTrans894.Close()
		_, err895 := mbTrans894.WriteString(arg893)
		if err895 != nil {
			Usage()
			return
		}
		factory896 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt897 := factory896.GetProtocol(mbTrans894)
		argvalue2 := bidmaster_server.NewAdExchangeDmp()
		err898 := argvalue2.Read(jsProt897)
		if err898 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.AddAdExchangeDmp(value0, value1, value2))
		fmt.Print("\n")
		break
	case "editAdExchangeDmp":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "EditAdExchangeDmp requires 3 args")
			flag.Usage()
		}
		arg899 := flag.Arg(1)
		mbTrans900 := thrift.NewTMemoryBufferLen(len(arg899))
		defer mbTrans900.Close()
		_, err901 := mbTrans900.WriteString(arg899)
		if err901 != nil {
			Usage()
			return
		}
		factory902 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt903 := factory902.GetProtocol(mbTrans900)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err904 := argvalue0.Read(jsProt903)
		if err904 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err905 := (strconv.Atoi(flag.Arg(2)))
		if err905 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg906 := flag.Arg(3)
		mbTrans907 := thrift.NewTMemoryBufferLen(len(arg906))
		defer mbTrans907.Close()
		_, err908 := mbTrans907.WriteString(arg906)
		if err908 != nil {
			Usage()
			return
		}
		factory909 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt910 := factory909.GetProtocol(mbTrans907)
		argvalue2 := bidmaster_server.NewAdExchangeDmp()
		err911 := argvalue2.Read(jsProt910)
		if err911 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		fmt.Print(client.EditAdExchangeDmp(value0, value1, value2))
		fmt.Print("\n")
		break
	case "deleteAdExchangeDmpByIds":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "DeleteAdExchangeDmpByIds requires 3 args")
			flag.Usage()
		}
		arg912 := flag.Arg(1)
		mbTrans913 := thrift.NewTMemoryBufferLen(len(arg912))
		defer mbTrans913.Close()
		_, err914 := mbTrans913.WriteString(arg912)
		if err914 != nil {
			Usage()
			return
		}
		factory915 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt916 := factory915.GetProtocol(mbTrans913)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err917 := argvalue0.Read(jsProt916)
		if err917 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err918 := (strconv.Atoi(flag.Arg(2)))
		if err918 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		arg919 := flag.Arg(3)
		mbTrans920 := thrift.NewTMemoryBufferLen(len(arg919))
		defer mbTrans920.Close()
		_, err921 := mbTrans920.WriteString(arg919)
		if err921 != nil {
			Usage()
			return
		}
		factory922 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt923 := factory922.GetProtocol(mbTrans920)
		containerStruct2 := bidmaster_server.NewDeleteAdExchangeDmpByIdsArgs()
		err924 := containerStruct2.ReadField3(jsProt923)
		if err924 != nil {
			Usage()
			return
		}
		argvalue2 := containerStruct2.Ids
		value2 := argvalue2
		fmt.Print(client.DeleteAdExchangeDmpByIds(value0, value1, value2))
		fmt.Print("\n")
		break
	case "getAdExchangeDmpByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdExchangeDmpByIds requires 2 args")
			flag.Usage()
		}
		arg925 := flag.Arg(1)
		mbTrans926 := thrift.NewTMemoryBufferLen(len(arg925))
		defer mbTrans926.Close()
		_, err927 := mbTrans926.WriteString(arg925)
		if err927 != nil {
			Usage()
			return
		}
		factory928 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt929 := factory928.GetProtocol(mbTrans926)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err930 := argvalue0.Read(jsProt929)
		if err930 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg931 := flag.Arg(2)
		mbTrans932 := thrift.NewTMemoryBufferLen(len(arg931))
		defer mbTrans932.Close()
		_, err933 := mbTrans932.WriteString(arg931)
		if err933 != nil {
			Usage()
			return
		}
		factory934 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt935 := factory934.GetProtocol(mbTrans932)
		containerStruct1 := bidmaster_server.NewGetAdExchangeDmpByIdsArgs()
		err936 := containerStruct1.ReadField2(jsProt935)
		if err936 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdExchangeDmpByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdExchangeDmpByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdExchangeDmpByParams requires 2 args")
			flag.Usage()
		}
		arg937 := flag.Arg(1)
		mbTrans938 := thrift.NewTMemoryBufferLen(len(arg937))
		defer mbTrans938.Close()
		_, err939 := mbTrans938.WriteString(arg937)
		if err939 != nil {
			Usage()
			return
		}
		factory940 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt941 := factory940.GetProtocol(mbTrans938)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err942 := argvalue0.Read(jsProt941)
		if err942 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg943 := flag.Arg(2)
		mbTrans944 := thrift.NewTMemoryBufferLen(len(arg943))
		defer mbTrans944.Close()
		_, err945 := mbTrans944.WriteString(arg943)
		if err945 != nil {
			Usage()
			return
		}
		factory946 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt947 := factory946.GetProtocol(mbTrans944)
		argvalue1 := bidmaster_server.NewAdExchangeDmpParams()
		err948 := argvalue1.Read(jsProt947)
		if err948 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdExchangeDmpByParams(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdSuggestPrice":
		if flag.NArg()-1 != 3 {
			fmt.Fprintln(os.Stderr, "SearchAdSuggestPrice requires 3 args")
			flag.Usage()
		}
		arg949 := flag.Arg(1)
		mbTrans950 := thrift.NewTMemoryBufferLen(len(arg949))
		defer mbTrans950.Close()
		_, err951 := mbTrans950.WriteString(arg949)
		if err951 != nil {
			Usage()
			return
		}
		factory952 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt953 := factory952.GetProtocol(mbTrans950)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err954 := argvalue0.Read(jsProt953)
		if err954 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err955 := (strconv.Atoi(flag.Arg(2)))
		if err955 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err956 := (strconv.Atoi(flag.Arg(3)))
		if err956 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		fmt.Print(client.SearchAdSuggestPrice(value0, value1, value2))
		fmt.Print("\n")
		break
	case "addAdTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddAdTracking requires 2 args")
			flag.Usage()
		}
		arg957 := flag.Arg(1)
		mbTrans958 := thrift.NewTMemoryBufferLen(len(arg957))
		defer mbTrans958.Close()
		_, err959 := mbTrans958.WriteString(arg957)
		if err959 != nil {
			Usage()
			return
		}
		factory960 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt961 := factory960.GetProtocol(mbTrans958)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err962 := argvalue0.Read(jsProt961)
		if err962 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg963 := flag.Arg(2)
		mbTrans964 := thrift.NewTMemoryBufferLen(len(arg963))
		defer mbTrans964.Close()
		_, err965 := mbTrans964.WriteString(arg963)
		if err965 != nil {
			Usage()
			return
		}
		factory966 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt967 := factory966.GetProtocol(mbTrans964)
		argvalue1 := bidmaster_server.NewAdTracking()
		err968 := argvalue1.Read(jsProt967)
		if err968 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddAdTracking(value0, value1))
		fmt.Print("\n")
		break
	case "editAdTracking":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "EditAdTracking requires 2 args")
			flag.Usage()
		}
		arg969 := flag.Arg(1)
		mbTrans970 := thrift.NewTMemoryBufferLen(len(arg969))
		defer mbTrans970.Close()
		_, err971 := mbTrans970.WriteString(arg969)
		if err971 != nil {
			Usage()
			return
		}
		factory972 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt973 := factory972.GetProtocol(mbTrans970)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err974 := argvalue0.Read(jsProt973)
		if err974 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg975 := flag.Arg(2)
		mbTrans976 := thrift.NewTMemoryBufferLen(len(arg975))
		defer mbTrans976.Close()
		_, err977 := mbTrans976.WriteString(arg975)
		if err977 != nil {
			Usage()
			return
		}
		factory978 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt979 := factory978.GetProtocol(mbTrans976)
		argvalue1 := bidmaster_server.NewAdTracking()
		err980 := argvalue1.Read(jsProt979)
		if err980 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.EditAdTracking(value0, value1))
		fmt.Print("\n")
		break
	case "getAdTrackingsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAdTrackingsByIds requires 2 args")
			flag.Usage()
		}
		arg981 := flag.Arg(1)
		mbTrans982 := thrift.NewTMemoryBufferLen(len(arg981))
		defer mbTrans982.Close()
		_, err983 := mbTrans982.WriteString(arg981)
		if err983 != nil {
			Usage()
			return
		}
		factory984 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt985 := factory984.GetProtocol(mbTrans982)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err986 := argvalue0.Read(jsProt985)
		if err986 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg987 := flag.Arg(2)
		mbTrans988 := thrift.NewTMemoryBufferLen(len(arg987))
		defer mbTrans988.Close()
		_, err989 := mbTrans988.WriteString(arg987)
		if err989 != nil {
			Usage()
			return
		}
		factory990 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt991 := factory990.GetProtocol(mbTrans988)
		containerStruct1 := bidmaster_server.NewGetAdTrackingsByIdsArgs()
		err992 := containerStruct1.ReadField2(jsProt991)
		if err992 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAdTrackingsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "searchAdTrackingByParams":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "SearchAdTrackingByParams requires 2 args")
			flag.Usage()
		}
		arg993 := flag.Arg(1)
		mbTrans994 := thrift.NewTMemoryBufferLen(len(arg993))
		defer mbTrans994.Close()
		_, err995 := mbTrans994.WriteString(arg993)
		if err995 != nil {
			Usage()
			return
		}
		factory996 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt997 := factory996.GetProtocol(mbTrans994)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err998 := argvalue0.Read(jsProt997)
		if err998 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg999 := flag.Arg(2)
		mbTrans1000 := thrift.NewTMemoryBufferLen(len(arg999))
		defer mbTrans1000.Close()
		_, err1001 := mbTrans1000.WriteString(arg999)
		if err1001 != nil {
			Usage()
			return
		}
		factory1002 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1003 := factory1002.GetProtocol(mbTrans1000)
		argvalue1 := bidmaster_server.NewAdTrackingParams()
		err1004 := argvalue1.Read(jsProt1003)
		if err1004 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.SearchAdTrackingByParams(value0, value1))
		fmt.Print("\n")
		break
	case "deleteAdTrackingByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "DeleteAdTrackingByIds requires 2 args")
			flag.Usage()
		}
		arg1005 := flag.Arg(1)
		mbTrans1006 := thrift.NewTMemoryBufferLen(len(arg1005))
		defer mbTrans1006.Close()
		_, err1007 := mbTrans1006.WriteString(arg1005)
		if err1007 != nil {
			Usage()
			return
		}
		factory1008 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1009 := factory1008.GetProtocol(mbTrans1006)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1010 := argvalue0.Read(jsProt1009)
		if err1010 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1011 := flag.Arg(2)
		mbTrans1012 := thrift.NewTMemoryBufferLen(len(arg1011))
		defer mbTrans1012.Close()
		_, err1013 := mbTrans1012.WriteString(arg1011)
		if err1013 != nil {
			Usage()
			return
		}
		factory1014 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1015 := factory1014.GetProtocol(mbTrans1012)
		containerStruct1 := bidmaster_server.NewDeleteAdTrackingByIdsArgs()
		err1016 := containerStruct1.ReadField2(jsProt1015)
		if err1016 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.DeleteAdTrackingByIds(value0, value1))
		fmt.Print("\n")
		break
	case "addDeviceFile":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "AddDeviceFile requires 2 args")
			flag.Usage()
		}
		arg1017 := flag.Arg(1)
		mbTrans1018 := thrift.NewTMemoryBufferLen(len(arg1017))
		defer mbTrans1018.Close()
		_, err1019 := mbTrans1018.WriteString(arg1017)
		if err1019 != nil {
			Usage()
			return
		}
		factory1020 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1021 := factory1020.GetProtocol(mbTrans1018)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1022 := argvalue0.Read(jsProt1021)
		if err1022 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1023 := flag.Arg(2)
		mbTrans1024 := thrift.NewTMemoryBufferLen(len(arg1023))
		defer mbTrans1024.Close()
		_, err1025 := mbTrans1024.WriteString(arg1023)
		if err1025 != nil {
			Usage()
			return
		}
		factory1026 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1027 := factory1026.GetProtocol(mbTrans1024)
		argvalue1 := bidmaster_server.NewDeviceFile()
		err1028 := argvalue1.Read(jsProt1027)
		if err1028 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		fmt.Print(client.AddDeviceFile(value0, value1))
		fmt.Print("\n")
		break
	case "getDeviceFilesByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetDeviceFilesByIds requires 2 args")
			flag.Usage()
		}
		arg1029 := flag.Arg(1)
		mbTrans1030 := thrift.NewTMemoryBufferLen(len(arg1029))
		defer mbTrans1030.Close()
		_, err1031 := mbTrans1030.WriteString(arg1029)
		if err1031 != nil {
			Usage()
			return
		}
		factory1032 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1033 := factory1032.GetProtocol(mbTrans1030)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1034 := argvalue0.Read(jsProt1033)
		if err1034 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg1035 := flag.Arg(2)
		mbTrans1036 := thrift.NewTMemoryBufferLen(len(arg1035))
		defer mbTrans1036.Close()
		_, err1037 := mbTrans1036.WriteString(arg1035)
		if err1037 != nil {
			Usage()
			return
		}
		factory1038 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1039 := factory1038.GetProtocol(mbTrans1036)
		containerStruct1 := bidmaster_server.NewGetDeviceFilesByIdsArgs()
		err1040 := containerStruct1.ReadField2(jsProt1039)
		if err1040 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetDeviceFilesByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getAliyunOssToken":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetAliyunOssToken requires 1 args")
			flag.Usage()
		}
		arg1041 := flag.Arg(1)
		mbTrans1042 := thrift.NewTMemoryBufferLen(len(arg1041))
		defer mbTrans1042.Close()
		_, err1043 := mbTrans1042.WriteString(arg1041)
		if err1043 != nil {
			Usage()
			return
		}
		factory1044 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1045 := factory1044.GetProtocol(mbTrans1042)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1046 := argvalue0.Read(jsProt1045)
		if err1046 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetAliyunOssToken(value0))
		fmt.Print("\n")
		break
	case "getBaiduBosToken":
		if flag.NArg()-1 != 1 {
			fmt.Fprintln(os.Stderr, "GetBaiduBosToken requires 1 args")
			flag.Usage()
		}
		arg1047 := flag.Arg(1)
		mbTrans1048 := thrift.NewTMemoryBufferLen(len(arg1047))
		defer mbTrans1048.Close()
		_, err1049 := mbTrans1048.WriteString(arg1047)
		if err1049 != nil {
			Usage()
			return
		}
		factory1050 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt1051 := factory1050.GetProtocol(mbTrans1048)
		argvalue0 := bidmaster_server.NewRequestHeader()
		err1052 := argvalue0.Read(jsProt1051)
		if err1052 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		fmt.Print(client.GetBaiduBosToken(value0))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
