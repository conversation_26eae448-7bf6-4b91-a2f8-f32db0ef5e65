// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package bidmaster_finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/bidmaster_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = bidmaster_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__

type BidMasterFinanceServer interface { //BidMaster Finance 服务接口定义
	//

	// 代理商充值
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - AgentUid: 代理商UID *
	//  - Amount: 充值金额, 必须为正
	//  - CashRecharged: 累计实际现金充值, 取自bidmaster_types.AgnetAccount.cashRecharged
	//  - Note: 备注
	AgentRecharge(header *common.RequestHeader, agentUid int32, amount int64, cashRecharged int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error)
	// 代理商退款
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - AgentUid: 代理商UID *
	//  - Amount: 退款金额, 必须为正
	//  - CashBalance: 当前现金账户余额, 取自bidmaster_types.AgnetAccount.cashBalance
	//  - Note: 备注
	AgentRefund(header *common.RequestHeader, agentUid int32, amount int64, cashBalance int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error)
	// 代理商返货奖励
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - AgentUid: 代理商UID *
	//  - Amount: 本次返货金额
	//  - Award: 已累计返货金额, 取自bidmaster_types.AgnetAccount.award
	//  - Note: 备注
	RewardAgent(header *common.RequestHeader, agentUid int32, amount int64, award int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error)
	// 代理商退返货
	//
	// Parameters:
	//  - Header: 请求消息结构体
	//  - AgentUid: 代理商UID *
	//  - Amount: 退款金额, 必须为正
	//  - CashBalance: 当前现金账户余额, 取自bidmaster_types.AgnetAccount.cashBalance
	//  - Note: 备注
	AgentRefundReward(header *common.RequestHeader, agentUid int32, amount int64, cashBalance int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error)
	// 代理商执行向广告主加款
	//
	// Parameters:
	//  - Header: 请求消息结构体 *
	//  - AgentUid: 代理商UID *
	//  - SponsorId: 广告主ID *
	//  - Amount: 拨款金额 *
	//  - Balance: 代理商当前余额, 取自bidmaster_types.AgnetAccount.balance
	//  - AmountType: 加款金额类型
	//  - Note: 备注 *
	PerformAppropriation(header *common.RequestHeader, agentUid int32, sponsorId int32, amount int64, balance int64, amountType OperationAmountType, note string) (r *AppropriationTransaction, e *BidMasterFinanceServerException, err error)
	// 代理商执行向广告主减款
	//
	// Parameters:
	//  - Header: 请求消息结构体 *
	//  - AgentUid: 代理商UID *
	//  - SponsorId: 广告主ID *
	//  - Amount: 拨款金额 *
	//  - Balance: 代理商当前余额, 取自bidmaster_types.AgnetAccount.balance
	//  - AmountType: 减款金额类型
	//  - Note: 备注 *
	PerformRecovery(header *common.RequestHeader, agentUid int32, sponsorId int32, amount int64, balance int64, amountType OperationAmountType, note string) (r *AppropriationTransaction, e *BidMasterFinanceServerException, err error)
	// 查询代理商资金流水记录
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Param: 查询参数 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QueryAgentTransaction(header *common.RequestHeader, param *AgentTransactionSearchParam, offset int32, limit int32) (r *AgentTransactionResult, e *BidMasterFinanceServerException, err error)
	// 查询广告主资金流水记录
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Param: 查询参数 *
	//  - Offset: 查询偏移量 *
	//  - Limit: 查询数量 *
	QuerySponsorTransaction(header *common.RequestHeader, param *SponsorTransactionSearchParam, offset int32, limit int32) (r *SponsorTransactionResult, e *BidMasterFinanceServerException, err error)
	// 获取代理商财务信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Ids: 查询参数 *
	GetAgentAccountsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*bidmaster_types.AgentAccount, e *BidMasterFinanceServerException, err error)
	// 获取广告主财务信息
	//
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - Ids: 查询参数 *
	GetSponsorAccountsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*bidmaster_types.SponsorAccount, e *BidMasterFinanceServerException, err error)
	// 编辑广告主日预算
	//
	// Parameters:
	//  - Header: 请求消息头结构体 *
	//  - AgentUid: 代理商UID *
	//  - SponsorId: 广告主ID *
	//  - DailyBudget: 日预算, 为0时表示不限定预算 *
	EditSponsorDailyBudget(header *common.RequestHeader, agentUid int32, sponsorId int32, dailyBudget int64) (e *BidMasterFinanceServerException, err error)
}

//BidMaster Finance 服务接口定义
//
type BidMasterFinanceServerClient struct {
	Transport       thrift.TTransport
	ProtocolFactory thrift.TProtocolFactory
	InputProtocol   thrift.TProtocol
	OutputProtocol  thrift.TProtocol
	SeqId           int32
}

func NewBidMasterFinanceServerClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *BidMasterFinanceServerClient {
	return &BidMasterFinanceServerClient{Transport: t,
		ProtocolFactory: f,
		InputProtocol:   f.GetProtocol(t),
		OutputProtocol:  f.GetProtocol(t),
		SeqId:           0,
	}
}

func NewBidMasterFinanceServerClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *BidMasterFinanceServerClient {
	return &BidMasterFinanceServerClient{Transport: t,
		ProtocolFactory: nil,
		InputProtocol:   iprot,
		OutputProtocol:  oprot,
		SeqId:           0,
	}
}

// 代理商充值
//
// Parameters:
//  - Header: 请求消息结构体
//  - AgentUid: 代理商UID *
//  - Amount: 充值金额, 必须为正
//  - CashRecharged: 累计实际现金充值, 取自bidmaster_types.AgnetAccount.cashRecharged
//  - Note: 备注
func (p *BidMasterFinanceServerClient) AgentRecharge(header *common.RequestHeader, agentUid int32, amount int64, cashRecharged int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	if err = p.sendAgentRecharge(header, agentUid, amount, cashRecharged, note); err != nil {
		return
	}
	return p.recvAgentRecharge()
}

func (p *BidMasterFinanceServerClient) sendAgentRecharge(header *common.RequestHeader, agentUid int32, amount int64, cashRecharged int64, note string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("agentRecharge", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args3 := NewAgentRechargeArgs()
	args3.Header = header
	args3.AgentUid = agentUid
	args3.Amount = amount
	args3.CashRecharged = cashRecharged
	args3.Note = note
	if err = args3.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvAgentRecharge() (value *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error5 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error6 error
		error6, err = error5.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error6
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result4 := NewAgentRechargeResult()
	if err = result4.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result4.Success
	if result4.E != nil {
		e = result4.E
	}
	return
}

// 代理商退款
//
// Parameters:
//  - Header: 请求消息结构体
//  - AgentUid: 代理商UID *
//  - Amount: 退款金额, 必须为正
//  - CashBalance: 当前现金账户余额, 取自bidmaster_types.AgnetAccount.cashBalance
//  - Note: 备注
func (p *BidMasterFinanceServerClient) AgentRefund(header *common.RequestHeader, agentUid int32, amount int64, cashBalance int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	if err = p.sendAgentRefund(header, agentUid, amount, cashBalance, note); err != nil {
		return
	}
	return p.recvAgentRefund()
}

func (p *BidMasterFinanceServerClient) sendAgentRefund(header *common.RequestHeader, agentUid int32, amount int64, cashBalance int64, note string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("agentRefund", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args7 := NewAgentRefundArgs()
	args7.Header = header
	args7.AgentUid = agentUid
	args7.Amount = amount
	args7.CashBalance = cashBalance
	args7.Note = note
	if err = args7.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvAgentRefund() (value *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error9 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error10 error
		error10, err = error9.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error10
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result8 := NewAgentRefundResult()
	if err = result8.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result8.Success
	if result8.E != nil {
		e = result8.E
	}
	return
}

// 代理商返货奖励
//
// Parameters:
//  - Header: 请求消息结构体
//  - AgentUid: 代理商UID *
//  - Amount: 本次返货金额
//  - Award: 已累计返货金额, 取自bidmaster_types.AgnetAccount.award
//  - Note: 备注
func (p *BidMasterFinanceServerClient) RewardAgent(header *common.RequestHeader, agentUid int32, amount int64, award int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	if err = p.sendRewardAgent(header, agentUid, amount, award, note); err != nil {
		return
	}
	return p.recvRewardAgent()
}

func (p *BidMasterFinanceServerClient) sendRewardAgent(header *common.RequestHeader, agentUid int32, amount int64, award int64, note string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("rewardAgent", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args11 := NewRewardAgentArgs()
	args11.Header = header
	args11.AgentUid = agentUid
	args11.Amount = amount
	args11.Award = award
	args11.Note = note
	if err = args11.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvRewardAgent() (value *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error13 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error14 error
		error14, err = error13.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error14
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result12 := NewRewardAgentResult()
	if err = result12.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result12.Success
	if result12.E != nil {
		e = result12.E
	}
	return
}

// 代理商退返货
//
// Parameters:
//  - Header: 请求消息结构体
//  - AgentUid: 代理商UID *
//  - Amount: 退款金额, 必须为正
//  - CashBalance: 当前现金账户余额, 取自bidmaster_types.AgnetAccount.cashBalance
//  - Note: 备注
func (p *BidMasterFinanceServerClient) AgentRefundReward(header *common.RequestHeader, agentUid int32, amount int64, cashBalance int64, note string) (r *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	if err = p.sendAgentRefundReward(header, agentUid, amount, cashBalance, note); err != nil {
		return
	}
	return p.recvAgentRefundReward()
}

func (p *BidMasterFinanceServerClient) sendAgentRefundReward(header *common.RequestHeader, agentUid int32, amount int64, cashBalance int64, note string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("agentRefundReward", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args15 := NewAgentRefundRewardArgs()
	args15.Header = header
	args15.AgentUid = agentUid
	args15.Amount = amount
	args15.CashBalance = cashBalance
	args15.Note = note
	if err = args15.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvAgentRefundReward() (value *AgentTransaction, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error17 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error18 error
		error18, err = error17.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error18
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result16 := NewAgentRefundRewardResult()
	if err = result16.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result16.Success
	if result16.E != nil {
		e = result16.E
	}
	return
}

// 代理商执行向广告主加款
//
// Parameters:
//  - Header: 请求消息结构体 *
//  - AgentUid: 代理商UID *
//  - SponsorId: 广告主ID *
//  - Amount: 拨款金额 *
//  - Balance: 代理商当前余额, 取自bidmaster_types.AgnetAccount.balance
//  - AmountType: 加款金额类型
//  - Note: 备注 *
func (p *BidMasterFinanceServerClient) PerformAppropriation(header *common.RequestHeader, agentUid int32, sponsorId int32, amount int64, balance int64, amountType OperationAmountType, note string) (r *AppropriationTransaction, e *BidMasterFinanceServerException, err error) {
	if err = p.sendPerformAppropriation(header, agentUid, sponsorId, amount, balance, amountType, note); err != nil {
		return
	}
	return p.recvPerformAppropriation()
}

func (p *BidMasterFinanceServerClient) sendPerformAppropriation(header *common.RequestHeader, agentUid int32, sponsorId int32, amount int64, balance int64, amountType OperationAmountType, note string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("performAppropriation", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args19 := NewPerformAppropriationArgs()
	args19.Header = header
	args19.AgentUid = agentUid
	args19.SponsorId = sponsorId
	args19.Amount = amount
	args19.Balance = balance
	args19.AmountType = amountType
	args19.Note = note
	if err = args19.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvPerformAppropriation() (value *AppropriationTransaction, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error21 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error22 error
		error22, err = error21.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error22
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result20 := NewPerformAppropriationResult()
	if err = result20.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result20.Success
	if result20.E != nil {
		e = result20.E
	}
	return
}

// 代理商执行向广告主减款
//
// Parameters:
//  - Header: 请求消息结构体 *
//  - AgentUid: 代理商UID *
//  - SponsorId: 广告主ID *
//  - Amount: 拨款金额 *
//  - Balance: 代理商当前余额, 取自bidmaster_types.AgnetAccount.balance
//  - AmountType: 减款金额类型
//  - Note: 备注 *
func (p *BidMasterFinanceServerClient) PerformRecovery(header *common.RequestHeader, agentUid int32, sponsorId int32, amount int64, balance int64, amountType OperationAmountType, note string) (r *AppropriationTransaction, e *BidMasterFinanceServerException, err error) {
	if err = p.sendPerformRecovery(header, agentUid, sponsorId, amount, balance, amountType, note); err != nil {
		return
	}
	return p.recvPerformRecovery()
}

func (p *BidMasterFinanceServerClient) sendPerformRecovery(header *common.RequestHeader, agentUid int32, sponsorId int32, amount int64, balance int64, amountType OperationAmountType, note string) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("performRecovery", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args23 := NewPerformRecoveryArgs()
	args23.Header = header
	args23.AgentUid = agentUid
	args23.SponsorId = sponsorId
	args23.Amount = amount
	args23.Balance = balance
	args23.AmountType = amountType
	args23.Note = note
	if err = args23.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvPerformRecovery() (value *AppropriationTransaction, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error25 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error26 error
		error26, err = error25.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error26
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result24 := NewPerformRecoveryResult()
	if err = result24.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result24.Success
	if result24.E != nil {
		e = result24.E
	}
	return
}

// 查询代理商资金流水记录
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Param: 查询参数 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *BidMasterFinanceServerClient) QueryAgentTransaction(header *common.RequestHeader, param *AgentTransactionSearchParam, offset int32, limit int32) (r *AgentTransactionResult, e *BidMasterFinanceServerException, err error) {
	if err = p.sendQueryAgentTransaction(header, param, offset, limit); err != nil {
		return
	}
	return p.recvQueryAgentTransaction()
}

func (p *BidMasterFinanceServerClient) sendQueryAgentTransaction(header *common.RequestHeader, param *AgentTransactionSearchParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("queryAgentTransaction", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args27 := NewQueryAgentTransactionArgs()
	args27.Header = header
	args27.Param = param
	args27.Offset = offset
	args27.Limit = limit
	if err = args27.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvQueryAgentTransaction() (value *AgentTransactionResult, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error29 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error30 error
		error30, err = error29.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error30
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result28 := NewQueryAgentTransactionResult()
	if err = result28.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result28.Success
	if result28.E != nil {
		e = result28.E
	}
	return
}

// 查询广告主资金流水记录
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Param: 查询参数 *
//  - Offset: 查询偏移量 *
//  - Limit: 查询数量 *
func (p *BidMasterFinanceServerClient) QuerySponsorTransaction(header *common.RequestHeader, param *SponsorTransactionSearchParam, offset int32, limit int32) (r *SponsorTransactionResult, e *BidMasterFinanceServerException, err error) {
	if err = p.sendQuerySponsorTransaction(header, param, offset, limit); err != nil {
		return
	}
	return p.recvQuerySponsorTransaction()
}

func (p *BidMasterFinanceServerClient) sendQuerySponsorTransaction(header *common.RequestHeader, param *SponsorTransactionSearchParam, offset int32, limit int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("querySponsorTransaction", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args31 := NewQuerySponsorTransactionArgs()
	args31.Header = header
	args31.Param = param
	args31.Offset = offset
	args31.Limit = limit
	if err = args31.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvQuerySponsorTransaction() (value *SponsorTransactionResult, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error33 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error34 error
		error34, err = error33.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error34
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result32 := NewQuerySponsorTransactionResult()
	if err = result32.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result32.Success
	if result32.E != nil {
		e = result32.E
	}
	return
}

// 获取代理商财务信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Ids: 查询参数 *
func (p *BidMasterFinanceServerClient) GetAgentAccountsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*bidmaster_types.AgentAccount, e *BidMasterFinanceServerException, err error) {
	if err = p.sendGetAgentAccountsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetAgentAccountsByIds()
}

func (p *BidMasterFinanceServerClient) sendGetAgentAccountsByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getAgentAccountsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args35 := NewGetAgentAccountsByIdsArgs()
	args35.Header = header
	args35.Ids = ids
	if err = args35.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvGetAgentAccountsByIds() (value map[int32]*bidmaster_types.AgentAccount, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error37 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error38 error
		error38, err = error37.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error38
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result36 := NewGetAgentAccountsByIdsResult()
	if err = result36.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result36.Success
	if result36.E != nil {
		e = result36.E
	}
	return
}

// 获取广告主财务信息
//
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - Ids: 查询参数 *
func (p *BidMasterFinanceServerClient) GetSponsorAccountsByIds(header *common.RequestHeader, ids []int32) (r map[int32]*bidmaster_types.SponsorAccount, e *BidMasterFinanceServerException, err error) {
	if err = p.sendGetSponsorAccountsByIds(header, ids); err != nil {
		return
	}
	return p.recvGetSponsorAccountsByIds()
}

func (p *BidMasterFinanceServerClient) sendGetSponsorAccountsByIds(header *common.RequestHeader, ids []int32) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("getSponsorAccountsByIds", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args39 := NewGetSponsorAccountsByIdsArgs()
	args39.Header = header
	args39.Ids = ids
	if err = args39.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvGetSponsorAccountsByIds() (value map[int32]*bidmaster_types.SponsorAccount, e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error41 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error42 error
		error42, err = error41.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error42
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result40 := NewGetSponsorAccountsByIdsResult()
	if err = result40.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	value = result40.Success
	if result40.E != nil {
		e = result40.E
	}
	return
}

// 编辑广告主日预算
//
// Parameters:
//  - Header: 请求消息头结构体 *
//  - AgentUid: 代理商UID *
//  - SponsorId: 广告主ID *
//  - DailyBudget: 日预算, 为0时表示不限定预算 *
func (p *BidMasterFinanceServerClient) EditSponsorDailyBudget(header *common.RequestHeader, agentUid int32, sponsorId int32, dailyBudget int64) (e *BidMasterFinanceServerException, err error) {
	if err = p.sendEditSponsorDailyBudget(header, agentUid, sponsorId, dailyBudget); err != nil {
		return
	}
	return p.recvEditSponsorDailyBudget()
}

func (p *BidMasterFinanceServerClient) sendEditSponsorDailyBudget(header *common.RequestHeader, agentUid int32, sponsorId int32, dailyBudget int64) (err error) {
	oprot := p.OutputProtocol
	if oprot == nil {
		oprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.OutputProtocol = oprot
	}
	p.SeqId++
	if err = oprot.WriteMessageBegin("editSponsorDailyBudget", thrift.CALL, p.SeqId); err != nil {
		return
	}
	args43 := NewEditSponsorDailyBudgetArgs()
	args43.Header = header
	args43.AgentUid = agentUid
	args43.SponsorId = sponsorId
	args43.DailyBudget = dailyBudget
	if err = args43.Write(oprot); err != nil {
		return
	}
	if err = oprot.WriteMessageEnd(); err != nil {
		return
	}
	return oprot.Flush()
}

func (p *BidMasterFinanceServerClient) recvEditSponsorDailyBudget() (e *BidMasterFinanceServerException, err error) {
	iprot := p.InputProtocol
	if iprot == nil {
		iprot = p.ProtocolFactory.GetProtocol(p.Transport)
		p.InputProtocol = iprot
	}
	_, mTypeId, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return
	}
	if mTypeId == thrift.EXCEPTION {
		error45 := thrift.NewTApplicationException(thrift.UNKNOWN_APPLICATION_EXCEPTION, "Unknown Exception")
		var error46 error
		error46, err = error45.Read(iprot)
		if err != nil {
			return
		}
		if err = iprot.ReadMessageEnd(); err != nil {
			return
		}
		err = error46
		return
	}
	if p.SeqId != seqId {
		err = thrift.NewTApplicationException(thrift.BAD_SEQUENCE_ID, "ping failed: out of sequence response")
		return
	}
	result44 := NewEditSponsorDailyBudgetResult()
	if err = result44.Read(iprot); err != nil {
		return
	}
	if err = iprot.ReadMessageEnd(); err != nil {
		return
	}
	if result44.E != nil {
		e = result44.E
	}
	return
}

type BidMasterFinanceServerProcessor struct {
	processorMap map[string]thrift.TProcessorFunction
	handler      BidMasterFinanceServer
}

func (p *BidMasterFinanceServerProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
	p.processorMap[key] = processor
}

func (p *BidMasterFinanceServerProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
	processor, ok = p.processorMap[key]
	return processor, ok
}

func (p *BidMasterFinanceServerProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
	return p.processorMap
}

func NewBidMasterFinanceServerProcessor(handler BidMasterFinanceServer) *BidMasterFinanceServerProcessor {

	self47 := &BidMasterFinanceServerProcessor{handler: handler, processorMap: make(map[string]thrift.TProcessorFunction)}
	self47.processorMap["agentRecharge"] = &bidMasterFinanceServerProcessorAgentRecharge{handler: handler}
	self47.processorMap["agentRefund"] = &bidMasterFinanceServerProcessorAgentRefund{handler: handler}
	self47.processorMap["rewardAgent"] = &bidMasterFinanceServerProcessorRewardAgent{handler: handler}
	self47.processorMap["agentRefundReward"] = &bidMasterFinanceServerProcessorAgentRefundReward{handler: handler}
	self47.processorMap["performAppropriation"] = &bidMasterFinanceServerProcessorPerformAppropriation{handler: handler}
	self47.processorMap["performRecovery"] = &bidMasterFinanceServerProcessorPerformRecovery{handler: handler}
	self47.processorMap["queryAgentTransaction"] = &bidMasterFinanceServerProcessorQueryAgentTransaction{handler: handler}
	self47.processorMap["querySponsorTransaction"] = &bidMasterFinanceServerProcessorQuerySponsorTransaction{handler: handler}
	self47.processorMap["getAgentAccountsByIds"] = &bidMasterFinanceServerProcessorGetAgentAccountsByIds{handler: handler}
	self47.processorMap["getSponsorAccountsByIds"] = &bidMasterFinanceServerProcessorGetSponsorAccountsByIds{handler: handler}
	self47.processorMap["editSponsorDailyBudget"] = &bidMasterFinanceServerProcessorEditSponsorDailyBudget{handler: handler}
	return self47
}

func (p *BidMasterFinanceServerProcessor) Process(iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	name, _, seqId, err := iprot.ReadMessageBegin()
	if err != nil {
		return false, err
	}
	if processor, ok := p.GetProcessorFunction(name); ok {
		return processor.Process(seqId, iprot, oprot)
	}
	iprot.Skip(thrift.STRUCT)
	iprot.ReadMessageEnd()
	x48 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function "+name)
	oprot.WriteMessageBegin(name, thrift.EXCEPTION, seqId)
	x48.Write(oprot)
	oprot.WriteMessageEnd()
	oprot.Flush()
	return false, x48

}

type bidMasterFinanceServerProcessorAgentRecharge struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorAgentRecharge) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAgentRechargeArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("agentRecharge", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAgentRechargeResult()
	if result.Success, result.E, err = p.handler.AgentRecharge(args.Header, args.AgentUid, args.Amount, args.CashRecharged, args.Note); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing agentRecharge: "+err.Error())
		oprot.WriteMessageBegin("agentRecharge", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("agentRecharge", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorAgentRefund struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorAgentRefund) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAgentRefundArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("agentRefund", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAgentRefundResult()
	if result.Success, result.E, err = p.handler.AgentRefund(args.Header, args.AgentUid, args.Amount, args.CashBalance, args.Note); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing agentRefund: "+err.Error())
		oprot.WriteMessageBegin("agentRefund", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("agentRefund", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorRewardAgent struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorRewardAgent) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewRewardAgentArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("rewardAgent", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewRewardAgentResult()
	if result.Success, result.E, err = p.handler.RewardAgent(args.Header, args.AgentUid, args.Amount, args.Award, args.Note); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing rewardAgent: "+err.Error())
		oprot.WriteMessageBegin("rewardAgent", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("rewardAgent", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorAgentRefundReward struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorAgentRefundReward) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewAgentRefundRewardArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("agentRefundReward", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewAgentRefundRewardResult()
	if result.Success, result.E, err = p.handler.AgentRefundReward(args.Header, args.AgentUid, args.Amount, args.CashBalance, args.Note); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing agentRefundReward: "+err.Error())
		oprot.WriteMessageBegin("agentRefundReward", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("agentRefundReward", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorPerformAppropriation struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorPerformAppropriation) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPerformAppropriationArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("performAppropriation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPerformAppropriationResult()
	if result.Success, result.E, err = p.handler.PerformAppropriation(args.Header, args.AgentUid, args.SponsorId, args.Amount, args.Balance, args.AmountType, args.Note); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing performAppropriation: "+err.Error())
		oprot.WriteMessageBegin("performAppropriation", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("performAppropriation", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorPerformRecovery struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorPerformRecovery) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewPerformRecoveryArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("performRecovery", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewPerformRecoveryResult()
	if result.Success, result.E, err = p.handler.PerformRecovery(args.Header, args.AgentUid, args.SponsorId, args.Amount, args.Balance, args.AmountType, args.Note); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing performRecovery: "+err.Error())
		oprot.WriteMessageBegin("performRecovery", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("performRecovery", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorQueryAgentTransaction struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorQueryAgentTransaction) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQueryAgentTransactionArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("queryAgentTransaction", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQueryAgentTransactionResult()
	if result.Success, result.E, err = p.handler.QueryAgentTransaction(args.Header, args.Param, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing queryAgentTransaction: "+err.Error())
		oprot.WriteMessageBegin("queryAgentTransaction", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("queryAgentTransaction", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorQuerySponsorTransaction struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorQuerySponsorTransaction) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewQuerySponsorTransactionArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("querySponsorTransaction", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewQuerySponsorTransactionResult()
	if result.Success, result.E, err = p.handler.QuerySponsorTransaction(args.Header, args.Param, args.Offset, args.Limit); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing querySponsorTransaction: "+err.Error())
		oprot.WriteMessageBegin("querySponsorTransaction", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("querySponsorTransaction", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorGetAgentAccountsByIds struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorGetAgentAccountsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetAgentAccountsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getAgentAccountsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetAgentAccountsByIdsResult()
	if result.Success, result.E, err = p.handler.GetAgentAccountsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getAgentAccountsByIds: "+err.Error())
		oprot.WriteMessageBegin("getAgentAccountsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getAgentAccountsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorGetSponsorAccountsByIds struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorGetSponsorAccountsByIds) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewGetSponsorAccountsByIdsArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("getSponsorAccountsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewGetSponsorAccountsByIdsResult()
	if result.Success, result.E, err = p.handler.GetSponsorAccountsByIds(args.Header, args.Ids); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing getSponsorAccountsByIds: "+err.Error())
		oprot.WriteMessageBegin("getSponsorAccountsByIds", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("getSponsorAccountsByIds", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

type bidMasterFinanceServerProcessorEditSponsorDailyBudget struct {
	handler BidMasterFinanceServer
}

func (p *bidMasterFinanceServerProcessorEditSponsorDailyBudget) Process(seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
	args := NewEditSponsorDailyBudgetArgs()
	if err = args.Read(iprot); err != nil {
		iprot.ReadMessageEnd()
		x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err.Error())
		oprot.WriteMessageBegin("editSponsorDailyBudget", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	iprot.ReadMessageEnd()
	result := NewEditSponsorDailyBudgetResult()
	if result.E, err = p.handler.EditSponsorDailyBudget(args.Header, args.AgentUid, args.SponsorId, args.DailyBudget); err != nil {
		x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing editSponsorDailyBudget: "+err.Error())
		oprot.WriteMessageBegin("editSponsorDailyBudget", thrift.EXCEPTION, seqId)
		x.Write(oprot)
		oprot.WriteMessageEnd()
		oprot.Flush()
		return
	}
	if err2 := oprot.WriteMessageBegin("editSponsorDailyBudget", thrift.REPLY, seqId); err2 != nil {
		err = err2
	}
	if err2 := result.Write(oprot); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.WriteMessageEnd(); err == nil && err2 != nil {
		err = err2
	}
	if err2 := oprot.Flush(); err == nil && err2 != nil {
		err = err2
	}
	if err != nil {
		return
	}
	return true, err
}

// HELPER FUNCTIONS AND STRUCTURES

type AgentRechargeArgs struct {
	Header        *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid      int32                 `thrift:"agentUid,2" json:"agentUid"`
	Amount        int64                 `thrift:"amount,3" json:"amount"`
	CashRecharged int64                 `thrift:"cashRecharged,4" json:"cashRecharged"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Note string `thrift:"note,10" json:"note"`
}

func NewAgentRechargeArgs() *AgentRechargeArgs {
	return &AgentRechargeArgs{}
}

func (p *AgentRechargeArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentRechargeArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AgentRechargeArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentRechargeArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *AgentRechargeArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CashRecharged = v
	}
	return nil
}

func (p *AgentRechargeArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AgentRechargeArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("agentRecharge_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentRechargeArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AgentRechargeArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentRechargeArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *AgentRechargeArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cashRecharged", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cashRecharged: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CashRecharged)); err != nil {
		return fmt.Errorf("%T.cashRecharged (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cashRecharged: %s", p, err)
	}
	return err
}

func (p *AgentRechargeArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:note: %s", p, err)
	}
	return err
}

func (p *AgentRechargeArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentRechargeArgs(%+v)", *p)
}

type AgentRechargeResult struct {
	Success *AgentTransaction                `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewAgentRechargeResult() *AgentRechargeResult {
	return &AgentRechargeResult{}
}

func (p *AgentRechargeResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentRechargeResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAgentTransaction()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AgentRechargeResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AgentRechargeResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("agentRecharge_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentRechargeResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AgentRechargeResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AgentRechargeResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentRechargeResult(%+v)", *p)
}

type AgentRefundArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid    int32                 `thrift:"agentUid,2" json:"agentUid"`
	Amount      int64                 `thrift:"amount,3" json:"amount"`
	CashBalance int64                 `thrift:"cashBalance,4" json:"cashBalance"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Note string `thrift:"note,10" json:"note"`
}

func NewAgentRefundArgs() *AgentRefundArgs {
	return &AgentRefundArgs{}
}

func (p *AgentRefundArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AgentRefundArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentRefundArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *AgentRefundArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CashBalance = v
	}
	return nil
}

func (p *AgentRefundArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AgentRefundArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("agentRefund_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AgentRefundArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentRefundArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *AgentRefundArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cashBalance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cashBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CashBalance)); err != nil {
		return fmt.Errorf("%T.cashBalance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cashBalance: %s", p, err)
	}
	return err
}

func (p *AgentRefundArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:note: %s", p, err)
	}
	return err
}

func (p *AgentRefundArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentRefundArgs(%+v)", *p)
}

type AgentRefundResult struct {
	Success *AgentTransaction                `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewAgentRefundResult() *AgentRefundResult {
	return &AgentRefundResult{}
}

func (p *AgentRefundResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAgentTransaction()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AgentRefundResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AgentRefundResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("agentRefund_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AgentRefundResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AgentRefundResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentRefundResult(%+v)", *p)
}

type RewardAgentArgs struct {
	Header   *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid int32                 `thrift:"agentUid,2" json:"agentUid"`
	Amount   int64                 `thrift:"amount,3" json:"amount"`
	Award    int64                 `thrift:"award,4" json:"award"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Note string `thrift:"note,10" json:"note"`
}

func NewRewardAgentArgs() *RewardAgentArgs {
	return &RewardAgentArgs{}
}

func (p *RewardAgentArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RewardAgentArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *RewardAgentArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *RewardAgentArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *RewardAgentArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Award = v
	}
	return nil
}

func (p *RewardAgentArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *RewardAgentArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rewardAgent_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RewardAgentArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *RewardAgentArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *RewardAgentArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *RewardAgentArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("award", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:award: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Award)); err != nil {
		return fmt.Errorf("%T.award (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:award: %s", p, err)
	}
	return err
}

func (p *RewardAgentArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:note: %s", p, err)
	}
	return err
}

func (p *RewardAgentArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RewardAgentArgs(%+v)", *p)
}

type RewardAgentResult struct {
	Success *AgentTransaction                `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewRewardAgentResult() *RewardAgentResult {
	return &RewardAgentResult{}
}

func (p *RewardAgentResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *RewardAgentResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAgentTransaction()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *RewardAgentResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *RewardAgentResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("rewardAgent_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *RewardAgentResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *RewardAgentResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *RewardAgentResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("RewardAgentResult(%+v)", *p)
}

type AgentRefundRewardArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid    int32                 `thrift:"agentUid,2" json:"agentUid"`
	Amount      int64                 `thrift:"amount,3" json:"amount"`
	CashBalance int64                 `thrift:"cashBalance,4" json:"cashBalance"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Note string `thrift:"note,10" json:"note"`
}

func NewAgentRefundRewardArgs() *AgentRefundRewardArgs {
	return &AgentRefundRewardArgs{}
}

func (p *AgentRefundRewardArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundRewardArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *AgentRefundRewardArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentRefundRewardArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *AgentRefundRewardArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CashBalance = v
	}
	return nil
}

func (p *AgentRefundRewardArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AgentRefundRewardArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("agentRefundReward_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundRewardArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *AgentRefundRewardArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentRefundRewardArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *AgentRefundRewardArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cashBalance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cashBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CashBalance)); err != nil {
		return fmt.Errorf("%T.cashBalance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cashBalance: %s", p, err)
	}
	return err
}

func (p *AgentRefundRewardArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:note: %s", p, err)
	}
	return err
}

func (p *AgentRefundRewardArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentRefundRewardArgs(%+v)", *p)
}

type AgentRefundRewardResult struct {
	Success *AgentTransaction                `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewAgentRefundRewardResult() *AgentRefundRewardResult {
	return &AgentRefundRewardResult{}
}

func (p *AgentRefundRewardResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundRewardResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAgentTransaction()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *AgentRefundRewardResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *AgentRefundRewardResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("agentRefundReward_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentRefundRewardResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *AgentRefundRewardResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *AgentRefundRewardResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentRefundRewardResult(%+v)", *p)
}

type PerformAppropriationArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid   int32                 `thrift:"agentUid,2" json:"agentUid"`
	SponsorId  int32                 `thrift:"sponsorId,3" json:"sponsorId"`
	Amount     int64                 `thrift:"amount,4" json:"amount"`
	Balance    int64                 `thrift:"balance,5" json:"balance"`
	AmountType OperationAmountType   `thrift:"amountType,6" json:"amountType"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Note string `thrift:"note,10" json:"note"`
}

func NewPerformAppropriationArgs() *PerformAppropriationArgs {
	return &PerformAppropriationArgs{
		AmountType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PerformAppropriationArgs) IsSetAmountType() bool {
	return int64(p.AmountType) != math.MinInt32-1
}

func (p *PerformAppropriationArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *PerformAppropriationArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *PerformAppropriationArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *PerformAppropriationArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Balance = v
	}
	return nil
}

func (p *PerformAppropriationArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AmountType = OperationAmountType(v)
	}
	return nil
}

func (p *PerformAppropriationArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *PerformAppropriationArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("performAppropriation_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:amount: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:balance: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAmountType() {
		if err := oprot.WriteFieldBegin("amountType", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:amountType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AmountType)); err != nil {
			return fmt.Errorf("%T.amountType (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:amountType: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:note: %s", p, err)
	}
	return err
}

func (p *PerformAppropriationArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PerformAppropriationArgs(%+v)", *p)
}

type PerformAppropriationResult struct {
	Success *AppropriationTransaction        `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewPerformAppropriationResult() *PerformAppropriationResult {
	return &PerformAppropriationResult{}
}

func (p *PerformAppropriationResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppropriationTransaction()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PerformAppropriationResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *PerformAppropriationResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("performAppropriation_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PerformAppropriationResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *PerformAppropriationResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PerformAppropriationResult(%+v)", *p)
}

type PerformRecoveryArgs struct {
	Header     *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid   int32                 `thrift:"agentUid,2" json:"agentUid"`
	SponsorId  int32                 `thrift:"sponsorId,3" json:"sponsorId"`
	Amount     int64                 `thrift:"amount,4" json:"amount"`
	Balance    int64                 `thrift:"balance,5" json:"balance"`
	AmountType OperationAmountType   `thrift:"amountType,6" json:"amountType"`
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Note string `thrift:"note,10" json:"note"`
}

func NewPerformRecoveryArgs() *PerformRecoveryArgs {
	return &PerformRecoveryArgs{
		AmountType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *PerformRecoveryArgs) IsSetAmountType() bool {
	return int64(p.AmountType) != math.MinInt32-1
}

func (p *PerformRecoveryArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PerformRecoveryArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *PerformRecoveryArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *PerformRecoveryArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *PerformRecoveryArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *PerformRecoveryArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Balance = v
	}
	return nil
}

func (p *PerformRecoveryArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.AmountType = OperationAmountType(v)
	}
	return nil
}

func (p *PerformRecoveryArgs) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *PerformRecoveryArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("performRecovery_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PerformRecoveryArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *PerformRecoveryArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *PerformRecoveryArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *PerformRecoveryArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:amount: %s", p, err)
	}
	return err
}

func (p *PerformRecoveryArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("balance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:balance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Balance)); err != nil {
		return fmt.Errorf("%T.balance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:balance: %s", p, err)
	}
	return err
}

func (p *PerformRecoveryArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if p.IsSetAmountType() {
		if err := oprot.WriteFieldBegin("amountType", thrift.I32, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:amountType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AmountType)); err != nil {
			return fmt.Errorf("%T.amountType (6) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:amountType: %s", p, err)
		}
	}
	return err
}

func (p *PerformRecoveryArgs) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:note: %s", p, err)
	}
	return err
}

func (p *PerformRecoveryArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PerformRecoveryArgs(%+v)", *p)
}

type PerformRecoveryResult struct {
	Success *AppropriationTransaction        `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewPerformRecoveryResult() *PerformRecoveryResult {
	return &PerformRecoveryResult{}
}

func (p *PerformRecoveryResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PerformRecoveryResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAppropriationTransaction()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *PerformRecoveryResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *PerformRecoveryResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("performRecovery_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PerformRecoveryResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *PerformRecoveryResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *PerformRecoveryResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PerformRecoveryResult(%+v)", *p)
}

type QueryAgentTransactionArgs struct {
	Header *common.RequestHeader        `thrift:"header,1" json:"header"`
	Param  *AgentTransactionSearchParam `thrift:"param,2" json:"param"`
	// unused field # 3
	// unused field # 4
	Offset int32 `thrift:"offset,5" json:"offset"`
	Limit  int32 `thrift:"limit,6" json:"limit"`
}

func NewQueryAgentTransactionArgs() *QueryAgentTransactionArgs {
	return &QueryAgentTransactionArgs{}
}

func (p *QueryAgentTransactionArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAgentTransactionArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QueryAgentTransactionArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = NewAgentTransactionSearchParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *QueryAgentTransactionArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QueryAgentTransactionArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QueryAgentTransactionArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAgentTransaction_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAgentTransactionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QueryAgentTransactionArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *QueryAgentTransactionArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QueryAgentTransactionArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QueryAgentTransactionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAgentTransactionArgs(%+v)", *p)
}

type QueryAgentTransactionResult struct {
	Success *AgentTransactionResult          `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewQueryAgentTransactionResult() *QueryAgentTransactionResult {
	return &QueryAgentTransactionResult{}
}

func (p *QueryAgentTransactionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QueryAgentTransactionResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewAgentTransactionResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QueryAgentTransactionResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QueryAgentTransactionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("queryAgentTransaction_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QueryAgentTransactionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QueryAgentTransactionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QueryAgentTransactionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QueryAgentTransactionResult(%+v)", *p)
}

type QuerySponsorTransactionArgs struct {
	Header *common.RequestHeader          `thrift:"header,1" json:"header"`
	Param  *SponsorTransactionSearchParam `thrift:"param,2" json:"param"`
	// unused field # 3
	// unused field # 4
	Offset int32 `thrift:"offset,5" json:"offset"`
	Limit  int32 `thrift:"limit,6" json:"limit"`
}

func NewQuerySponsorTransactionArgs() *QuerySponsorTransactionArgs {
	return &QuerySponsorTransactionArgs{}
}

func (p *QuerySponsorTransactionArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QuerySponsorTransactionArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *QuerySponsorTransactionArgs) readField2(iprot thrift.TProtocol) error {
	p.Param = NewSponsorTransactionSearchParam()
	if err := p.Param.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Param)
	}
	return nil
}

func (p *QuerySponsorTransactionArgs) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *QuerySponsorTransactionArgs) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *QuerySponsorTransactionArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("querySponsorTransaction_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QuerySponsorTransactionArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *QuerySponsorTransactionArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Param != nil {
		if err := oprot.WriteFieldBegin("param", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:param: %s", p, err)
		}
		if err := p.Param.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Param)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:param: %s", p, err)
		}
	}
	return err
}

func (p *QuerySponsorTransactionArgs) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:offset: %s", p, err)
	}
	return err
}

func (p *QuerySponsorTransactionArgs) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:limit: %s", p, err)
	}
	return err
}

func (p *QuerySponsorTransactionArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QuerySponsorTransactionArgs(%+v)", *p)
}

type QuerySponsorTransactionResult struct {
	Success *SponsorTransactionResult        `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewQuerySponsorTransactionResult() *QuerySponsorTransactionResult {
	return &QuerySponsorTransactionResult{}
}

func (p *QuerySponsorTransactionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *QuerySponsorTransactionResult) readField0(iprot thrift.TProtocol) error {
	p.Success = NewSponsorTransactionResult()
	if err := p.Success.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Success)
	}
	return nil
}

func (p *QuerySponsorTransactionResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *QuerySponsorTransactionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("querySponsorTransaction_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *QuerySponsorTransactionResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.STRUCT, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := p.Success.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Success)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *QuerySponsorTransactionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *QuerySponsorTransactionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("QuerySponsorTransactionResult(%+v)", *p)
}

type GetAgentAccountsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetAgentAccountsByIdsArgs() *GetAgentAccountsByIdsArgs {
	return &GetAgentAccountsByIdsArgs{}
}

func (p *GetAgentAccountsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAgentAccountsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetAgentAccountsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem49 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem49 = v
		}
		p.Ids = append(p.Ids, _elem49)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetAgentAccountsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAgentAccountsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAgentAccountsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetAgentAccountsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetAgentAccountsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentAccountsByIdsArgs(%+v)", *p)
}

type GetAgentAccountsByIdsResult struct {
	Success map[int32]*bidmaster_types.AgentAccount `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException        `thrift:"e,1" json:"e"`
}

func NewGetAgentAccountsByIdsResult() *GetAgentAccountsByIdsResult {
	return &GetAgentAccountsByIdsResult{}
}

func (p *GetAgentAccountsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetAgentAccountsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*bidmaster_types.AgentAccount, size)
	for i := 0; i < size; i++ {
		var _key50 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key50 = v
		}
		_val51 := bidmaster_types.NewAgentAccount()
		if err := _val51.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val51)
		}
		p.Success[_key50] = _val51
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetAgentAccountsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetAgentAccountsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getAgentAccountsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetAgentAccountsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetAgentAccountsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetAgentAccountsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetAgentAccountsByIdsResult(%+v)", *p)
}

type GetSponsorAccountsByIdsArgs struct {
	Header *common.RequestHeader `thrift:"header,1" json:"header"`
	Ids    []int32               `thrift:"ids,2" json:"ids"`
}

func NewGetSponsorAccountsByIdsArgs() *GetSponsorAccountsByIdsArgs {
	return &GetSponsorAccountsByIdsArgs{}
}

func (p *GetSponsorAccountsByIdsArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsArgs) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Ids = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem52 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem52 = v
		}
		p.Ids = append(p.Ids, _elem52)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSponsorAccountsByIds_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *GetSponsorAccountsByIdsArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Ids != nil {
		if err := oprot.WriteFieldBegin("ids", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:ids: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Ids)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Ids {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:ids: %s", p, err)
		}
	}
	return err
}

func (p *GetSponsorAccountsByIdsArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSponsorAccountsByIdsArgs(%+v)", *p)
}

type GetSponsorAccountsByIdsResult struct {
	Success map[int32]*bidmaster_types.SponsorAccount `thrift:"success,0" json:"success"`
	E       *BidMasterFinanceServerException          `thrift:"e,1" json:"e"`
}

func NewGetSponsorAccountsByIdsResult() *GetSponsorAccountsByIdsResult {
	return &GetSponsorAccountsByIdsResult{}
}

func (p *GetSponsorAccountsByIdsResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 0:
			if fieldTypeId == thrift.MAP {
				if err := p.readField0(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsResult) readField0(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.Success = make(map[int32]*bidmaster_types.SponsorAccount, size)
	for i := 0; i < size; i++ {
		var _key53 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key53 = v
		}
		_val54 := bidmaster_types.NewSponsorAccount()
		if err := _val54.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _val54)
		}
		p.Success[_key53] = _val54
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("getSponsorAccountsByIds_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	default:
		if err := p.writeField0(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GetSponsorAccountsByIdsResult) writeField0(oprot thrift.TProtocol) (err error) {
	if p.Success != nil {
		if err := oprot.WriteFieldBegin("success", thrift.MAP, 0); err != nil {
			return fmt.Errorf("%T write field begin error 0:success: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.STRUCT, len(p.Success)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.Success {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 0:success: %s", p, err)
		}
	}
	return err
}

func (p *GetSponsorAccountsByIdsResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *GetSponsorAccountsByIdsResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GetSponsorAccountsByIdsResult(%+v)", *p)
}

type EditSponsorDailyBudgetArgs struct {
	Header      *common.RequestHeader `thrift:"header,1" json:"header"`
	AgentUid    int32                 `thrift:"agentUid,2" json:"agentUid"`
	SponsorId   int32                 `thrift:"sponsorId,3" json:"sponsorId"`
	DailyBudget int64                 `thrift:"dailyBudget,4" json:"dailyBudget"`
}

func NewEditSponsorDailyBudgetArgs() *EditSponsorDailyBudgetArgs {
	return &EditSponsorDailyBudgetArgs{}
}

func (p *EditSponsorDailyBudgetArgs) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditSponsorDailyBudgetArgs) readField1(iprot thrift.TProtocol) error {
	p.Header = common.NewRequestHeader()
	if err := p.Header.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Header)
	}
	return nil
}

func (p *EditSponsorDailyBudgetArgs) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *EditSponsorDailyBudgetArgs) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *EditSponsorDailyBudgetArgs) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DailyBudget = v
	}
	return nil
}

func (p *EditSponsorDailyBudgetArgs) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editSponsorDailyBudget_args"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditSponsorDailyBudgetArgs) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Header != nil {
		if err := oprot.WriteFieldBegin("header", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:header: %s", p, err)
		}
		if err := p.Header.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Header)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:header: %s", p, err)
		}
	}
	return err
}

func (p *EditSponsorDailyBudgetArgs) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:agentUid: %s", p, err)
	}
	return err
}

func (p *EditSponsorDailyBudgetArgs) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sponsorId: %s", p, err)
	}
	return err
}

func (p *EditSponsorDailyBudgetArgs) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dailyBudget", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dailyBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DailyBudget)); err != nil {
		return fmt.Errorf("%T.dailyBudget (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dailyBudget: %s", p, err)
	}
	return err
}

func (p *EditSponsorDailyBudgetArgs) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditSponsorDailyBudgetArgs(%+v)", *p)
}

type EditSponsorDailyBudgetResult struct {
	E *BidMasterFinanceServerException `thrift:"e,1" json:"e"`
}

func NewEditSponsorDailyBudgetResult() *EditSponsorDailyBudgetResult {
	return &EditSponsorDailyBudgetResult{}
}

func (p *EditSponsorDailyBudgetResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *EditSponsorDailyBudgetResult) readField1(iprot thrift.TProtocol) error {
	p.E = NewBidMasterFinanceServerException()
	if err := p.E.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.E)
	}
	return nil
}

func (p *EditSponsorDailyBudgetResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("editSponsorDailyBudget_result"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	switch {
	case p.E != nil:
		if err := p.writeField1(oprot); err != nil {
			return err
		}
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *EditSponsorDailyBudgetResult) writeField1(oprot thrift.TProtocol) (err error) {
	if p.E != nil {
		if err := oprot.WriteFieldBegin("e", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:e: %s", p, err)
		}
		if err := p.E.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.E)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:e: %s", p, err)
		}
	}
	return err
}

func (p *EditSponsorDailyBudgetResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("EditSponsorDailyBudgetResult(%+v)", *p)
}
