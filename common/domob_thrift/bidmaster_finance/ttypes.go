// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package bidmaster_finance

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/bidmaster_types"
	"rtb_model_server/common/domob_thrift/common"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = bidmaster_types.GoUnusedProtection__
var _ = common.GoUnusedProtection__
var GoUnusedProtection__ int

//@Description("代理商资金账户操作类型,供查询时使用") *
type AgentTransactionType int64

const (
	AgentTransactionType_ATT_ALL                          AgentTransactionType = 0
	AgentTransactionType_ATT_RECHARGE                     AgentTransactionType = 1
	AgentTransactionType_ATT_REWARD                       AgentTransactionType = 2
	AgentTransactionType_ATT_WITHDRAW                     AgentTransactionType = 3
	AgentTransactionType_ATT_SPONSOR_APPROPRIATION        AgentTransactionType = 4
	AgentTransactionType_ATT_SPONSOR_RECOVERY             AgentTransactionType = 5
	AgentTransactionType_ATT_SPONSOR_APPROPRIATION_REWARD AgentTransactionType = 6
	AgentTransactionType_ATT_SPONSOR_RECOVERY_REWARD      AgentTransactionType = 7
	AgentTransactionType_ATT_WITHDRAW_REWARD              AgentTransactionType = 8
)

func (p AgentTransactionType) String() string {
	switch p {
	case AgentTransactionType_ATT_ALL:
		return "AgentTransactionType_ATT_ALL"
	case AgentTransactionType_ATT_RECHARGE:
		return "AgentTransactionType_ATT_RECHARGE"
	case AgentTransactionType_ATT_REWARD:
		return "AgentTransactionType_ATT_REWARD"
	case AgentTransactionType_ATT_WITHDRAW:
		return "AgentTransactionType_ATT_WITHDRAW"
	case AgentTransactionType_ATT_SPONSOR_APPROPRIATION:
		return "AgentTransactionType_ATT_SPONSOR_APPROPRIATION"
	case AgentTransactionType_ATT_SPONSOR_RECOVERY:
		return "AgentTransactionType_ATT_SPONSOR_RECOVERY"
	case AgentTransactionType_ATT_SPONSOR_APPROPRIATION_REWARD:
		return "AgentTransactionType_ATT_SPONSOR_APPROPRIATION_REWARD"
	case AgentTransactionType_ATT_SPONSOR_RECOVERY_REWARD:
		return "AgentTransactionType_ATT_SPONSOR_RECOVERY_REWARD"
	case AgentTransactionType_ATT_WITHDRAW_REWARD:
		return "AgentTransactionType_ATT_WITHDRAW_REWARD"
	}
	return "<UNSET>"
}

func AgentTransactionTypeFromString(s string) (AgentTransactionType, error) {
	switch s {
	case "AgentTransactionType_ATT_ALL":
		return AgentTransactionType_ATT_ALL, nil
	case "AgentTransactionType_ATT_RECHARGE":
		return AgentTransactionType_ATT_RECHARGE, nil
	case "AgentTransactionType_ATT_REWARD":
		return AgentTransactionType_ATT_REWARD, nil
	case "AgentTransactionType_ATT_WITHDRAW":
		return AgentTransactionType_ATT_WITHDRAW, nil
	case "AgentTransactionType_ATT_SPONSOR_APPROPRIATION":
		return AgentTransactionType_ATT_SPONSOR_APPROPRIATION, nil
	case "AgentTransactionType_ATT_SPONSOR_RECOVERY":
		return AgentTransactionType_ATT_SPONSOR_RECOVERY, nil
	case "AgentTransactionType_ATT_SPONSOR_APPROPRIATION_REWARD":
		return AgentTransactionType_ATT_SPONSOR_APPROPRIATION_REWARD, nil
	case "AgentTransactionType_ATT_SPONSOR_RECOVERY_REWARD":
		return AgentTransactionType_ATT_SPONSOR_RECOVERY_REWARD, nil
	case "AgentTransactionType_ATT_WITHDRAW_REWARD":
		return AgentTransactionType_ATT_WITHDRAW_REWARD, nil
	}
	return AgentTransactionType(math.MinInt32 - 1), fmt.Errorf("not a valid AgentTransactionType string")
}

//@Description("广告主资金账户操作类型,供查询时使用") *
type SponsorTransactionType int64

const (
	SponsorTransactionType_STT_ALL                  SponsorTransactionType = 0
	SponsorTransactionType_STT_APPROPRIATION        SponsorTransactionType = 1
	SponsorTransactionType_STT_RECOVERY             SponsorTransactionType = 2
	SponsorTransactionType_STT_APPROPRIATION_REWARD SponsorTransactionType = 3
	SponsorTransactionType_STT_RECOVERY_REWARD      SponsorTransactionType = 4
)

func (p SponsorTransactionType) String() string {
	switch p {
	case SponsorTransactionType_STT_ALL:
		return "SponsorTransactionType_STT_ALL"
	case SponsorTransactionType_STT_APPROPRIATION:
		return "SponsorTransactionType_STT_APPROPRIATION"
	case SponsorTransactionType_STT_RECOVERY:
		return "SponsorTransactionType_STT_RECOVERY"
	case SponsorTransactionType_STT_APPROPRIATION_REWARD:
		return "SponsorTransactionType_STT_APPROPRIATION_REWARD"
	case SponsorTransactionType_STT_RECOVERY_REWARD:
		return "SponsorTransactionType_STT_RECOVERY_REWARD"
	}
	return "<UNSET>"
}

func SponsorTransactionTypeFromString(s string) (SponsorTransactionType, error) {
	switch s {
	case "SponsorTransactionType_STT_ALL":
		return SponsorTransactionType_STT_ALL, nil
	case "SponsorTransactionType_STT_APPROPRIATION":
		return SponsorTransactionType_STT_APPROPRIATION, nil
	case "SponsorTransactionType_STT_RECOVERY":
		return SponsorTransactionType_STT_RECOVERY, nil
	case "SponsorTransactionType_STT_APPROPRIATION_REWARD":
		return SponsorTransactionType_STT_APPROPRIATION_REWARD, nil
	case "SponsorTransactionType_STT_RECOVERY_REWARD":
		return SponsorTransactionType_STT_RECOVERY_REWARD, nil
	}
	return SponsorTransactionType(math.MinInt32 - 1), fmt.Errorf("not a valid SponsorTransactionType string")
}

//@Description("代理商加减款时,操作的金额类型:现金、返货") *
type OperationAmountType int64

const (
	OperationAmountType_OAT_AMOUNTCASH  OperationAmountType = 1
	OperationAmountType_OAT_AMOUNTAWARD OperationAmountType = 2
)

func (p OperationAmountType) String() string {
	switch p {
	case OperationAmountType_OAT_AMOUNTCASH:
		return "OperationAmountType_OAT_AMOUNTCASH"
	case OperationAmountType_OAT_AMOUNTAWARD:
		return "OperationAmountType_OAT_AMOUNTAWARD"
	}
	return "<UNSET>"
}

func OperationAmountTypeFromString(s string) (OperationAmountType, error) {
	switch s {
	case "OperationAmountType_OAT_AMOUNTCASH":
		return OperationAmountType_OAT_AMOUNTCASH, nil
	case "OperationAmountType_OAT_AMOUNTAWARD":
		return OperationAmountType_OAT_AMOUNTAWARD, nil
	}
	return OperationAmountType(math.MinInt32 - 1), fmt.Errorf("not a valid OperationAmountType string")
}

type ExceptionCode int64

const (
	ExceptionCode_EC_GENERIC_SYSTEM_ERROR     ExceptionCode = 50000
	ExceptionCode_EC_DATABASE_READ_ERROR      ExceptionCode = 50001
	ExceptionCode_EC_DATABASE_WRITE_ERROR     ExceptionCode = 50002
	ExceptionCode_EC_GENERIC_CLIENT_ERROR     ExceptionCode = 40000
	ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM ExceptionCode = 40001
)

func (p ExceptionCode) String() string {
	switch p {
	case ExceptionCode_EC_GENERIC_SYSTEM_ERROR:
		return "ExceptionCode_EC_GENERIC_SYSTEM_ERROR"
	case ExceptionCode_EC_DATABASE_READ_ERROR:
		return "ExceptionCode_EC_DATABASE_READ_ERROR"
	case ExceptionCode_EC_DATABASE_WRITE_ERROR:
		return "ExceptionCode_EC_DATABASE_WRITE_ERROR"
	case ExceptionCode_EC_GENERIC_CLIENT_ERROR:
		return "ExceptionCode_EC_GENERIC_CLIENT_ERROR"
	case ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM:
		return "ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM"
	}
	return "<UNSET>"
}

func ExceptionCodeFromString(s string) (ExceptionCode, error) {
	switch s {
	case "ExceptionCode_EC_GENERIC_SYSTEM_ERROR":
		return ExceptionCode_EC_GENERIC_SYSTEM_ERROR, nil
	case "ExceptionCode_EC_DATABASE_READ_ERROR":
		return ExceptionCode_EC_DATABASE_READ_ERROR, nil
	case "ExceptionCode_EC_DATABASE_WRITE_ERROR":
		return ExceptionCode_EC_DATABASE_WRITE_ERROR, nil
	case "ExceptionCode_EC_GENERIC_CLIENT_ERROR":
		return ExceptionCode_EC_GENERIC_CLIENT_ERROR, nil
	case "ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM":
		return ExceptionCode_EC_CLIENT_BAD_REQUEST_PARAM, nil
	}
	return ExceptionCode(math.MinInt32 - 1), fmt.Errorf("not a valid ExceptionCode string")
}

type AgentTransaction struct {
	Time                int64                `thrift:"time,1" json:"time"`
	TypeA1              AgentTransactionType `thrift:"type,2" json:"type"`
	Amount              int64                `thrift:"amount,3" json:"amount"`
	OpeningBalance      int64                `thrift:"openingBalance,4" json:"openingBalance"`
	ClosingBalance      int64                `thrift:"closingBalance,5" json:"closingBalance"`
	OpeningCashBalance  int64                `thrift:"openingCashBalance,6" json:"openingCashBalance"`
	ClosingCashBalance  int64                `thrift:"closingCashBalance,7" json:"closingCashBalance"`
	OpeningAwardBalance int64                `thrift:"openingAwardBalance,8" json:"openingAwardBalance"`
	ClosingAwardBalance int64                `thrift:"closingAwardBalance,9" json:"closingAwardBalance"`
	Id                  int64                `thrift:"id,10" json:"id"`
	AgentUid            int32                `thrift:"agentUid,11" json:"agentUid"`
	SponsorId           int32                `thrift:"sponsorId,12" json:"sponsorId"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Note string `thrift:"note,20" json:"note"`
}

func NewAgentTransaction() *AgentTransaction {
	return &AgentTransaction{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AgentTransaction) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AgentTransaction) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentTransaction) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *AgentTransaction) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = AgentTransactionType(v)
	}
	return nil
}

func (p *AgentTransaction) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *AgentTransaction) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OpeningBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ClosingBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OpeningCashBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ClosingCashBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OpeningAwardBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClosingAwardBalance = v
	}
	return nil
}

func (p *AgentTransaction) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *AgentTransaction) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentTransaction) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *AgentTransaction) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AgentTransaction) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentTransaction"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentTransaction) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransaction) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingBalance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:openingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningBalance)); err != nil {
		return fmt.Errorf("%T.openingBalance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:openingBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingBalance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:closingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingBalance)); err != nil {
		return fmt.Errorf("%T.closingBalance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:closingBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingCashBalance", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:openingCashBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningCashBalance)); err != nil {
		return fmt.Errorf("%T.openingCashBalance (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:openingCashBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingCashBalance", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:closingCashBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingCashBalance)); err != nil {
		return fmt.Errorf("%T.closingCashBalance (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:closingCashBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingAwardBalance", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:openingAwardBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningAwardBalance)); err != nil {
		return fmt.Errorf("%T.openingAwardBalance (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:openingAwardBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingAwardBalance", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:closingAwardBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingAwardBalance)); err != nil {
		return fmt.Errorf("%T.closingAwardBalance (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:closingAwardBalance: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:id: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:sponsorId: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:note: %s", p, err)
	}
	return err
}

func (p *AgentTransaction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentTransaction(%+v)", *p)
}

type AgentTransactionSearchParam struct {
	StartTime int64 `thrift:"startTime,1" json:"startTime"`
	EndTime   int64 `thrift:"endTime,2" json:"endTime"`
	AgentUid  int32 `thrift:"agentUid,3" json:"agentUid"`
	// unused field # 4
	TypeA1 AgentTransactionType   `thrift:"type,5" json:"type"`
	Types  []AgentTransactionType `thrift:"types,6" json:"types"`
}

func NewAgentTransactionSearchParam() *AgentTransactionSearchParam {
	return &AgentTransactionSearchParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AgentTransactionSearchParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AgentTransactionSearchParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = AgentTransactionType(v)
	}
	return nil
}

func (p *AgentTransactionSearchParam) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Types = make([]AgentTransactionType, 0, size)
	for i := 0; i < size; i++ {
		var _elem0 AgentTransactionType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem0 = AgentTransactionType(v)
		}
		p.Types = append(p.Types, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AgentTransactionSearchParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentTransactionSearchParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:agentUid: %s", p, err)
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransactionSearchParam) writeField6(oprot thrift.TProtocol) (err error) {
	if p.Types != nil {
		if err := oprot.WriteFieldBegin("types", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:types: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.Types)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Types {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:types: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransactionSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentTransactionSearchParam(%+v)", *p)
}

type AgentTransactionResult struct {
	TotalCount int32               `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32               `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32               `thrift:"offset,3" json:"offset"`
	Limit      int32               `thrift:"limit,4" json:"limit"`
	Result     []*AgentTransaction `thrift:"result,5" json:"result"`
}

func NewAgentTransactionResult() *AgentTransactionResult {
	return &AgentTransactionResult{}
}

func (p *AgentTransactionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *AgentTransactionResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *AgentTransactionResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *AgentTransactionResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *AgentTransactionResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*AgentTransaction, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewAgentTransaction()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.Result = append(p.Result, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AgentTransactionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AgentTransactionResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AgentTransactionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *AgentTransactionResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *AgentTransactionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AgentTransactionResult(%+v)", *p)
}

type SponsorTransaction struct {
	Time                int64                  `thrift:"time,1" json:"time"`
	TypeA1              SponsorTransactionType `thrift:"type,2" json:"type"`
	Amount              int64                  `thrift:"amount,3" json:"amount"`
	OpeningBalance      int64                  `thrift:"openingBalance,4" json:"openingBalance"`
	ClosingBalance      int64                  `thrift:"closingBalance,5" json:"closingBalance"`
	OpeningCashBalance  int64                  `thrift:"openingCashBalance,6" json:"openingCashBalance"`
	ClosingCashBalance  int64                  `thrift:"closingCashBalance,7" json:"closingCashBalance"`
	OpeningAwardBalance int64                  `thrift:"openingAwardBalance,8" json:"openingAwardBalance"`
	ClosingAwardBalance int64                  `thrift:"closingAwardBalance,9" json:"closingAwardBalance"`
	Id                  int64                  `thrift:"id,10" json:"id"`
	SponsorId           int32                  `thrift:"sponsorId,11" json:"sponsorId"`
	AgentUid            int32                  `thrift:"agentUid,12" json:"agentUid"`
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	Note string `thrift:"note,20" json:"note"`
}

func NewSponsorTransaction() *SponsorTransaction {
	return &SponsorTransaction{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SponsorTransaction) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SponsorTransaction) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransaction) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Time = v
	}
	return nil
}

func (p *SponsorTransaction) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = SponsorTransactionType(v)
	}
	return nil
}

func (p *SponsorTransaction) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Amount = v
	}
	return nil
}

func (p *SponsorTransaction) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.OpeningBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.ClosingBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.OpeningCashBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ClosingCashBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.OpeningAwardBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.ClosingAwardBalance = v
	}
	return nil
}

func (p *SponsorTransaction) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Id = v
	}
	return nil
}

func (p *SponsorTransaction) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *SponsorTransaction) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorTransaction) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *SponsorTransaction) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorTransaction"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransaction) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("time", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:time: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Time)); err != nil {
		return fmt.Errorf("%T.time (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:time: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField2(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (2) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:type: %s", p, err)
		}
	}
	return err
}

func (p *SponsorTransaction) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("amount", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:amount: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Amount)); err != nil {
		return fmt.Errorf("%T.amount (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:amount: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingBalance", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:openingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningBalance)); err != nil {
		return fmt.Errorf("%T.openingBalance (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:openingBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingBalance", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:closingBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingBalance)); err != nil {
		return fmt.Errorf("%T.closingBalance (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:closingBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingCashBalance", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:openingCashBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningCashBalance)); err != nil {
		return fmt.Errorf("%T.openingCashBalance (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:openingCashBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingCashBalance", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:closingCashBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingCashBalance)); err != nil {
		return fmt.Errorf("%T.closingCashBalance (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:closingCashBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("openingAwardBalance", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:openingAwardBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.OpeningAwardBalance)); err != nil {
		return fmt.Errorf("%T.openingAwardBalance (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:openingAwardBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closingAwardBalance", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:closingAwardBalance: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.ClosingAwardBalance)); err != nil {
		return fmt.Errorf("%T.closingAwardBalance (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:closingAwardBalance: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Id)); err != nil {
		return fmt.Errorf("%T.id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:id: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:sponsorId: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:note: %s", p, err)
	}
	return err
}

func (p *SponsorTransaction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorTransaction(%+v)", *p)
}

type SponsorTransactionSearchParam struct {
	StartTime int64                  `thrift:"startTime,1" json:"startTime"`
	EndTime   int64                  `thrift:"endTime,2" json:"endTime"`
	AgentUid  int32                  `thrift:"agentUid,3" json:"agentUid"`
	SponsorId int32                  `thrift:"sponsorId,4" json:"sponsorId"`
	TypeA1    SponsorTransactionType `thrift:"type,5" json:"type"`
}

func NewSponsorTransactionSearchParam() *SponsorTransactionSearchParam {
	return &SponsorTransactionSearchParam{
		TypeA1: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *SponsorTransactionSearchParam) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *SponsorTransactionSearchParam) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I64 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.StartTime = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.EndTime = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AgentUid = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *SponsorTransactionSearchParam) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = SponsorTransactionType(v)
	}
	return nil
}

func (p *SponsorTransactionSearchParam) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorTransactionSearchParam"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionSearchParam) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:startTime: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:endTime: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("agentUid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:agentUid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AgentUid)); err != nil {
		return fmt.Errorf("%T.agentUid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:agentUid: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsorId", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:sponsorId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsorId (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:sponsorId: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionSearchParam) writeField5(oprot thrift.TProtocol) (err error) {
	if p.IsSetTypeA1() {
		if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
			return fmt.Errorf("%T.type (5) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:type: %s", p, err)
		}
	}
	return err
}

func (p *SponsorTransactionSearchParam) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorTransactionSearchParam(%+v)", *p)
}

type SponsorTransactionResult struct {
	TotalCount int32                 `thrift:"totalCount,1" json:"totalCount"`
	MaxLimit   int32                 `thrift:"maxLimit,2" json:"maxLimit"`
	Offset     int32                 `thrift:"offset,3" json:"offset"`
	Limit      int32                 `thrift:"limit,4" json:"limit"`
	Result     []*SponsorTransaction `thrift:"result,5" json:"result"`
}

func NewSponsorTransactionResult() *SponsorTransactionResult {
	return &SponsorTransactionResult{}
}

func (p *SponsorTransactionResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.LIST {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.TotalCount = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.MaxLimit = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Offset = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Limit = v
	}
	return nil
}

func (p *SponsorTransactionResult) readField5(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Result = make([]*SponsorTransaction, 0, size)
	for i := 0; i < size; i++ {
		_elem2 := NewSponsorTransaction()
		if err := _elem2.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem2)
		}
		p.Result = append(p.Result, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *SponsorTransactionResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("SponsorTransactionResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *SponsorTransactionResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalCount", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:totalCount: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TotalCount)); err != nil {
		return fmt.Errorf("%T.totalCount (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:totalCount: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("maxLimit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:maxLimit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.MaxLimit)); err != nil {
		return fmt.Errorf("%T.maxLimit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:maxLimit: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("offset", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:offset: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Offset)); err != nil {
		return fmt.Errorf("%T.offset (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:offset: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("limit", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Limit)); err != nil {
		return fmt.Errorf("%T.limit (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:limit: %s", p, err)
	}
	return err
}

func (p *SponsorTransactionResult) writeField5(oprot thrift.TProtocol) (err error) {
	if p.Result != nil {
		if err := oprot.WriteFieldBegin("result", thrift.LIST, 5); err != nil {
			return fmt.Errorf("%T write field begin error 5:result: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Result)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Result {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 5:result: %s", p, err)
		}
	}
	return err
}

func (p *SponsorTransactionResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("SponsorTransactionResult(%+v)", *p)
}

type AppropriationTransaction struct {
	AgentTransation    *AgentTransaction   `thrift:"agentTransation,1" json:"agentTransation"`
	SponsorTransaction *SponsorTransaction `thrift:"sponsorTransaction,2" json:"sponsorTransaction"`
}

func NewAppropriationTransaction() *AppropriationTransaction {
	return &AppropriationTransaction{}
}

func (p *AppropriationTransaction) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AppropriationTransaction) readField1(iprot thrift.TProtocol) error {
	p.AgentTransation = NewAgentTransaction()
	if err := p.AgentTransation.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.AgentTransation)
	}
	return nil
}

func (p *AppropriationTransaction) readField2(iprot thrift.TProtocol) error {
	p.SponsorTransaction = NewSponsorTransaction()
	if err := p.SponsorTransaction.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.SponsorTransaction)
	}
	return nil
}

func (p *AppropriationTransaction) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AppropriationTransaction"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AppropriationTransaction) writeField1(oprot thrift.TProtocol) (err error) {
	if p.AgentTransation != nil {
		if err := oprot.WriteFieldBegin("agentTransation", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:agentTransation: %s", p, err)
		}
		if err := p.AgentTransation.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.AgentTransation)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:agentTransation: %s", p, err)
		}
	}
	return err
}

func (p *AppropriationTransaction) writeField2(oprot thrift.TProtocol) (err error) {
	if p.SponsorTransaction != nil {
		if err := oprot.WriteFieldBegin("sponsorTransaction", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:sponsorTransaction: %s", p, err)
		}
		if err := p.SponsorTransaction.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.SponsorTransaction)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:sponsorTransaction: %s", p, err)
		}
	}
	return err
}

func (p *AppropriationTransaction) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AppropriationTransaction(%+v)", *p)
}

type BidMasterFinanceServerException struct {
	Code    ExceptionCode `thrift:"code,1" json:"code"`
	Message string        `thrift:"message,2" json:"message"`
}

func NewBidMasterFinanceServerException() *BidMasterFinanceServerException {
	return &BidMasterFinanceServerException{
		Code: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *BidMasterFinanceServerException) IsSetCode() bool {
	return int64(p.Code) != math.MinInt32-1
}

func (p *BidMasterFinanceServerException) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *BidMasterFinanceServerException) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Code = ExceptionCode(v)
	}
	return nil
}

func (p *BidMasterFinanceServerException) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Message = v
	}
	return nil
}

func (p *BidMasterFinanceServerException) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("BidMasterFinanceServerException"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *BidMasterFinanceServerException) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetCode() {
		if err := oprot.WriteFieldBegin("code", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:code: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Code)); err != nil {
			return fmt.Errorf("%T.code (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:code: %s", p, err)
		}
	}
	return err
}

func (p *BidMasterFinanceServerException) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("message", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:message: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Message)); err != nil {
		return fmt.Errorf("%T.message (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:message: %s", p, err)
	}
	return err
}

func (p *BidMasterFinanceServerException) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("BidMasterFinanceServerException(%+v)", *p)
}
