// Autogenerated by <PERSON>hrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package main

import (
	"bidmaster_finance"
	"flag"
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
)

func Usage() {
	fmt.Fprintln(os.Stderr, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
	flag.PrintDefaults()
	fmt.Fprintln(os.Stderr, "\nFunctions:")
	fmt.Fprintln(os.<PERSON>derr, "  AgentTransaction agentRecharge(RequestHeader header, i32 agentUid, i64 amount, i64 cashRecharged, string note)")
	fmt.Fprintln(os.<PERSON>derr, "  AgentTransaction agentRefund(RequestHeader header, i32 agentUid, i64 amount, i64 cashBalance, string note)")
	fmt.Fprintln(os.Stderr, "  AgentTransaction rewardAgent(RequestHeader header, i32 agentUid, i64 amount, i64 award, string note)")
	fmt.Fprintln(os.Stderr, "  AgentTransaction agentRefundReward(RequestHeader header, i32 agentUid, i64 amount, i64 cashBalance, string note)")
	fmt.Fprintln(os.Stderr, "  AppropriationTransaction performAppropriation(RequestHeader header, i32 agentUid, i32 sponsorId, i64 amount, i64 balance, OperationAmountType amountType, string note)")
	fmt.Fprintln(os.Stderr, "  AppropriationTransaction performRecovery(RequestHeader header, i32 agentUid, i32 sponsorId, i64 amount, i64 balance, OperationAmountType amountType, string note)")
	fmt.Fprintln(os.Stderr, "  AgentTransactionResult queryAgentTransaction(RequestHeader header, AgentTransactionSearchParam param, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "  SponsorTransactionResult querySponsorTransaction(RequestHeader header, SponsorTransactionSearchParam param, i32 offset, i32 limit)")
	fmt.Fprintln(os.Stderr, "   getAgentAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "   getSponsorAccountsByIds(RequestHeader header,  ids)")
	fmt.Fprintln(os.Stderr, "  void editSponsorDailyBudget(RequestHeader header, i32 agentUid, i32 sponsorId, i64 dailyBudget)")
	fmt.Fprintln(os.Stderr)
	os.Exit(0)
}

func main() {
	flag.Usage = Usage
	var host string
	var port int
	var protocol string
	var urlString string
	var framed bool
	var useHttp bool
	var parsedUrl url.URL
	var trans thrift.TTransport
	_ = math.MinInt32 // will become unneeded eventually
	_ = strconv.Atoi
	flag.Usage = Usage
	flag.StringVar(&host, "h", "localhost", "Specify host and port")
	flag.IntVar(&port, "p", 9090, "Specify port")
	flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
	flag.StringVar(&urlString, "u", "", "Specify the url")
	flag.BoolVar(&framed, "framed", false, "Use framed transport")
	flag.BoolVar(&useHttp, "http", false, "Use http")
	flag.Parse()

	if len(urlString) > 0 {
		parsedUrl, err := url.Parse(urlString)
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
		host = parsedUrl.Host
		useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http"
	} else if useHttp {
		_, err := url.Parse(fmt.Sprint("http://", host, ":", port))
		if err != nil {
			fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
			flag.Usage()
		}
	}

	cmd := flag.Arg(0)
	var err error
	if useHttp {
		trans, err = thrift.NewTHttpClient(parsedUrl.String())
	} else {
		portStr := fmt.Sprint(port)
		if strings.Contains(host, ":") {
			host, portStr, err = net.SplitHostPort(host)
			if err != nil {
				fmt.Fprintln(os.Stderr, "error with host:", err)
				os.Exit(1)
			}
		}
		trans, err = thrift.NewTSocket(net.JoinHostPort(host, portStr))
		if err != nil {
			fmt.Fprintln(os.Stderr, "error resolving address:", err)
			os.Exit(1)
		}
		if framed {
			trans = thrift.NewTFramedTransport(trans)
		}
	}
	if err != nil {
		fmt.Fprintln(os.Stderr, "Error creating transport", err)
		os.Exit(1)
	}
	defer trans.Close()
	var protocolFactory thrift.TProtocolFactory
	switch protocol {
	case "compact":
		protocolFactory = thrift.NewTCompactProtocolFactory()
		break
	case "simplejson":
		protocolFactory = thrift.NewTSimpleJSONProtocolFactory()
		break
	case "json":
		protocolFactory = thrift.NewTJSONProtocolFactory()
		break
	case "binary", "":
		protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
		Usage()
		os.Exit(1)
	}
	client := bidmaster_finance.NewBidMasterFinanceServerClientFactory(trans, protocolFactory)
	if err := trans.Open(); err != nil {
		fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
		os.Exit(1)
	}

	switch cmd {
	case "agentRecharge":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "AgentRecharge requires 5 args")
			flag.Usage()
		}
		arg55 := flag.Arg(1)
		mbTrans56 := thrift.NewTMemoryBufferLen(len(arg55))
		defer mbTrans56.Close()
		_, err57 := mbTrans56.WriteString(arg55)
		if err57 != nil {
			Usage()
			return
		}
		factory58 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt59 := factory58.GetProtocol(mbTrans56)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err60 := argvalue0.Read(jsProt59)
		if err60 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err61 := (strconv.Atoi(flag.Arg(2)))
		if err61 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err62 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err62 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err63 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err63 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.AgentRecharge(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "agentRefund":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "AgentRefund requires 5 args")
			flag.Usage()
		}
		arg65 := flag.Arg(1)
		mbTrans66 := thrift.NewTMemoryBufferLen(len(arg65))
		defer mbTrans66.Close()
		_, err67 := mbTrans66.WriteString(arg65)
		if err67 != nil {
			Usage()
			return
		}
		factory68 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt69 := factory68.GetProtocol(mbTrans66)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err70 := argvalue0.Read(jsProt69)
		if err70 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err71 := (strconv.Atoi(flag.Arg(2)))
		if err71 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err72 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err72 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err73 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err73 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.AgentRefund(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "rewardAgent":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "RewardAgent requires 5 args")
			flag.Usage()
		}
		arg75 := flag.Arg(1)
		mbTrans76 := thrift.NewTMemoryBufferLen(len(arg75))
		defer mbTrans76.Close()
		_, err77 := mbTrans76.WriteString(arg75)
		if err77 != nil {
			Usage()
			return
		}
		factory78 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt79 := factory78.GetProtocol(mbTrans76)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err80 := argvalue0.Read(jsProt79)
		if err80 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err81 := (strconv.Atoi(flag.Arg(2)))
		if err81 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err82 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err82 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err83 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err83 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.RewardAgent(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "agentRefundReward":
		if flag.NArg()-1 != 5 {
			fmt.Fprintln(os.Stderr, "AgentRefundReward requires 5 args")
			flag.Usage()
		}
		arg85 := flag.Arg(1)
		mbTrans86 := thrift.NewTMemoryBufferLen(len(arg85))
		defer mbTrans86.Close()
		_, err87 := mbTrans86.WriteString(arg85)
		if err87 != nil {
			Usage()
			return
		}
		factory88 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt89 := factory88.GetProtocol(mbTrans86)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err90 := argvalue0.Read(jsProt89)
		if err90 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err91 := (strconv.Atoi(flag.Arg(2)))
		if err91 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		argvalue2, err92 := (strconv.ParseInt(flag.Arg(3), 10, 64))
		if err92 != nil {
			Usage()
			return
		}
		value2 := argvalue2
		argvalue3, err93 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err93 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4 := flag.Arg(5)
		value4 := argvalue4
		fmt.Print(client.AgentRefundReward(value0, value1, value2, value3, value4))
		fmt.Print("\n")
		break
	case "performAppropriation":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "PerformAppropriation requires 7 args")
			flag.Usage()
		}
		arg95 := flag.Arg(1)
		mbTrans96 := thrift.NewTMemoryBufferLen(len(arg95))
		defer mbTrans96.Close()
		_, err97 := mbTrans96.WriteString(arg95)
		if err97 != nil {
			Usage()
			return
		}
		factory98 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt99 := factory98.GetProtocol(mbTrans96)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err100 := argvalue0.Read(jsProt99)
		if err100 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err101 := (strconv.Atoi(flag.Arg(2)))
		if err101 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err102 := (strconv.Atoi(flag.Arg(3)))
		if err102 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err103 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err103 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err104 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err104 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := bidmaster_finance.OperationAmountType(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.PerformAppropriation(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "performRecovery":
		if flag.NArg()-1 != 7 {
			fmt.Fprintln(os.Stderr, "PerformRecovery requires 7 args")
			flag.Usage()
		}
		arg106 := flag.Arg(1)
		mbTrans107 := thrift.NewTMemoryBufferLen(len(arg106))
		defer mbTrans107.Close()
		_, err108 := mbTrans107.WriteString(arg106)
		if err108 != nil {
			Usage()
			return
		}
		factory109 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt110 := factory109.GetProtocol(mbTrans107)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err111 := argvalue0.Read(jsProt110)
		if err111 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err112 := (strconv.Atoi(flag.Arg(2)))
		if err112 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err113 := (strconv.Atoi(flag.Arg(3)))
		if err113 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err114 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err114 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		argvalue4, err115 := (strconv.ParseInt(flag.Arg(5), 10, 64))
		if err115 != nil {
			Usage()
			return
		}
		value4 := argvalue4
		tmp5, err := (strconv.Atoi(flag.Arg(6)))
		if err != nil {
			Usage()
			return
		}
		argvalue5 := bidmaster_finance.OperationAmountType(tmp5)
		value5 := argvalue5
		argvalue6 := flag.Arg(7)
		value6 := argvalue6
		fmt.Print(client.PerformRecovery(value0, value1, value2, value3, value4, value5, value6))
		fmt.Print("\n")
		break
	case "queryAgentTransaction":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QueryAgentTransaction requires 4 args")
			flag.Usage()
		}
		arg117 := flag.Arg(1)
		mbTrans118 := thrift.NewTMemoryBufferLen(len(arg117))
		defer mbTrans118.Close()
		_, err119 := mbTrans118.WriteString(arg117)
		if err119 != nil {
			Usage()
			return
		}
		factory120 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt121 := factory120.GetProtocol(mbTrans118)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err122 := argvalue0.Read(jsProt121)
		if err122 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg123 := flag.Arg(2)
		mbTrans124 := thrift.NewTMemoryBufferLen(len(arg123))
		defer mbTrans124.Close()
		_, err125 := mbTrans124.WriteString(arg123)
		if err125 != nil {
			Usage()
			return
		}
		factory126 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt127 := factory126.GetProtocol(mbTrans124)
		argvalue1 := bidmaster_finance.NewAgentTransactionSearchParam()
		err128 := argvalue1.Read(jsProt127)
		if err128 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err129 := (strconv.Atoi(flag.Arg(3)))
		if err129 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err130 := (strconv.Atoi(flag.Arg(4)))
		if err130 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QueryAgentTransaction(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "querySponsorTransaction":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "QuerySponsorTransaction requires 4 args")
			flag.Usage()
		}
		arg131 := flag.Arg(1)
		mbTrans132 := thrift.NewTMemoryBufferLen(len(arg131))
		defer mbTrans132.Close()
		_, err133 := mbTrans132.WriteString(arg131)
		if err133 != nil {
			Usage()
			return
		}
		factory134 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt135 := factory134.GetProtocol(mbTrans132)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err136 := argvalue0.Read(jsProt135)
		if err136 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg137 := flag.Arg(2)
		mbTrans138 := thrift.NewTMemoryBufferLen(len(arg137))
		defer mbTrans138.Close()
		_, err139 := mbTrans138.WriteString(arg137)
		if err139 != nil {
			Usage()
			return
		}
		factory140 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt141 := factory140.GetProtocol(mbTrans138)
		argvalue1 := bidmaster_finance.NewSponsorTransactionSearchParam()
		err142 := argvalue1.Read(jsProt141)
		if err142 != nil {
			Usage()
			return
		}
		value1 := argvalue1
		tmp2, err143 := (strconv.Atoi(flag.Arg(3)))
		if err143 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		tmp3, err144 := (strconv.Atoi(flag.Arg(4)))
		if err144 != nil {
			Usage()
			return
		}
		argvalue3 := int32(tmp3)
		value3 := argvalue3
		fmt.Print(client.QuerySponsorTransaction(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "getAgentAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetAgentAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg145 := flag.Arg(1)
		mbTrans146 := thrift.NewTMemoryBufferLen(len(arg145))
		defer mbTrans146.Close()
		_, err147 := mbTrans146.WriteString(arg145)
		if err147 != nil {
			Usage()
			return
		}
		factory148 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt149 := factory148.GetProtocol(mbTrans146)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err150 := argvalue0.Read(jsProt149)
		if err150 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg151 := flag.Arg(2)
		mbTrans152 := thrift.NewTMemoryBufferLen(len(arg151))
		defer mbTrans152.Close()
		_, err153 := mbTrans152.WriteString(arg151)
		if err153 != nil {
			Usage()
			return
		}
		factory154 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt155 := factory154.GetProtocol(mbTrans152)
		containerStruct1 := bidmaster_finance.NewGetAgentAccountsByIdsArgs()
		err156 := containerStruct1.ReadField2(jsProt155)
		if err156 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetAgentAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "getSponsorAccountsByIds":
		if flag.NArg()-1 != 2 {
			fmt.Fprintln(os.Stderr, "GetSponsorAccountsByIds requires 2 args")
			flag.Usage()
		}
		arg157 := flag.Arg(1)
		mbTrans158 := thrift.NewTMemoryBufferLen(len(arg157))
		defer mbTrans158.Close()
		_, err159 := mbTrans158.WriteString(arg157)
		if err159 != nil {
			Usage()
			return
		}
		factory160 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt161 := factory160.GetProtocol(mbTrans158)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err162 := argvalue0.Read(jsProt161)
		if err162 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		arg163 := flag.Arg(2)
		mbTrans164 := thrift.NewTMemoryBufferLen(len(arg163))
		defer mbTrans164.Close()
		_, err165 := mbTrans164.WriteString(arg163)
		if err165 != nil {
			Usage()
			return
		}
		factory166 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt167 := factory166.GetProtocol(mbTrans164)
		containerStruct1 := bidmaster_finance.NewGetSponsorAccountsByIdsArgs()
		err168 := containerStruct1.ReadField2(jsProt167)
		if err168 != nil {
			Usage()
			return
		}
		argvalue1 := containerStruct1.Ids
		value1 := argvalue1
		fmt.Print(client.GetSponsorAccountsByIds(value0, value1))
		fmt.Print("\n")
		break
	case "editSponsorDailyBudget":
		if flag.NArg()-1 != 4 {
			fmt.Fprintln(os.Stderr, "EditSponsorDailyBudget requires 4 args")
			flag.Usage()
		}
		arg169 := flag.Arg(1)
		mbTrans170 := thrift.NewTMemoryBufferLen(len(arg169))
		defer mbTrans170.Close()
		_, err171 := mbTrans170.WriteString(arg169)
		if err171 != nil {
			Usage()
			return
		}
		factory172 := thrift.NewTSimpleJSONProtocolFactory()
		jsProt173 := factory172.GetProtocol(mbTrans170)
		argvalue0 := bidmaster_finance.NewRequestHeader()
		err174 := argvalue0.Read(jsProt173)
		if err174 != nil {
			Usage()
			return
		}
		value0 := argvalue0
		tmp1, err175 := (strconv.Atoi(flag.Arg(2)))
		if err175 != nil {
			Usage()
			return
		}
		argvalue1 := int32(tmp1)
		value1 := argvalue1
		tmp2, err176 := (strconv.Atoi(flag.Arg(3)))
		if err176 != nil {
			Usage()
			return
		}
		argvalue2 := int32(tmp2)
		value2 := argvalue2
		argvalue3, err177 := (strconv.ParseInt(flag.Arg(4), 10, 64))
		if err177 != nil {
			Usage()
			return
		}
		value3 := argvalue3
		fmt.Print(client.EditSponsorDailyBudget(value0, value1, value2, value3))
		fmt.Print("\n")
		break
	case "":
		Usage()
		break
	default:
		fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
	}
}
