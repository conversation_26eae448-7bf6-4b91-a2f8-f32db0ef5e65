// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package adinfo_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/tag_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = tag_types.GoUnusedProtection__
var GoUnusedProtection__ int

//广告的频次类型定义，0 表示展现的，1 表示点击的
type FrequencyType int64

const (
	FrequencyType_FREQUENCYTYPE_IMP FrequencyType = 0
	FrequencyType_FREQUENCYTYPE_CLK FrequencyType = 1
)

func (p FrequencyType) String() string {
	switch p {
	case FrequencyType_FREQUENCYTYPE_IMP:
		return "FrequencyType_FREQUENCYTYPE_IMP"
	case FrequencyType_FREQUENCYTYPE_CLK:
		return "FrequencyType_FREQUENCYTYPE_CLK"
	}
	return "<UNSET>"
}

func FrequencyTypeFromString(s string) (FrequencyType, error) {
	switch s {
	case "FrequencyType_FREQUENCYTYPE_IMP":
		return FrequencyType_FREQUENCYTYPE_IMP, nil
	case "FrequencyType_FREQUENCYTYPE_CLK":
		return FrequencyType_FREQUENCYTYPE_CLK, nil
	}
	return FrequencyType(math.MinInt32 - 1), fmt.Errorf("not a valid FrequencyType string")
}

//合同类型枚举，含义参考合同类型常量
type ContractType int64

const (
	ContractType_CONTRACTTYPE_UNKNOWN ContractType = 0
	ContractType_CONTRACTTYPE_PAY     ContractType = 1
	ContractType_CONTRACTTYPE_FREE    ContractType = 2
)

func (p ContractType) String() string {
	switch p {
	case ContractType_CONTRACTTYPE_UNKNOWN:
		return "ContractType_CONTRACTTYPE_UNKNOWN"
	case ContractType_CONTRACTTYPE_PAY:
		return "ContractType_CONTRACTTYPE_PAY"
	case ContractType_CONTRACTTYPE_FREE:
		return "ContractType_CONTRACTTYPE_FREE"
	}
	return "<UNSET>"
}

func ContractTypeFromString(s string) (ContractType, error) {
	switch s {
	case "ContractType_CONTRACTTYPE_UNKNOWN":
		return ContractType_CONTRACTTYPE_UNKNOWN, nil
	case "ContractType_CONTRACTTYPE_PAY":
		return ContractType_CONTRACTTYPE_PAY, nil
	case "ContractType_CONTRACTTYPE_FREE":
		return ContractType_CONTRACTTYPE_FREE, nil
	}
	return ContractType(math.MinInt32 - 1), fmt.Errorf("not a valid ContractType string")
}

//广告推广计划的业务类型，0 表示普通计划，1 表示App计划，2 表示排期单计划
type BusinessType int64

const (
	BusinessType_BUSINESSTYPE_NORMAL   BusinessType = 0
	BusinessType_BUSINESSTYPE_APP      BusinessType = 1
	BusinessType_BUSINESSTYPE_SCHEDULE BusinessType = 2
)

func (p BusinessType) String() string {
	switch p {
	case BusinessType_BUSINESSTYPE_NORMAL:
		return "BusinessType_BUSINESSTYPE_NORMAL"
	case BusinessType_BUSINESSTYPE_APP:
		return "BusinessType_BUSINESSTYPE_APP"
	case BusinessType_BUSINESSTYPE_SCHEDULE:
		return "BusinessType_BUSINESSTYPE_SCHEDULE"
	}
	return "<UNSET>"
}

func BusinessTypeFromString(s string) (BusinessType, error) {
	switch s {
	case "BusinessType_BUSINESSTYPE_NORMAL":
		return BusinessType_BUSINESSTYPE_NORMAL, nil
	case "BusinessType_BUSINESSTYPE_APP":
		return BusinessType_BUSINESSTYPE_APP, nil
	case "BusinessType_BUSINESSTYPE_SCHEDULE":
		return BusinessType_BUSINESSTYPE_SCHEDULE, nil
	}
	return BusinessType(math.MinInt32 - 1), fmt.Errorf("not a valid BusinessType string")
}

//广告domob水印状态
type AdWaterMarkStatus int64

const (
	AdWaterMarkStatus_AWMS_UNKNOWN AdWaterMarkStatus = 0
	AdWaterMarkStatus_AWMS_USED    AdWaterMarkStatus = 1
	AdWaterMarkStatus_AWMS_UNUSED  AdWaterMarkStatus = 2
)

func (p AdWaterMarkStatus) String() string {
	switch p {
	case AdWaterMarkStatus_AWMS_UNKNOWN:
		return "AdWaterMarkStatus_AWMS_UNKNOWN"
	case AdWaterMarkStatus_AWMS_USED:
		return "AdWaterMarkStatus_AWMS_USED"
	case AdWaterMarkStatus_AWMS_UNUSED:
		return "AdWaterMarkStatus_AWMS_UNUSED"
	}
	return "<UNSET>"
}

func AdWaterMarkStatusFromString(s string) (AdWaterMarkStatus, error) {
	switch s {
	case "AdWaterMarkStatus_AWMS_UNKNOWN":
		return AdWaterMarkStatus_AWMS_UNKNOWN, nil
	case "AdWaterMarkStatus_AWMS_USED":
		return AdWaterMarkStatus_AWMS_USED, nil
	case "AdWaterMarkStatus_AWMS_UNUSED":
		return AdWaterMarkStatus_AWMS_UNUSED, nil
	}
	return AdWaterMarkStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdWaterMarkStatus string")
}

//广告暂停开关状态
type AdPauseStatus int64

const (
	AdPauseStatus_APAS_RUNNABLE AdPauseStatus = 0
	AdPauseStatus_APAS_PAUSED   AdPauseStatus = 1
)

func (p AdPauseStatus) String() string {
	switch p {
	case AdPauseStatus_APAS_RUNNABLE:
		return "AdPauseStatus_APAS_RUNNABLE"
	case AdPauseStatus_APAS_PAUSED:
		return "AdPauseStatus_APAS_PAUSED"
	}
	return "<UNSET>"
}

func AdPauseStatusFromString(s string) (AdPauseStatus, error) {
	switch s {
	case "AdPauseStatus_APAS_RUNNABLE":
		return AdPauseStatus_APAS_RUNNABLE, nil
	case "AdPauseStatus_APAS_PAUSED":
		return AdPauseStatus_APAS_PAUSED, nil
	}
	return AdPauseStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdPauseStatus string")
}

//广告推广计划状态
type AdPlanStatus int64

const (
	AdPlanStatus_APS_RUNNABLE AdPlanStatus = 0
	AdPlanStatus_APS_DELETED  AdPlanStatus = 1
)

func (p AdPlanStatus) String() string {
	switch p {
	case AdPlanStatus_APS_RUNNABLE:
		return "AdPlanStatus_APS_RUNNABLE"
	case AdPlanStatus_APS_DELETED:
		return "AdPlanStatus_APS_DELETED"
	}
	return "<UNSET>"
}

func AdPlanStatusFromString(s string) (AdPlanStatus, error) {
	switch s {
	case "AdPlanStatus_APS_RUNNABLE":
		return AdPlanStatus_APS_RUNNABLE, nil
	case "AdPlanStatus_APS_DELETED":
		return AdPlanStatus_APS_DELETED, nil
	}
	return AdPlanStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdPlanStatus string")
}

//广告推广策略状态
type AdStrategyStatus int64

const (
	AdStrategyStatus_ASS_RUNNABLE AdStrategyStatus = 0
	AdStrategyStatus_ASS_DELETED  AdStrategyStatus = 1
)

func (p AdStrategyStatus) String() string {
	switch p {
	case AdStrategyStatus_ASS_RUNNABLE:
		return "AdStrategyStatus_ASS_RUNNABLE"
	case AdStrategyStatus_ASS_DELETED:
		return "AdStrategyStatus_ASS_DELETED"
	}
	return "<UNSET>"
}

func AdStrategyStatusFromString(s string) (AdStrategyStatus, error) {
	switch s {
	case "AdStrategyStatus_ASS_RUNNABLE":
		return AdStrategyStatus_ASS_RUNNABLE, nil
	case "AdStrategyStatus_ASS_DELETED":
		return AdStrategyStatus_ASS_DELETED, nil
	}
	return AdStrategyStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdStrategyStatus string")
}

//广告创意状态
type AdCreativeStatus int64

const (
	AdCreativeStatus_ACS_DRAFT            AdCreativeStatus = 0
	AdCreativeStatus_ACS_PENDING_APPROVAL AdCreativeStatus = 1
	AdCreativeStatus_ACS_RUNNABLE         AdCreativeStatus = 2
	AdCreativeStatus_ACS_REJECTED         AdCreativeStatus = 3
	AdCreativeStatus_ACS_DELETED          AdCreativeStatus = 4
)

func (p AdCreativeStatus) String() string {
	switch p {
	case AdCreativeStatus_ACS_DRAFT:
		return "AdCreativeStatus_ACS_DRAFT"
	case AdCreativeStatus_ACS_PENDING_APPROVAL:
		return "AdCreativeStatus_ACS_PENDING_APPROVAL"
	case AdCreativeStatus_ACS_RUNNABLE:
		return "AdCreativeStatus_ACS_RUNNABLE"
	case AdCreativeStatus_ACS_REJECTED:
		return "AdCreativeStatus_ACS_REJECTED"
	case AdCreativeStatus_ACS_DELETED:
		return "AdCreativeStatus_ACS_DELETED"
	}
	return "<UNSET>"
}

func AdCreativeStatusFromString(s string) (AdCreativeStatus, error) {
	switch s {
	case "AdCreativeStatus_ACS_DRAFT":
		return AdCreativeStatus_ACS_DRAFT, nil
	case "AdCreativeStatus_ACS_PENDING_APPROVAL":
		return AdCreativeStatus_ACS_PENDING_APPROVAL, nil
	case "AdCreativeStatus_ACS_RUNNABLE":
		return AdCreativeStatus_ACS_RUNNABLE, nil
	case "AdCreativeStatus_ACS_REJECTED":
		return AdCreativeStatus_ACS_REJECTED, nil
	case "AdCreativeStatus_ACS_DELETED":
		return AdCreativeStatus_ACS_DELETED, nil
	}
	return AdCreativeStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdCreativeStatus string")
}

//目前仅用于代理商系统，根据被代理的广告主ID获取该广告主的用户简要状态
type AdUserBriefStatus int64

const (
	AdUserBriefStatus_AUBS_INACTIVE AdUserBriefStatus = 1
	AdUserBriefStatus_AUBS_ENDED    AdUserBriefStatus = 2
	AdUserBriefStatus_AUBS_ACTIVE   AdUserBriefStatus = 3
)

func (p AdUserBriefStatus) String() string {
	switch p {
	case AdUserBriefStatus_AUBS_INACTIVE:
		return "AdUserBriefStatus_AUBS_INACTIVE"
	case AdUserBriefStatus_AUBS_ENDED:
		return "AdUserBriefStatus_AUBS_ENDED"
	case AdUserBriefStatus_AUBS_ACTIVE:
		return "AdUserBriefStatus_AUBS_ACTIVE"
	}
	return "<UNSET>"
}

func AdUserBriefStatusFromString(s string) (AdUserBriefStatus, error) {
	switch s {
	case "AdUserBriefStatus_AUBS_INACTIVE":
		return AdUserBriefStatus_AUBS_INACTIVE, nil
	case "AdUserBriefStatus_AUBS_ENDED":
		return AdUserBriefStatus_AUBS_ENDED, nil
	case "AdUserBriefStatus_AUBS_ACTIVE":
		return AdUserBriefStatus_AUBS_ACTIVE, nil
	}
	return AdUserBriefStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AdUserBriefStatus string")
}

//状态灯颜色
type AdStatusLampColor int64

const (
	AdStatusLampColor_ASLCR_GREEN               AdStatusLampColor = 1
	AdStatusLampColor_ASLCR_YELLOW              AdStatusLampColor = 2
	AdStatusLampColor_ASLCR_YELLOW_WITH_WARNING AdStatusLampColor = 3
	AdStatusLampColor_ASLCR_RED                 AdStatusLampColor = 4
	AdStatusLampColor_ASLCR_EMPTY               AdStatusLampColor = 5
)

func (p AdStatusLampColor) String() string {
	switch p {
	case AdStatusLampColor_ASLCR_GREEN:
		return "AdStatusLampColor_ASLCR_GREEN"
	case AdStatusLampColor_ASLCR_YELLOW:
		return "AdStatusLampColor_ASLCR_YELLOW"
	case AdStatusLampColor_ASLCR_YELLOW_WITH_WARNING:
		return "AdStatusLampColor_ASLCR_YELLOW_WITH_WARNING"
	case AdStatusLampColor_ASLCR_RED:
		return "AdStatusLampColor_ASLCR_RED"
	case AdStatusLampColor_ASLCR_EMPTY:
		return "AdStatusLampColor_ASLCR_EMPTY"
	}
	return "<UNSET>"
}

func AdStatusLampColorFromString(s string) (AdStatusLampColor, error) {
	switch s {
	case "AdStatusLampColor_ASLCR_GREEN":
		return AdStatusLampColor_ASLCR_GREEN, nil
	case "AdStatusLampColor_ASLCR_YELLOW":
		return AdStatusLampColor_ASLCR_YELLOW, nil
	case "AdStatusLampColor_ASLCR_YELLOW_WITH_WARNING":
		return AdStatusLampColor_ASLCR_YELLOW_WITH_WARNING, nil
	case "AdStatusLampColor_ASLCR_RED":
		return AdStatusLampColor_ASLCR_RED, nil
	case "AdStatusLampColor_ASLCR_EMPTY":
		return AdStatusLampColor_ASLCR_EMPTY, nil
	}
	return AdStatusLampColor(math.MinInt32 - 1), fmt.Errorf("not a valid AdStatusLampColor string")
}

//广告状态灯代码
type AdStatusLampCode int64

const (
	AdStatusLampCode_ASLC_RUNNING                     AdStatusLampCode = 10
	AdStatusLampCode_ASLC_ADCREATIVE_PENDING_APPROVAL AdStatusLampCode = 20
	AdStatusLampCode_ASLC_ADPLAN_NOT_STARTED          AdStatusLampCode = 21
	AdStatusLampCode_ASLC_BALANCE_DEPLETED            AdStatusLampCode = 30
	AdStatusLampCode_ASLC_ADPLAN_BUDGET_DEPLETED      AdStatusLampCode = 31
	AdStatusLampCode_ASLC_ADPLAN_PAUSED               AdStatusLampCode = 32
	AdStatusLampCode_ASLC_ADSTRATEGY_PAUSED           AdStatusLampCode = 33
	AdStatusLampCode_ASLC_ADCREATIVE_REJECTED         AdStatusLampCode = 34
	AdStatusLampCode_ASLC_ADPLAN_TOTAL_BUDGET_OVER    AdStatusLampCode = 35
	AdStatusLampCode_ASLC_ADPLAN_ENDED                AdStatusLampCode = 40
	AdStatusLampCode_ASLC_ADCREATIVE_PAUSED           AdStatusLampCode = 41
	AdStatusLampCode_ASLC_ADSTRATEGY_BUDGET_DEPLETED  AdStatusLampCode = 42
	AdStatusLampCode_ASLC_ADPLAN_SYS_PAUSED           AdStatusLampCode = 43
	AdStatusLampCode_ASLC_EMPTY                       AdStatusLampCode = 100
)

func (p AdStatusLampCode) String() string {
	switch p {
	case AdStatusLampCode_ASLC_RUNNING:
		return "AdStatusLampCode_ASLC_RUNNING"
	case AdStatusLampCode_ASLC_ADCREATIVE_PENDING_APPROVAL:
		return "AdStatusLampCode_ASLC_ADCREATIVE_PENDING_APPROVAL"
	case AdStatusLampCode_ASLC_ADPLAN_NOT_STARTED:
		return "AdStatusLampCode_ASLC_ADPLAN_NOT_STARTED"
	case AdStatusLampCode_ASLC_BALANCE_DEPLETED:
		return "AdStatusLampCode_ASLC_BALANCE_DEPLETED"
	case AdStatusLampCode_ASLC_ADPLAN_BUDGET_DEPLETED:
		return "AdStatusLampCode_ASLC_ADPLAN_BUDGET_DEPLETED"
	case AdStatusLampCode_ASLC_ADPLAN_PAUSED:
		return "AdStatusLampCode_ASLC_ADPLAN_PAUSED"
	case AdStatusLampCode_ASLC_ADSTRATEGY_PAUSED:
		return "AdStatusLampCode_ASLC_ADSTRATEGY_PAUSED"
	case AdStatusLampCode_ASLC_ADCREATIVE_REJECTED:
		return "AdStatusLampCode_ASLC_ADCREATIVE_REJECTED"
	case AdStatusLampCode_ASLC_ADPLAN_TOTAL_BUDGET_OVER:
		return "AdStatusLampCode_ASLC_ADPLAN_TOTAL_BUDGET_OVER"
	case AdStatusLampCode_ASLC_ADPLAN_ENDED:
		return "AdStatusLampCode_ASLC_ADPLAN_ENDED"
	case AdStatusLampCode_ASLC_ADCREATIVE_PAUSED:
		return "AdStatusLampCode_ASLC_ADCREATIVE_PAUSED"
	case AdStatusLampCode_ASLC_ADSTRATEGY_BUDGET_DEPLETED:
		return "AdStatusLampCode_ASLC_ADSTRATEGY_BUDGET_DEPLETED"
	case AdStatusLampCode_ASLC_ADPLAN_SYS_PAUSED:
		return "AdStatusLampCode_ASLC_ADPLAN_SYS_PAUSED"
	case AdStatusLampCode_ASLC_EMPTY:
		return "AdStatusLampCode_ASLC_EMPTY"
	}
	return "<UNSET>"
}

func AdStatusLampCodeFromString(s string) (AdStatusLampCode, error) {
	switch s {
	case "AdStatusLampCode_ASLC_RUNNING":
		return AdStatusLampCode_ASLC_RUNNING, nil
	case "AdStatusLampCode_ASLC_ADCREATIVE_PENDING_APPROVAL":
		return AdStatusLampCode_ASLC_ADCREATIVE_PENDING_APPROVAL, nil
	case "AdStatusLampCode_ASLC_ADPLAN_NOT_STARTED":
		return AdStatusLampCode_ASLC_ADPLAN_NOT_STARTED, nil
	case "AdStatusLampCode_ASLC_BALANCE_DEPLETED":
		return AdStatusLampCode_ASLC_BALANCE_DEPLETED, nil
	case "AdStatusLampCode_ASLC_ADPLAN_BUDGET_DEPLETED":
		return AdStatusLampCode_ASLC_ADPLAN_BUDGET_DEPLETED, nil
	case "AdStatusLampCode_ASLC_ADPLAN_PAUSED":
		return AdStatusLampCode_ASLC_ADPLAN_PAUSED, nil
	case "AdStatusLampCode_ASLC_ADSTRATEGY_PAUSED":
		return AdStatusLampCode_ASLC_ADSTRATEGY_PAUSED, nil
	case "AdStatusLampCode_ASLC_ADCREATIVE_REJECTED":
		return AdStatusLampCode_ASLC_ADCREATIVE_REJECTED, nil
	case "AdStatusLampCode_ASLC_ADPLAN_TOTAL_BUDGET_OVER":
		return AdStatusLampCode_ASLC_ADPLAN_TOTAL_BUDGET_OVER, nil
	case "AdStatusLampCode_ASLC_ADPLAN_ENDED":
		return AdStatusLampCode_ASLC_ADPLAN_ENDED, nil
	case "AdStatusLampCode_ASLC_ADCREATIVE_PAUSED":
		return AdStatusLampCode_ASLC_ADCREATIVE_PAUSED, nil
	case "AdStatusLampCode_ASLC_ADSTRATEGY_BUDGET_DEPLETED":
		return AdStatusLampCode_ASLC_ADSTRATEGY_BUDGET_DEPLETED, nil
	case "AdStatusLampCode_ASLC_ADPLAN_SYS_PAUSED":
		return AdStatusLampCode_ASLC_ADPLAN_SYS_PAUSED, nil
	case "AdStatusLampCode_ASLC_EMPTY":
		return AdStatusLampCode_ASLC_EMPTY, nil
	}
	return AdStatusLampCode(math.MinInt32 - 1), fmt.Errorf("not a valid AdStatusLampCode string")
}

//日期类型
type DateType int64

const (
	DateType_DT_TODAY    DateType = 0
	DateType_DT_TOMORROW DateType = 1
)

func (p DateType) String() string {
	switch p {
	case DateType_DT_TODAY:
		return "DateType_DT_TODAY"
	case DateType_DT_TOMORROW:
		return "DateType_DT_TOMORROW"
	}
	return "<UNSET>"
}

func DateTypeFromString(s string) (DateType, error) {
	switch s {
	case "DateType_DT_TODAY":
		return DateType_DT_TODAY, nil
	case "DateType_DT_TOMORROW":
		return DateType_DT_TOMORROW, nil
	}
	return DateType(math.MinInt32 - 1), fmt.Errorf("not a valid DateType string")
}

//广告层级标识，目前在扩展信息中使用
type AdHierarchyType int64

const (
	AdHierarchyType_EXTINFO_ID_TYPE_ADPLAN     AdHierarchyType = 1
	AdHierarchyType_EXTINFO_ID_TYPE_ADSTRATEGY AdHierarchyType = 2
	AdHierarchyType_EXTINFO_ID_TYPE_ADCREATIVE AdHierarchyType = 3
	AdHierarchyType_EXTINFO_ID_TYPE_ADUSER     AdHierarchyType = 4
)

func (p AdHierarchyType) String() string {
	switch p {
	case AdHierarchyType_EXTINFO_ID_TYPE_ADPLAN:
		return "AdHierarchyType_EXTINFO_ID_TYPE_ADPLAN"
	case AdHierarchyType_EXTINFO_ID_TYPE_ADSTRATEGY:
		return "AdHierarchyType_EXTINFO_ID_TYPE_ADSTRATEGY"
	case AdHierarchyType_EXTINFO_ID_TYPE_ADCREATIVE:
		return "AdHierarchyType_EXTINFO_ID_TYPE_ADCREATIVE"
	case AdHierarchyType_EXTINFO_ID_TYPE_ADUSER:
		return "AdHierarchyType_EXTINFO_ID_TYPE_ADUSER"
	}
	return "<UNSET>"
}

func AdHierarchyTypeFromString(s string) (AdHierarchyType, error) {
	switch s {
	case "AdHierarchyType_EXTINFO_ID_TYPE_ADPLAN":
		return AdHierarchyType_EXTINFO_ID_TYPE_ADPLAN, nil
	case "AdHierarchyType_EXTINFO_ID_TYPE_ADSTRATEGY":
		return AdHierarchyType_EXTINFO_ID_TYPE_ADSTRATEGY, nil
	case "AdHierarchyType_EXTINFO_ID_TYPE_ADCREATIVE":
		return AdHierarchyType_EXTINFO_ID_TYPE_ADCREATIVE, nil
	case "AdHierarchyType_EXTINFO_ID_TYPE_ADUSER":
		return AdHierarchyType_EXTINFO_ID_TYPE_ADUSER, nil
	}
	return AdHierarchyType(math.MinInt32 - 1), fmt.Errorf("not a valid AdHierarchyType string")
}

//app应用类型,目前用于AdApp的appPlatform
type AdAppPlatform int64

const (
	AdAppPlatform_AP_ANDROID AdAppPlatform = 1
	AdAppPlatform_AP_IOS     AdAppPlatform = 2
	AdAppPlatform_AP_IOS_JB  AdAppPlatform = 3
	AdAppPlatform_AP_OTHER   AdAppPlatform = 9
)

func (p AdAppPlatform) String() string {
	switch p {
	case AdAppPlatform_AP_ANDROID:
		return "AdAppPlatform_AP_ANDROID"
	case AdAppPlatform_AP_IOS:
		return "AdAppPlatform_AP_IOS"
	case AdAppPlatform_AP_IOS_JB:
		return "AdAppPlatform_AP_IOS_JB"
	case AdAppPlatform_AP_OTHER:
		return "AdAppPlatform_AP_OTHER"
	}
	return "<UNSET>"
}

func AdAppPlatformFromString(s string) (AdAppPlatform, error) {
	switch s {
	case "AdAppPlatform_AP_ANDROID":
		return AdAppPlatform_AP_ANDROID, nil
	case "AdAppPlatform_AP_IOS":
		return AdAppPlatform_AP_IOS, nil
	case "AdAppPlatform_AP_IOS_JB":
		return AdAppPlatform_AP_IOS_JB, nil
	case "AdAppPlatform_AP_OTHER":
		return AdAppPlatform_AP_OTHER, nil
	}
	return AdAppPlatform(math.MinInt32 - 1), fmt.Errorf("not a valid AdAppPlatform string")
}

//app推广类型
type AdAppPromotionType int64

const (
	AdAppPromotionType_APT_NORMAL_AD    AdAppPromotionType = 1
	AdAppPromotionType_APT_OFFERWALL_AD AdAppPromotionType = 2
)

func (p AdAppPromotionType) String() string {
	switch p {
	case AdAppPromotionType_APT_NORMAL_AD:
		return "AdAppPromotionType_APT_NORMAL_AD"
	case AdAppPromotionType_APT_OFFERWALL_AD:
		return "AdAppPromotionType_APT_OFFERWALL_AD"
	}
	return "<UNSET>"
}

func AdAppPromotionTypeFromString(s string) (AdAppPromotionType, error) {
	switch s {
	case "AdAppPromotionType_APT_NORMAL_AD":
		return AdAppPromotionType_APT_NORMAL_AD, nil
	case "AdAppPromotionType_APT_OFFERWALL_AD":
		return AdAppPromotionType_APT_OFFERWALL_AD, nil
	}
	return AdAppPromotionType(math.MinInt32 - 1), fmt.Errorf("not a valid AdAppPromotionType string")
}

type UidInt common.UidInt

type AdPlanIdInt common.IdInt

type AdStrategyIdInt common.IdInt

type AdCreativeIdInt common.IdInt

type CurrencyAmount common.Amount

type ImgIdInt common.IdInt

type IdInt common.IdInt

type TimeInt common.TimeInt

type AdQueryResult *common.QueryResult

type AdQueryInt common.QueryInt

type RequestHeader *common.RequestHeader

type GenderCode common.GenderCode

type RegionCode common.RegionCode

type AgeCode common.AgeCode

type CarrierCode common.CarrierCode

type DeviceCode common.DeviceCode

type OSCode common.OSCode

type AccessTypeCode common.AccessTypeCode

type CostType common.CostType

type BudgetType common.BudgetType

type AdCategory common.AdCategory

type AdActionType common.AdActionType

type AdStrategyType common.AdStrategyType

type AdCreativeType common.AdCreativeType

type AdCreativeIconType common.AdCreativeIconType

type TemplateSizeCode common.TemplateSizeCode

type TemplateSizeCodeInt common.TemplateSizeCodeInt

type HtmlTemplateCodeInt int32

type AdRenderType common.AdRenderType

type ImageType common.ImageType

type SDKUrlOpenType common.SDKUrlOpenType

type LandingDirection common.LandingDirection

type AdPlacementType common.AdPlacementType

type RateInt int32

type RegionCityInt int32

type ResIdInt int32

type DemoTagIdInt tag_types.DemoTagIdInt

type DemoTagging *tag_types.DemoTagging

type MediaTagIdInt tag_types.MediaTagIdInt

type DeviceGroupIdInt common.DeviceGroupIdInt

type WaterMarkPosition common.WaterMarkPosition

type CloseButtonPosition common.CloseButtonPosition

type CapabilityIdInt int32

type PositionIdInt int32

type IdTypeInt int32

type ResourceGroup *common.ResourceGroup

type FreqInfo *common.FreqInfo

type AdResourceGroupId common.IdInt

type AdResourceId common.IdInt

type AdAppIdInt common.IdInt

type AdAppSize int32

type AdCreativeTemplateType common.AdCreativeTemplateType

type AdCreativeTemplateSpecIdInt int32

type AdUser struct {
	Uid             UidInt            `thrift:"uid,1" json:"uid"`
	Banned          bool              `thrift:"banned,2" json:"banned"`
	NoBalance       bool              `thrift:"noBalance,3" json:"noBalance"`
	AdUserWaterMark AdWaterMarkStatus `thrift:"adUserWaterMark,4" json:"adUserWaterMark"`
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	// unused field # 10
	// unused field # 11
	// unused field # 12
	// unused field # 13
	// unused field # 14
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	// unused field # 19
	// unused field # 20
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	ExtInfo map[string]string `thrift:"extInfo,30" json:"extInfo"`
}

func NewAdUser() *AdUser {
	return &AdUser{
		AdUserWaterMark: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdUser) IsSetAdUserWaterMark() bool {
	return int64(p.AdUserWaterMark) != math.MinInt32-1
}

func (p *AdUser) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.MAP {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdUser) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdUser) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Banned = v
	}
	return nil
}

func (p *AdUser) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.NoBalance = v
	}
	return nil
}

func (p *AdUser) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.AdUserWaterMark = AdWaterMarkStatus(v)
	}
	return nil
}

func (p *AdUser) readField30(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key0 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key0 = v
		}
		var _val1 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val1 = v
		}
		p.ExtInfo[_key0] = _val1
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdUser) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdUser"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdUser) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdUser) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("banned", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:banned: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Banned)); err != nil {
		return fmt.Errorf("%T.banned (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:banned: %s", p, err)
	}
	return err
}

func (p *AdUser) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("noBalance", thrift.BOOL, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:noBalance: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.NoBalance)); err != nil {
		return fmt.Errorf("%T.noBalance (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:noBalance: %s", p, err)
	}
	return err
}

func (p *AdUser) writeField4(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdUserWaterMark() {
		if err := oprot.WriteFieldBegin("adUserWaterMark", thrift.I32, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:adUserWaterMark: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdUserWaterMark)); err != nil {
			return fmt.Errorf("%T.adUserWaterMark (4) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:adUserWaterMark: %s", p, err)
		}
	}
	return err
}

func (p *AdUser) writeField30(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdUser) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdUser(%+v)", *p)
}

type AdBudgetItem struct {
	Uid          UidInt         `thrift:"uid,1" json:"uid"`
	Pid          AdPlanIdInt    `thrift:"pid,2" json:"pid"`
	DateStart    TimeInt        `thrift:"dateStart,3" json:"dateStart"`
	DateStartStr string         `thrift:"dateStartStr,4" json:"dateStartStr"`
	Budget       CurrencyAmount `thrift:"budget,5" json:"budget"`
	// unused field # 6
	CreateTime TimeInt `thrift:"createTime,7" json:"createTime"`
	LastUpdate TimeInt `thrift:"lastUpdate,8" json:"lastUpdate"`
}

func NewAdBudgetItem() *AdBudgetItem {
	return &AdBudgetItem{}
}

func (p *AdBudgetItem) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I64 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdBudgetItem) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdBudgetItem) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdBudgetItem) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.DateStart = TimeInt(v)
	}
	return nil
}

func (p *AdBudgetItem) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.DateStartStr = v
	}
	return nil
}

func (p *AdBudgetItem) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Budget = CurrencyAmount(v)
	}
	return nil
}

func (p *AdBudgetItem) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdBudgetItem) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdBudgetItem) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdBudgetItem"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdBudgetItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dateStart", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:dateStart: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DateStart)); err != nil {
		return fmt.Errorf("%T.dateStart (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:dateStart: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dateStartStr", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:dateStartStr: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DateStartStr)); err != nil {
		return fmt.Errorf("%T.dateStartStr (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:dateStartStr: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:budget: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:createTime: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdBudgetItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdBudgetItem(%+v)", *p)
}

type AdApp struct {
	Id            AdAppIdInt         `thrift:"id,1" json:"id"`
	Uid           UidInt             `thrift:"uid,2" json:"uid"`
	AppPlatform   AdAppPlatform      `thrift:"appPlatform,3" json:"appPlatform"`
	Name          string             `thrift:"name,4" json:"name"`
	Remark        string             `thrift:"remark,5" json:"remark"`
	Category      AdCategory         `thrift:"category,6" json:"category"`
	PackageName   string             `thrift:"packageName,7" json:"packageName"`
	Size          AdAppSize          `thrift:"size,8" json:"size"`
	Version       string             `thrift:"version,9" json:"version"`
	VersionCode   IdInt              `thrift:"versionCode,10" json:"versionCode"`
	Icon          ImgIdInt           `thrift:"icon,11" json:"icon"`
	AppUrl        string             `thrift:"appUrl,12" json:"appUrl"`
	PromotionType AdAppPromotionType `thrift:"promotionType,13" json:"promotionType"`
	CreateTime    TimeInt            `thrift:"createTime,14" json:"createTime"`
	LastUpdate    TimeInt            `thrift:"lastUpdate,15" json:"lastUpdate"`
}

func NewAdApp() *AdApp {
	return &AdApp{
		AppPlatform: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		PromotionType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdApp) IsSetAppPlatform() bool {
	return int64(p.AppPlatform) != math.MinInt32-1
}

func (p *AdApp) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *AdApp) IsSetPromotionType() bool {
	return int64(p.PromotionType) != math.MinInt32-1
}

func (p *AdApp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I64 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I64 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdApp) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = AdAppIdInt(v)
	}
	return nil
}

func (p *AdApp) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdApp) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.AppPlatform = AdAppPlatform(v)
	}
	return nil
}

func (p *AdApp) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdApp) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Remark = v
	}
	return nil
}

func (p *AdApp) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Category = AdCategory(v)
	}
	return nil
}

func (p *AdApp) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AdApp) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Size = AdAppSize(v)
	}
	return nil
}

func (p *AdApp) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *AdApp) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.VersionCode = IdInt(v)
	}
	return nil
}

func (p *AdApp) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Icon = ImgIdInt(v)
	}
	return nil
}

func (p *AdApp) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AppUrl = v
	}
	return nil
}

func (p *AdApp) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.PromotionType = AdAppPromotionType(v)
	}
	return nil
}

func (p *AdApp) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdApp) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdApp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdApp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdApp) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:uid: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetAppPlatform() {
		if err := oprot.WriteFieldBegin("appPlatform", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:appPlatform: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AppPlatform)); err != nil {
			return fmt.Errorf("%T.appPlatform (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:appPlatform: %s", p, err)
		}
	}
	return err
}

func (p *AdApp) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("remark", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:remark: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Remark)); err != nil {
		return fmt.Errorf("%T.remark (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:remark: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:category: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:packageName: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:size: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:version: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:versionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:versionCode: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:icon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Icon)); err != nil {
		return fmt.Errorf("%T.icon (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:icon: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appUrl", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:appUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppUrl)); err != nil {
		return fmt.Errorf("%T.appUrl (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:appUrl: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetPromotionType() {
		if err := oprot.WriteFieldBegin("promotionType", thrift.I32, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:promotionType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.PromotionType)); err != nil {
			return fmt.Errorf("%T.promotionType (13) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:promotionType: %s", p, err)
		}
	}
	return err
}

func (p *AdApp) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:createTime: %s", p, err)
	}
	return err
}

func (p *AdApp) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdApp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdApp(%+v)", *p)
}

type AdPlan struct {
	Uid         UidInt         `thrift:"uid,1" json:"uid"`
	Id          AdPlanIdInt    `thrift:"id,2" json:"id"`
	Name        string         `thrift:"name,3" json:"name"`
	CostType    CostType       `thrift:"costType,4" json:"costType"`
	StartTime   TimeInt        `thrift:"startTime,5" json:"startTime"`
	EndTime     TimeInt        `thrift:"endTime,6" json:"endTime"`
	Budget      CurrencyAmount `thrift:"budget,7" json:"budget"`
	Category    AdCategory     `thrift:"category,8" json:"category"`
	Note        string         `thrift:"note,9" json:"note"`
	CreateTime  TimeInt        `thrift:"createTime,10" json:"createTime"`
	LastUpdate  TimeInt        `thrift:"lastUpdate,11" json:"lastUpdate"`
	Paused      AdPauseStatus  `thrift:"paused,12" json:"paused"`
	Status      AdPlanStatus   `thrift:"status,13" json:"status"`
	CtaRate     RateInt        `thrift:"ctaRate,14" json:"ctaRate"`
	CpaCostType CostType       `thrift:"cpaCostType,15" json:"cpaCostType"`
	CptCostType CostType       `thrift:"cptCostType,16" json:"cptCostType"`
	// unused field # 17
	// unused field # 18
	// unused field # 19
	BudgetOver bool `thrift:"budgetOver,20" json:"budgetOver"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	NewBudgetEffectiveTime TimeInt        `thrift:"newBudgetEffectiveTime,25" json:"newBudgetEffectiveTime"`
	NewBudget              CurrencyAmount `thrift:"newBudget,26" json:"newBudget"`
	// unused field # 27
	// unused field # 28
	// unused field # 29
	TimeSlot        []int32            `thrift:"timeSlot,30" json:"timeSlot"`
	AdPlanWaterMark AdWaterMarkStatus  `thrift:"adPlanWaterMark,31" json:"adPlanWaterMark"`
	TotalBudget     CurrencyAmount     `thrift:"totalBudget,32" json:"totalBudget"`
	TotalBudgetOver bool               `thrift:"totalBudgetOver,33" json:"totalBudgetOver"`
	BudgetType      BudgetType         `thrift:"budgetType,34" json:"budgetType"`
	BusinessType    BusinessType       `thrift:"businessType,35" json:"businessType"`
	Bid             CurrencyAmount     `thrift:"bid,36" json:"bid"`
	Budgets         []*AdBudgetItem    `thrift:"budgets,37" json:"budgets"`
	ExtInfo         map[string]string  `thrift:"extInfo,38" json:"extInfo"`
	ImpFreqSet      []*common.FreqInfo `thrift:"impFreqSet,39" json:"impFreqSet"`
	ClkFreqSet      []*common.FreqInfo `thrift:"clkFreqSet,40" json:"clkFreqSet"`
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	AppId          int32         `thrift:"appId,50" json:"appId"`
	ChnId          int32         `thrift:"chnId,51" json:"chnId"`
	DspCampaignId  int32         `thrift:"dspCampaignId,52" json:"dspCampaignId"`
	SysPaused      AdPauseStatus `thrift:"sysPaused,53" json:"sysPaused"`
	TaskFlag       int32         `thrift:"taskFlag,54" json:"taskFlag"`
	SupplementFlag int32         `thrift:"supplementFlag,55" json:"supplementFlag"`
}

func NewAdPlan() *AdPlan {
	return &AdPlan{
		CostType: math.MinInt32 - 1, // unset sentinal value

		Category: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		CpaCostType: math.MinInt32 - 1, // unset sentinal value

		CptCostType: math.MinInt32 - 1, // unset sentinal value

		AdPlanWaterMark: math.MinInt32 - 1, // unset sentinal value

		BudgetType: math.MinInt32 - 1, // unset sentinal value

		BusinessType: math.MinInt32 - 1, // unset sentinal value

		SysPaused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdPlan) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *AdPlan) IsSetCategory() bool {
	return int64(p.Category) != math.MinInt32-1
}

func (p *AdPlan) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdPlan) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdPlan) IsSetCpaCostType() bool {
	return int64(p.CpaCostType) != math.MinInt32-1
}

func (p *AdPlan) IsSetCptCostType() bool {
	return int64(p.CptCostType) != math.MinInt32-1
}

func (p *AdPlan) IsSetAdPlanWaterMark() bool {
	return int64(p.AdPlanWaterMark) != math.MinInt32-1
}

func (p *AdPlan) IsSetBudgetType() bool {
	return int64(p.BudgetType) != math.MinInt32-1
}

func (p *AdPlan) IsSetBusinessType() bool {
	return int64(p.BusinessType) != math.MinInt32-1
}

func (p *AdPlan) IsSetSysPaused() bool {
	return int64(p.SysPaused) != math.MinInt32-1
}

func (p *AdPlan) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I64 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.STRING {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I64 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I64 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.LIST {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 32:
			if fieldTypeId == thrift.I64 {
				if err := p.readField32(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 33:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField33(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 34:
			if fieldTypeId == thrift.I32 {
				if err := p.readField34(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 35:
			if fieldTypeId == thrift.I32 {
				if err := p.readField35(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 36:
			if fieldTypeId == thrift.I64 {
				if err := p.readField36(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 37:
			if fieldTypeId == thrift.LIST {
				if err := p.readField37(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 38:
			if fieldTypeId == thrift.MAP {
				if err := p.readField38(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 39:
			if fieldTypeId == thrift.LIST {
				if err := p.readField39(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.LIST {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.I32 {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I32 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 53:
			if fieldTypeId == thrift.I32 {
				if err := p.readField53(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I32 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.I32 {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdPlan) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdPlan) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Id = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdPlan) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdPlan) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *AdPlan) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.StartTime = TimeInt(v)
	}
	return nil
}

func (p *AdPlan) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.EndTime = TimeInt(v)
	}
	return nil
}

func (p *AdPlan) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.Budget = CurrencyAmount(v)
	}
	return nil
}

func (p *AdPlan) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.Category = AdCategory(v)
	}
	return nil
}

func (p *AdPlan) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Note = v
	}
	return nil
}

func (p *AdPlan) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdPlan) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdPlan) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Paused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdPlan) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.Status = AdPlanStatus(v)
	}
	return nil
}

func (p *AdPlan) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.CtaRate = RateInt(v)
	}
	return nil
}

func (p *AdPlan) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.CpaCostType = CostType(v)
	}
	return nil
}

func (p *AdPlan) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.CptCostType = CostType(v)
	}
	return nil
}

func (p *AdPlan) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.BudgetOver = v
	}
	return nil
}

func (p *AdPlan) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.NewBudgetEffectiveTime = TimeInt(v)
	}
	return nil
}

func (p *AdPlan) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.NewBudget = CurrencyAmount(v)
	}
	return nil
}

func (p *AdPlan) readField30(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TimeSlot = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem2 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem2 = v
		}
		p.TimeSlot = append(p.TimeSlot, _elem2)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdPlan) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.AdPlanWaterMark = AdWaterMarkStatus(v)
	}
	return nil
}

func (p *AdPlan) readField32(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 32: %s", err)
	} else {
		p.TotalBudget = CurrencyAmount(v)
	}
	return nil
}

func (p *AdPlan) readField33(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 33: %s", err)
	} else {
		p.TotalBudgetOver = v
	}
	return nil
}

func (p *AdPlan) readField34(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 34: %s", err)
	} else {
		p.BudgetType = BudgetType(v)
	}
	return nil
}

func (p *AdPlan) readField35(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 35: %s", err)
	} else {
		p.BusinessType = BusinessType(v)
	}
	return nil
}

func (p *AdPlan) readField36(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 36: %s", err)
	} else {
		p.Bid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdPlan) readField37(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Budgets = make([]*AdBudgetItem, 0, size)
	for i := 0; i < size; i++ {
		_elem3 := NewAdBudgetItem()
		if err := _elem3.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem3)
		}
		p.Budgets = append(p.Budgets, _elem3)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdPlan) readField38(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key4 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key4 = v
		}
		var _val5 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val5 = v
		}
		p.ExtInfo[_key4] = _val5
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdPlan) readField39(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ImpFreqSet = make([]*common.FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem6 := common.NewFreqInfo()
		if err := _elem6.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem6)
		}
		p.ImpFreqSet = append(p.ImpFreqSet, _elem6)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdPlan) readField40(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ClkFreqSet = make([]*common.FreqInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem7 := common.NewFreqInfo()
		if err := _elem7.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem7)
		}
		p.ClkFreqSet = append(p.ClkFreqSet, _elem7)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdPlan) readField50(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 50: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AdPlan) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdPlan) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.DspCampaignId = v
	}
	return nil
}

func (p *AdPlan) readField53(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 53: %s", err)
	} else {
		p.SysPaused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdPlan) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.TaskFlag = v
	}
	return nil
}

func (p *AdPlan) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.SupplementFlag = v
	}
	return nil
}

func (p *AdPlan) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdPlan"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField32(oprot); err != nil {
		return err
	}
	if err := p.writeField33(oprot); err != nil {
		return err
	}
	if err := p.writeField34(oprot); err != nil {
		return err
	}
	if err := p.writeField35(oprot); err != nil {
		return err
	}
	if err := p.writeField36(oprot); err != nil {
		return err
	}
	if err := p.writeField37(oprot); err != nil {
		return err
	}
	if err := p.writeField38(oprot); err != nil {
		return err
	}
	if err := p.writeField39(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField53(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdPlan) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:id: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:costType: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("startTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:startTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.StartTime)); err != nil {
		return fmt.Errorf("%T.startTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:startTime: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("endTime", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:endTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EndTime)); err != nil {
		return fmt.Errorf("%T.endTime (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:endTime: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:budget: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("category", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:category: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Category)); err != nil {
		return fmt.Errorf("%T.category (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:category: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("note", thrift.STRING, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:note: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Note)); err != nil {
		return fmt.Errorf("%T.note (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:note: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:createTime: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField12(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 12); err != nil {
			return fmt.Errorf("%T write field begin error 12:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (12) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 12:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField13(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (13) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:status: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctaRate", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:ctaRate: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CtaRate)); err != nil {
		return fmt.Errorf("%T.ctaRate (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:ctaRate: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaCostType", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:cpaCostType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CpaCostType)); err != nil {
		return fmt.Errorf("%T.cpaCostType (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:cpaCostType: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cptCostType", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:cptCostType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CptCostType)); err != nil {
		return fmt.Errorf("%T.cptCostType (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:cptCostType: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budgetOver", thrift.BOOL, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:budgetOver: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.BudgetOver)); err != nil {
		return fmt.Errorf("%T.budgetOver (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:budgetOver: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newBudgetEffectiveTime", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:newBudgetEffectiveTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.NewBudgetEffectiveTime)); err != nil {
		return fmt.Errorf("%T.newBudgetEffectiveTime (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:newBudgetEffectiveTime: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newBudget", thrift.I64, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:newBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.NewBudget)); err != nil {
		return fmt.Errorf("%T.newBudget (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:newBudget: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField30(oprot thrift.TProtocol) (err error) {
	if p.TimeSlot != nil {
		if err := oprot.WriteFieldBegin("timeSlot", thrift.LIST, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:timeSlot: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.TimeSlot)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TimeSlot {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:timeSlot: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField31(oprot thrift.TProtocol) (err error) {
	if p.IsSetAdPlanWaterMark() {
		if err := oprot.WriteFieldBegin("adPlanWaterMark", thrift.I32, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:adPlanWaterMark: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.AdPlanWaterMark)); err != nil {
			return fmt.Errorf("%T.adPlanWaterMark (31) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:adPlanWaterMark: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField32(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudget", thrift.I64, 32); err != nil {
		return fmt.Errorf("%T write field begin error 32:totalBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.TotalBudget)); err != nil {
		return fmt.Errorf("%T.totalBudget (32) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 32:totalBudget: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField33(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("totalBudgetOver", thrift.BOOL, 33); err != nil {
		return fmt.Errorf("%T write field begin error 33:totalBudgetOver: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.TotalBudgetOver)); err != nil {
		return fmt.Errorf("%T.totalBudgetOver (33) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 33:totalBudgetOver: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField34(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budgetType", thrift.I32, 34); err != nil {
		return fmt.Errorf("%T write field begin error 34:budgetType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BudgetType)); err != nil {
		return fmt.Errorf("%T.budgetType (34) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 34:budgetType: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField35(oprot thrift.TProtocol) (err error) {
	if p.IsSetBusinessType() {
		if err := oprot.WriteFieldBegin("businessType", thrift.I32, 35); err != nil {
			return fmt.Errorf("%T write field begin error 35:businessType: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.BusinessType)); err != nil {
			return fmt.Errorf("%T.businessType (35) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 35:businessType: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField36(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 36); err != nil {
		return fmt.Errorf("%T write field begin error 36:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (36) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 36:bid: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField37(oprot thrift.TProtocol) (err error) {
	if p.Budgets != nil {
		if err := oprot.WriteFieldBegin("budgets", thrift.LIST, 37); err != nil {
			return fmt.Errorf("%T write field begin error 37:budgets: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Budgets)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Budgets {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 37:budgets: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField38(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 38); err != nil {
			return fmt.Errorf("%T write field begin error 38:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 38:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField39(oprot thrift.TProtocol) (err error) {
	if p.ImpFreqSet != nil {
		if err := oprot.WriteFieldBegin("impFreqSet", thrift.LIST, 39); err != nil {
			return fmt.Errorf("%T write field begin error 39:impFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ImpFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ImpFreqSet {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 39:impFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField40(oprot thrift.TProtocol) (err error) {
	if p.ClkFreqSet != nil {
		if err := oprot.WriteFieldBegin("clkFreqSet", thrift.LIST, 40); err != nil {
			return fmt.Errorf("%T write field begin error 40:clkFreqSet: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ClkFreqSet)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ClkFreqSet {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 40:clkFreqSet: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField50(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 50); err != nil {
		return fmt.Errorf("%T write field begin error 50:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (50) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 50:appId: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:chnId: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dspCampaignId", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:dspCampaignId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DspCampaignId)); err != nil {
		return fmt.Errorf("%T.dspCampaignId (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:dspCampaignId: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField53(oprot thrift.TProtocol) (err error) {
	if p.IsSetSysPaused() {
		if err := oprot.WriteFieldBegin("sysPaused", thrift.I32, 53); err != nil {
			return fmt.Errorf("%T write field begin error 53:sysPaused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SysPaused)); err != nil {
			return fmt.Errorf("%T.sysPaused (53) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 53:sysPaused: %s", p, err)
		}
	}
	return err
}

func (p *AdPlan) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("taskFlag", thrift.I32, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:taskFlag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TaskFlag)); err != nil {
		return fmt.Errorf("%T.taskFlag (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:taskFlag: %s", p, err)
	}
	return err
}

func (p *AdPlan) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("supplementFlag", thrift.I32, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:supplementFlag: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SupplementFlag)); err != nil {
		return fmt.Errorf("%T.supplementFlag (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:supplementFlag: %s", p, err)
	}
	return err
}

func (p *AdPlan) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdPlan(%+v)", *p)
}

type AdStrategy struct {
	Uid                  UidInt           `thrift:"uid,1" json:"uid"`
	Pid                  AdPlanIdInt      `thrift:"pid,2" json:"pid"`
	Id                   AdStrategyIdInt  `thrift:"id,3" json:"id"`
	Name                 string           `thrift:"name,4" json:"name"`
	TypeA1               AdStrategyType   `thrift:"type,5" json:"type"`
	OsTarget             []OSCode         `thrift:"osTarget,6" json:"osTarget"`
	DeviceTarget         []DeviceCode     `thrift:"deviceTarget,7" json:"deviceTarget"`
	AccessTarget         []AccessTypeCode `thrift:"accessTarget,8" json:"accessTarget"`
	CarrierTarget        []CarrierCode    `thrift:"carrierTarget,9" json:"carrierTarget"`
	GeoTarget            []RegionCode     `thrift:"geoTarget,10" json:"geoTarget"`
	AgeTarget            []AgeCode        `thrift:"ageTarget,11" json:"ageTarget"`
	GenderTarget         GenderCode       `thrift:"genderTarget,12" json:"genderTarget"`
	GeoCityTarget        []RegionCityInt  `thrift:"geoCityTarget,13" json:"geoCityTarget"`
	DemoTagTarget        []IdInt          `thrift:"demoTagTarget,14" json:"demoTagTarget"`
	BlackDemoTagTarget   []DemoTagIdInt   `thrift:"blackDemoTagTarget,15" json:"blackDemoTagTarget"`
	ExtendDemoTagTarget  bool             `thrift:"extendDemoTagTarget,16" json:"extendDemoTagTarget"`
	MediaTagTarget       []MediaTagIdInt  `thrift:"mediaTagTarget,17" json:"mediaTagTarget"`
	BlackMediaTagTarget  []MediaTagIdInt  `thrift:"blackMediaTagTarget,18" json:"blackMediaTagTarget"`
	ExtendMediaTagTarget bool             `thrift:"extendMediaTagTarget,19" json:"extendMediaTagTarget"`
	DefaultBid           CurrencyAmount   `thrift:"defaultBid,20" json:"defaultBid"`
	CreateTime           TimeInt          `thrift:"createTime,21" json:"createTime"`
	LastUpdate           TimeInt          `thrift:"lastUpdate,22" json:"lastUpdate"`
	Paused               AdPauseStatus    `thrift:"paused,23" json:"paused"`
	Status               AdStrategyStatus `thrift:"status,24" json:"status"`
	CpaBid               CurrencyAmount   `thrift:"cpaBid,25" json:"cpaBid"`
	AdLevel              int32            `thrift:"adLevel,26" json:"adLevel"`
	TargetRatio          int32            `thrift:"targetRatio,27" json:"targetRatio"`
	CpaCostLimit         CurrencyAmount   `thrift:"cpaCostLimit,28" json:"cpaCostLimit"`
	// unused field # 29
	ExtInfo map[string]string `thrift:"extInfo,30" json:"extInfo"`
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	AdDemoTaggings         []*tag_types.DemoTagging `thrift:"adDemoTaggings,50" json:"adDemoTaggings"`
	DeviceGroupTarget      DeviceGroupIdInt         `thrift:"deviceGroupTarget,51" json:"deviceGroupTarget"`
	BlackDeviceGroupTarget DeviceGroupIdInt         `thrift:"blackDeviceGroupTarget,52" json:"blackDeviceGroupTarget"`
	// unused field # 53
	Budget                 CurrencyAmount  `thrift:"budget,54" json:"budget"`
	BudgetOver             bool            `thrift:"budgetOver,55" json:"budgetOver"`
	NewBudgetEffectiveTime TimeInt         `thrift:"newBudgetEffectiveTime,56" json:"newBudgetEffectiveTime"`
	NewBudget              CurrencyAmount  `thrift:"newBudget,57" json:"newBudget"`
	TimeSlots              map[int32]int32 `thrift:"timeSlots,58" json:"timeSlots"`
	// unused field # 59
	PlacementType    AdPlacementType `thrift:"placementType,60" json:"placementType"`
	PositionTarget   []PositionIdInt `thrift:"positionTarget,61" json:"positionTarget"`
	AdVisibleSetting int32           `thrift:"adVisibleSetting,62" json:"adVisibleSetting"`
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	Bid                 CurrencyAmount `thrift:"bid,70" json:"bid"`
	ContainerFlagTarget []int32        `thrift:"containerFlagTarget,71" json:"containerFlagTarget"`
	ChannelTarget       []int32        `thrift:"channelTarget,72" json:"channelTarget"`
	SubChannelTarget    []int32        `thrift:"subChannelTarget,73" json:"subChannelTarget"`
	TitleTarget         []int32        `thrift:"titleTarget,74" json:"titleTarget"`
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	MinOsVersionTarget map[int32]int32 `thrift:"minOsVersionTarget,80" json:"minOsVersionTarget"`
	MaxOsVersionTarget map[int32]int32 `thrift:"maxOsVersionTarget,81" json:"maxOsVersionTarget"`
	ExtendType         int32           `thrift:"extendType,82" json:"extendType"`
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	AppId int32 `thrift:"appId,90" json:"appId"`
	ChnId int32 `thrift:"chnId,91" json:"chnId"`
}

func NewAdStrategy() *AdStrategy {
	return &AdStrategy{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		GenderTarget: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdStrategy) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AdStrategy) IsSetGenderTarget() bool {
	return int64(p.GenderTarget) != math.MinInt32-1
}

func (p *AdStrategy) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdStrategy) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdStrategy) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *AdStrategy) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.LIST {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.LIST {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.LIST {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.LIST {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.LIST {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.LIST {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.LIST {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.LIST {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.LIST {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.LIST {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.LIST {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.I64 {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.I32 {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 25:
			if fieldTypeId == thrift.I64 {
				if err := p.readField25(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 26:
			if fieldTypeId == thrift.I32 {
				if err := p.readField26(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 27:
			if fieldTypeId == thrift.I32 {
				if err := p.readField27(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 28:
			if fieldTypeId == thrift.I64 {
				if err := p.readField28(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.MAP {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 50:
			if fieldTypeId == thrift.LIST {
				if err := p.readField50(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 51:
			if fieldTypeId == thrift.I32 {
				if err := p.readField51(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 52:
			if fieldTypeId == thrift.I32 {
				if err := p.readField52(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 54:
			if fieldTypeId == thrift.I64 {
				if err := p.readField54(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 55:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField55(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 56:
			if fieldTypeId == thrift.I64 {
				if err := p.readField56(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 57:
			if fieldTypeId == thrift.I64 {
				if err := p.readField57(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 58:
			if fieldTypeId == thrift.MAP {
				if err := p.readField58(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 60:
			if fieldTypeId == thrift.I32 {
				if err := p.readField60(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 61:
			if fieldTypeId == thrift.LIST {
				if err := p.readField61(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 62:
			if fieldTypeId == thrift.I32 {
				if err := p.readField62(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.I64 {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.LIST {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.LIST {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.LIST {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.LIST {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 80:
			if fieldTypeId == thrift.MAP {
				if err := p.readField80(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 81:
			if fieldTypeId == thrift.MAP {
				if err := p.readField81(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 82:
			if fieldTypeId == thrift.I32 {
				if err := p.readField82(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 90:
			if fieldTypeId == thrift.I32 {
				if err := p.readField90(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 91:
			if fieldTypeId == thrift.I32 {
				if err := p.readField91(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStrategy) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdStrategy) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdStrategy) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Id = AdStrategyIdInt(v)
	}
	return nil
}

func (p *AdStrategy) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdStrategy) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = AdStrategyType(v)
	}
	return nil
}

func (p *AdStrategy) readField6(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.OsTarget = make([]OSCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem8 OSCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem8 = OSCode(v)
		}
		p.OsTarget = append(p.OsTarget, _elem8)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField7(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DeviceTarget = make([]DeviceCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem9 DeviceCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem9 = DeviceCode(v)
		}
		p.DeviceTarget = append(p.DeviceTarget, _elem9)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField8(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AccessTarget = make([]AccessTypeCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem10 AccessTypeCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem10 = AccessTypeCode(v)
		}
		p.AccessTarget = append(p.AccessTarget, _elem10)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField9(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CarrierTarget = make([]CarrierCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem11 CarrierCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem11 = CarrierCode(v)
		}
		p.CarrierTarget = append(p.CarrierTarget, _elem11)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField10(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoTarget = make([]RegionCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem12 RegionCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem12 = RegionCode(v)
		}
		p.GeoTarget = append(p.GeoTarget, _elem12)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField11(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AgeTarget = make([]AgeCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem13 AgeCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem13 = AgeCode(v)
		}
		p.AgeTarget = append(p.AgeTarget, _elem13)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.GenderTarget = GenderCode(v)
	}
	return nil
}

func (p *AdStrategy) readField13(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.GeoCityTarget = make([]RegionCityInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem14 RegionCityInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem14 = RegionCityInt(v)
		}
		p.GeoCityTarget = append(p.GeoCityTarget, _elem14)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField14(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.DemoTagTarget = make([]IdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem15 IdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem15 = IdInt(v)
		}
		p.DemoTagTarget = append(p.DemoTagTarget, _elem15)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField15(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BlackDemoTagTarget = make([]DemoTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem16 DemoTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem16 = DemoTagIdInt(v)
		}
		p.BlackDemoTagTarget = append(p.BlackDemoTagTarget, _elem16)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.ExtendDemoTagTarget = v
	}
	return nil
}

func (p *AdStrategy) readField17(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.MediaTagTarget = make([]MediaTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem17 MediaTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem17 = MediaTagIdInt(v)
		}
		p.MediaTagTarget = append(p.MediaTagTarget, _elem17)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField18(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.BlackMediaTagTarget = make([]MediaTagIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem18 MediaTagIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem18 = MediaTagIdInt(v)
		}
		p.BlackMediaTagTarget = append(p.BlackMediaTagTarget, _elem18)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.ExtendMediaTagTarget = v
	}
	return nil
}

func (p *AdStrategy) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.DefaultBid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdStrategy) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdStrategy) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdStrategy) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.Paused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdStrategy) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.Status = AdStrategyStatus(v)
	}
	return nil
}

func (p *AdStrategy) readField25(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 25: %s", err)
	} else {
		p.CpaBid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdStrategy) readField26(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 26: %s", err)
	} else {
		p.AdLevel = v
	}
	return nil
}

func (p *AdStrategy) readField27(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 27: %s", err)
	} else {
		p.TargetRatio = v
	}
	return nil
}

func (p *AdStrategy) readField28(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 28: %s", err)
	} else {
		p.CpaCostLimit = CurrencyAmount(v)
	}
	return nil
}

func (p *AdStrategy) readField30(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key19 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key19 = v
		}
		var _val20 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val20 = v
		}
		p.ExtInfo[_key19] = _val20
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField50(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdDemoTaggings = make([]*tag_types.DemoTagging, 0, size)
	for i := 0; i < size; i++ {
		_elem21 := tag_types.NewDemoTagging()
		if err := _elem21.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem21)
		}
		p.AdDemoTaggings = append(p.AdDemoTaggings, _elem21)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField51(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 51: %s", err)
	} else {
		p.DeviceGroupTarget = DeviceGroupIdInt(v)
	}
	return nil
}

func (p *AdStrategy) readField52(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 52: %s", err)
	} else {
		p.BlackDeviceGroupTarget = DeviceGroupIdInt(v)
	}
	return nil
}

func (p *AdStrategy) readField54(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 54: %s", err)
	} else {
		p.Budget = CurrencyAmount(v)
	}
	return nil
}

func (p *AdStrategy) readField55(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 55: %s", err)
	} else {
		p.BudgetOver = v
	}
	return nil
}

func (p *AdStrategy) readField56(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 56: %s", err)
	} else {
		p.NewBudgetEffectiveTime = TimeInt(v)
	}
	return nil
}

func (p *AdStrategy) readField57(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 57: %s", err)
	} else {
		p.NewBudget = CurrencyAmount(v)
	}
	return nil
}

func (p *AdStrategy) readField58(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.TimeSlots = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key22 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key22 = v
		}
		var _val23 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val23 = v
		}
		p.TimeSlots[_key22] = _val23
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField60(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 60: %s", err)
	} else {
		p.PlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *AdStrategy) readField61(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.PositionTarget = make([]PositionIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem24 PositionIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem24 = PositionIdInt(v)
		}
		p.PositionTarget = append(p.PositionTarget, _elem24)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField62(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 62: %s", err)
	} else {
		p.AdVisibleSetting = v
	}
	return nil
}

func (p *AdStrategy) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.Bid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdStrategy) readField71(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ContainerFlagTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem25 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem25 = v
		}
		p.ContainerFlagTarget = append(p.ContainerFlagTarget, _elem25)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField72(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ChannelTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem26 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem26 = v
		}
		p.ChannelTarget = append(p.ChannelTarget, _elem26)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField73(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SubChannelTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem27 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem27 = v
		}
		p.SubChannelTarget = append(p.SubChannelTarget, _elem27)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField74(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.TitleTarget = make([]int32, 0, size)
	for i := 0; i < size; i++ {
		var _elem28 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem28 = v
		}
		p.TitleTarget = append(p.TitleTarget, _elem28)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField80(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.MinOsVersionTarget = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key29 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key29 = v
		}
		var _val30 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val30 = v
		}
		p.MinOsVersionTarget[_key29] = _val30
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField81(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.MaxOsVersionTarget = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key31 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key31 = v
		}
		var _val32 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val32 = v
		}
		p.MaxOsVersionTarget[_key31] = _val32
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdStrategy) readField82(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 82: %s", err)
	} else {
		p.ExtendType = v
	}
	return nil
}

func (p *AdStrategy) readField90(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 90: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AdStrategy) readField91(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 91: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdStrategy) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStrategy"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField25(oprot); err != nil {
		return err
	}
	if err := p.writeField26(oprot); err != nil {
		return err
	}
	if err := p.writeField27(oprot); err != nil {
		return err
	}
	if err := p.writeField28(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField50(oprot); err != nil {
		return err
	}
	if err := p.writeField51(oprot); err != nil {
		return err
	}
	if err := p.writeField52(oprot); err != nil {
		return err
	}
	if err := p.writeField54(oprot); err != nil {
		return err
	}
	if err := p.writeField55(oprot); err != nil {
		return err
	}
	if err := p.writeField56(oprot); err != nil {
		return err
	}
	if err := p.writeField57(oprot); err != nil {
		return err
	}
	if err := p.writeField58(oprot); err != nil {
		return err
	}
	if err := p.writeField60(oprot); err != nil {
		return err
	}
	if err := p.writeField61(oprot); err != nil {
		return err
	}
	if err := p.writeField62(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField80(oprot); err != nil {
		return err
	}
	if err := p.writeField81(oprot); err != nil {
		return err
	}
	if err := p.writeField82(oprot); err != nil {
		return err
	}
	if err := p.writeField90(oprot); err != nil {
		return err
	}
	if err := p.writeField91(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStrategy) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:id: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:type: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField6(oprot thrift.TProtocol) (err error) {
	if p.OsTarget != nil {
		if err := oprot.WriteFieldBegin("osTarget", thrift.LIST, 6); err != nil {
			return fmt.Errorf("%T write field begin error 6:osTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.OsTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.OsTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 6:osTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField7(oprot thrift.TProtocol) (err error) {
	if p.DeviceTarget != nil {
		if err := oprot.WriteFieldBegin("deviceTarget", thrift.LIST, 7); err != nil {
			return fmt.Errorf("%T write field begin error 7:deviceTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DeviceTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DeviceTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 7:deviceTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField8(oprot thrift.TProtocol) (err error) {
	if p.AccessTarget != nil {
		if err := oprot.WriteFieldBegin("accessTarget", thrift.LIST, 8); err != nil {
			return fmt.Errorf("%T write field begin error 8:accessTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AccessTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AccessTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 8:accessTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField9(oprot thrift.TProtocol) (err error) {
	if p.CarrierTarget != nil {
		if err := oprot.WriteFieldBegin("carrierTarget", thrift.LIST, 9); err != nil {
			return fmt.Errorf("%T write field begin error 9:carrierTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.CarrierTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CarrierTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 9:carrierTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField10(oprot thrift.TProtocol) (err error) {
	if p.GeoTarget != nil {
		if err := oprot.WriteFieldBegin("geoTarget", thrift.LIST, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:geoTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GeoTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:geoTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField11(oprot thrift.TProtocol) (err error) {
	if p.AgeTarget != nil {
		if err := oprot.WriteFieldBegin("ageTarget", thrift.LIST, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:ageTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.AgeTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AgeTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:ageTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("genderTarget", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:genderTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.GenderTarget)); err != nil {
		return fmt.Errorf("%T.genderTarget (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:genderTarget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField13(oprot thrift.TProtocol) (err error) {
	if p.GeoCityTarget != nil {
		if err := oprot.WriteFieldBegin("geoCityTarget", thrift.LIST, 13); err != nil {
			return fmt.Errorf("%T write field begin error 13:geoCityTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.GeoCityTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.GeoCityTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 13:geoCityTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField14(oprot thrift.TProtocol) (err error) {
	if p.DemoTagTarget != nil {
		if err := oprot.WriteFieldBegin("demoTagTarget", thrift.LIST, 14); err != nil {
			return fmt.Errorf("%T write field begin error 14:demoTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.DemoTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.DemoTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 14:demoTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField15(oprot thrift.TProtocol) (err error) {
	if p.BlackDemoTagTarget != nil {
		if err := oprot.WriteFieldBegin("blackDemoTagTarget", thrift.LIST, 15); err != nil {
			return fmt.Errorf("%T write field begin error 15:blackDemoTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.BlackDemoTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BlackDemoTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 15:blackDemoTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendDemoTagTarget", thrift.BOOL, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:extendDemoTagTarget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExtendDemoTagTarget)); err != nil {
		return fmt.Errorf("%T.extendDemoTagTarget (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:extendDemoTagTarget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField17(oprot thrift.TProtocol) (err error) {
	if p.MediaTagTarget != nil {
		if err := oprot.WriteFieldBegin("mediaTagTarget", thrift.LIST, 17); err != nil {
			return fmt.Errorf("%T write field begin error 17:mediaTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.MediaTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.MediaTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 17:mediaTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField18(oprot thrift.TProtocol) (err error) {
	if p.BlackMediaTagTarget != nil {
		if err := oprot.WriteFieldBegin("blackMediaTagTarget", thrift.LIST, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:blackMediaTagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.BlackMediaTagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.BlackMediaTagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:blackMediaTagTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendMediaTagTarget", thrift.BOOL, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:extendMediaTagTarget: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ExtendMediaTagTarget)); err != nil {
		return fmt.Errorf("%T.extendMediaTagTarget (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:extendMediaTagTarget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("defaultBid", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:defaultBid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DefaultBid)); err != nil {
		return fmt.Errorf("%T.defaultBid (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:defaultBid: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:createTime: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField23(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 23); err != nil {
			return fmt.Errorf("%T write field begin error 23:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (23) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 23:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField24(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 24); err != nil {
			return fmt.Errorf("%T write field begin error 24:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (24) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 24:status: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField25(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaBid", thrift.I64, 25); err != nil {
		return fmt.Errorf("%T write field begin error 25:cpaBid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CpaBid)); err != nil {
		return fmt.Errorf("%T.cpaBid (25) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 25:cpaBid: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField26(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adLevel", thrift.I32, 26); err != nil {
		return fmt.Errorf("%T write field begin error 26:adLevel: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdLevel)); err != nil {
		return fmt.Errorf("%T.adLevel (26) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 26:adLevel: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField27(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("targetRatio", thrift.I32, 27); err != nil {
		return fmt.Errorf("%T write field begin error 27:targetRatio: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TargetRatio)); err != nil {
		return fmt.Errorf("%T.targetRatio (27) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 27:targetRatio: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField28(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cpaCostLimit", thrift.I64, 28); err != nil {
		return fmt.Errorf("%T write field begin error 28:cpaCostLimit: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CpaCostLimit)); err != nil {
		return fmt.Errorf("%T.cpaCostLimit (28) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 28:cpaCostLimit: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField30(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField50(oprot thrift.TProtocol) (err error) {
	if p.AdDemoTaggings != nil {
		if err := oprot.WriteFieldBegin("adDemoTaggings", thrift.LIST, 50); err != nil {
			return fmt.Errorf("%T write field begin error 50:adDemoTaggings: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdDemoTaggings)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdDemoTaggings {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 50:adDemoTaggings: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField51(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceGroupTarget", thrift.I32, 51); err != nil {
		return fmt.Errorf("%T write field begin error 51:deviceGroupTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DeviceGroupTarget)); err != nil {
		return fmt.Errorf("%T.deviceGroupTarget (51) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 51:deviceGroupTarget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField52(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("blackDeviceGroupTarget", thrift.I32, 52); err != nil {
		return fmt.Errorf("%T write field begin error 52:blackDeviceGroupTarget: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.BlackDeviceGroupTarget)); err != nil {
		return fmt.Errorf("%T.blackDeviceGroupTarget (52) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 52:blackDeviceGroupTarget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField54(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budget", thrift.I64, 54); err != nil {
		return fmt.Errorf("%T write field begin error 54:budget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Budget)); err != nil {
		return fmt.Errorf("%T.budget (54) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 54:budget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField55(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("budgetOver", thrift.BOOL, 55); err != nil {
		return fmt.Errorf("%T write field begin error 55:budgetOver: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.BudgetOver)); err != nil {
		return fmt.Errorf("%T.budgetOver (55) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 55:budgetOver: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField56(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newBudgetEffectiveTime", thrift.I64, 56); err != nil {
		return fmt.Errorf("%T write field begin error 56:newBudgetEffectiveTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.NewBudgetEffectiveTime)); err != nil {
		return fmt.Errorf("%T.newBudgetEffectiveTime (56) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 56:newBudgetEffectiveTime: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField57(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("newBudget", thrift.I64, 57); err != nil {
		return fmt.Errorf("%T write field begin error 57:newBudget: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.NewBudget)); err != nil {
		return fmt.Errorf("%T.newBudget (57) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 57:newBudget: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField58(oprot thrift.TProtocol) (err error) {
	if p.TimeSlots != nil {
		if err := oprot.WriteFieldBegin("timeSlots", thrift.MAP, 58); err != nil {
			return fmt.Errorf("%T write field begin error 58:timeSlots: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.TimeSlots)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.TimeSlots {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 58:timeSlots: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField60(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 60); err != nil {
		return fmt.Errorf("%T write field begin error 60:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (60) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 60:placementType: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField61(oprot thrift.TProtocol) (err error) {
	if p.PositionTarget != nil {
		if err := oprot.WriteFieldBegin("positionTarget", thrift.LIST, 61); err != nil {
			return fmt.Errorf("%T write field begin error 61:positionTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.PositionTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.PositionTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 61:positionTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField62(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adVisibleSetting", thrift.I32, 62); err != nil {
		return fmt.Errorf("%T write field begin error 62:adVisibleSetting: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdVisibleSetting)); err != nil {
		return fmt.Errorf("%T.adVisibleSetting (62) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 62:adVisibleSetting: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:bid: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField71(oprot thrift.TProtocol) (err error) {
	if p.ContainerFlagTarget != nil {
		if err := oprot.WriteFieldBegin("containerFlagTarget", thrift.LIST, 71); err != nil {
			return fmt.Errorf("%T write field begin error 71:containerFlagTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ContainerFlagTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ContainerFlagTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 71:containerFlagTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField72(oprot thrift.TProtocol) (err error) {
	if p.ChannelTarget != nil {
		if err := oprot.WriteFieldBegin("channelTarget", thrift.LIST, 72); err != nil {
			return fmt.Errorf("%T write field begin error 72:channelTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.ChannelTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ChannelTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 72:channelTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField73(oprot thrift.TProtocol) (err error) {
	if p.SubChannelTarget != nil {
		if err := oprot.WriteFieldBegin("subChannelTarget", thrift.LIST, 73); err != nil {
			return fmt.Errorf("%T write field begin error 73:subChannelTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.SubChannelTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SubChannelTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 73:subChannelTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField74(oprot thrift.TProtocol) (err error) {
	if p.TitleTarget != nil {
		if err := oprot.WriteFieldBegin("titleTarget", thrift.LIST, 74); err != nil {
			return fmt.Errorf("%T write field begin error 74:titleTarget: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.TitleTarget)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.TitleTarget {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 74:titleTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField80(oprot thrift.TProtocol) (err error) {
	if p.MinOsVersionTarget != nil {
		if err := oprot.WriteFieldBegin("minOsVersionTarget", thrift.MAP, 80); err != nil {
			return fmt.Errorf("%T write field begin error 80:minOsVersionTarget: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.MinOsVersionTarget)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.MinOsVersionTarget {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 80:minOsVersionTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField81(oprot thrift.TProtocol) (err error) {
	if p.MaxOsVersionTarget != nil {
		if err := oprot.WriteFieldBegin("maxOsVersionTarget", thrift.MAP, 81); err != nil {
			return fmt.Errorf("%T write field begin error 81:maxOsVersionTarget: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.MaxOsVersionTarget)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.MaxOsVersionTarget {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 81:maxOsVersionTarget: %s", p, err)
		}
	}
	return err
}

func (p *AdStrategy) writeField82(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("extendType", thrift.I32, 82); err != nil {
		return fmt.Errorf("%T write field begin error 82:extendType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExtendType)); err != nil {
		return fmt.Errorf("%T.extendType (82) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 82:extendType: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField90(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 90); err != nil {
		return fmt.Errorf("%T write field begin error 90:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (90) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 90:appId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) writeField91(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 91); err != nil {
		return fmt.Errorf("%T write field begin error 91:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (91) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 91:chnId: %s", p, err)
	}
	return err
}

func (p *AdStrategy) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStrategy(%+v)", *p)
}

type AdCreativeTemplate struct {
	Name          string                      `thrift:"name,1" json:"name"`
	TypeA1        AdCreativeTemplateType      `thrift:"type,2" json:"type"`
	Specification AdCreativeTemplateSpecIdInt `thrift:"specification,3" json:"specification"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Size      TemplateSizeCode `thrift:"size,10" json:"size"`
	Image     ImgIdInt         `thrift:"image,11" json:"image"`
	Icon      ImgIdInt         `thrift:"icon,12" json:"icon"`
	ImageType ImageType        `thrift:"imageType,13" json:"imageType"`
	// unused field # 14
	Isize  TemplateSizeCodeInt `thrift:"isize,15" json:"isize"`
	Html   ResIdInt            `thrift:"html,16" json:"html"`
	Url    string              `thrift:"url,17" json:"url"`
	HDicon ImgIdInt            `thrift:"HDicon,18" json:"HDicon"`
	Title  string              `thrift:"title,19" json:"title"`
	Brief  string              `thrift:"brief,20" json:"brief"`
	Detail string              `thrift:"detail,21" json:"detail"`
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	// unused field # 30
	// unused field # 31
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	// unused field # 40
	// unused field # 41
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	IsOriginal            bool              `thrift:"isOriginal,100" json:"isOriginal"`
	ShouldIgnoreWaterMark bool              `thrift:"shouldIgnoreWaterMark,101" json:"shouldIgnoreWaterMark"`
	WaterMarkPosition     WaterMarkPosition `thrift:"waterMarkPosition,102" json:"waterMarkPosition"`
	// unused field # 103
	// unused field # 104
	// unused field # 105
	// unused field # 106
	// unused field # 107
	// unused field # 108
	// unused field # 109
	PreIssuedResource *common.ResourceGroup `thrift:"preIssuedResource,110" json:"preIssuedResource"`
	// unused field # 111
	// unused field # 112
	// unused field # 113
	// unused field # 114
	// unused field # 115
	// unused field # 116
	// unused field # 117
	// unused field # 118
	// unused field # 119
	AppId int32 `thrift:"appId,120" json:"appId"`
	ChnId int32 `thrift:"chnId,121" json:"chnId"`
}

func NewAdCreativeTemplate() *AdCreativeTemplate {
	return &AdCreativeTemplate{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Size: math.MinInt32 - 1, // unset sentinal value

		ImageType: math.MinInt32 - 1, // unset sentinal value

		WaterMarkPosition: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCreativeTemplate) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AdCreativeTemplate) IsSetSize() bool {
	return int64(p.Size) != math.MinInt32-1
}

func (p *AdCreativeTemplate) IsSetImageType() bool {
	return int64(p.ImageType) != math.MinInt32-1
}

func (p *AdCreativeTemplate) IsSetWaterMarkPosition() bool {
	return int64(p.WaterMarkPosition) != math.MinInt32-1
}

func (p *AdCreativeTemplate) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.I32 {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.I32 {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.STRING {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.STRING {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 100:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField100(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 101:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField101(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 102:
			if fieldTypeId == thrift.I32 {
				if err := p.readField102(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 110:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField110(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 120:
			if fieldTypeId == thrift.I32 {
				if err := p.readField120(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 121:
			if fieldTypeId == thrift.I32 {
				if err := p.readField121(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeTemplate) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.TypeA1 = AdCreativeTemplateType(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Specification = AdCreativeTemplateSpecIdInt(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Size = TemplateSizeCode(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Image = ImgIdInt(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Icon = ImgIdInt(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.ImageType = ImageType(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.Isize = TemplateSizeCodeInt(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.Html = ResIdInt(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Url = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField18(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 18: %s", err)
	} else {
		p.HDicon = ImgIdInt(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.Title = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.Brief = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.Detail = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField100(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 100: %s", err)
	} else {
		p.IsOriginal = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField101(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 101: %s", err)
	} else {
		p.ShouldIgnoreWaterMark = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField102(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 102: %s", err)
	} else {
		p.WaterMarkPosition = WaterMarkPosition(v)
	}
	return nil
}

func (p *AdCreativeTemplate) readField110(iprot thrift.TProtocol) error {
	p.PreIssuedResource = common.NewResourceGroup()
	if err := p.PreIssuedResource.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.PreIssuedResource)
	}
	return nil
}

func (p *AdCreativeTemplate) readField120(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 120: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AdCreativeTemplate) readField121(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 121: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdCreativeTemplate) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCreativeTemplate"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField100(oprot); err != nil {
		return err
	}
	if err := p.writeField101(oprot); err != nil {
		return err
	}
	if err := p.writeField102(oprot); err != nil {
		return err
	}
	if err := p.writeField110(oprot); err != nil {
		return err
	}
	if err := p.writeField120(oprot); err != nil {
		return err
	}
	if err := p.writeField121(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeTemplate) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:name: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:type: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("specification", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:specification: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Specification)); err != nil {
		return fmt.Errorf("%T.specification (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:specification: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("size", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:size: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Size)); err != nil {
		return fmt.Errorf("%T.size (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:size: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("image", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:image: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Image)); err != nil {
		return fmt.Errorf("%T.image (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:image: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("icon", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:icon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Icon)); err != nil {
		return fmt.Errorf("%T.icon (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:icon: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("imageType", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:imageType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ImageType)); err != nil {
		return fmt.Errorf("%T.imageType (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:imageType: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isize", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:isize: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Isize)); err != nil {
		return fmt.Errorf("%T.isize (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:isize: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("html", thrift.I32, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:html: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Html)); err != nil {
		return fmt.Errorf("%T.html (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:html: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("url", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:url: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Url)); err != nil {
		return fmt.Errorf("%T.url (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:url: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField18(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("HDicon", thrift.I32, 18); err != nil {
		return fmt.Errorf("%T write field begin error 18:HDicon: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.HDicon)); err != nil {
		return fmt.Errorf("%T.HDicon (18) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 18:HDicon: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("title", thrift.STRING, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:title: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Title)); err != nil {
		return fmt.Errorf("%T.title (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:title: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("brief", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:brief: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Brief)); err != nil {
		return fmt.Errorf("%T.brief (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:brief: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("detail", thrift.STRING, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:detail: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Detail)); err != nil {
		return fmt.Errorf("%T.detail (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:detail: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField100(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("isOriginal", thrift.BOOL, 100); err != nil {
		return fmt.Errorf("%T write field begin error 100:isOriginal: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.IsOriginal)); err != nil {
		return fmt.Errorf("%T.isOriginal (100) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 100:isOriginal: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField101(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("shouldIgnoreWaterMark", thrift.BOOL, 101); err != nil {
		return fmt.Errorf("%T write field begin error 101:shouldIgnoreWaterMark: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.ShouldIgnoreWaterMark)); err != nil {
		return fmt.Errorf("%T.shouldIgnoreWaterMark (101) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 101:shouldIgnoreWaterMark: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField102(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("waterMarkPosition", thrift.I32, 102); err != nil {
		return fmt.Errorf("%T write field begin error 102:waterMarkPosition: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.WaterMarkPosition)); err != nil {
		return fmt.Errorf("%T.waterMarkPosition (102) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 102:waterMarkPosition: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField110(oprot thrift.TProtocol) (err error) {
	if p.PreIssuedResource != nil {
		if err := oprot.WriteFieldBegin("preIssuedResource", thrift.STRUCT, 110); err != nil {
			return fmt.Errorf("%T write field begin error 110:preIssuedResource: %s", p, err)
		}
		if err := p.PreIssuedResource.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.PreIssuedResource)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 110:preIssuedResource: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeTemplate) writeField120(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 120); err != nil {
		return fmt.Errorf("%T write field begin error 120:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (120) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 120:appId: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) writeField121(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 121); err != nil {
		return fmt.Errorf("%T write field begin error 121:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (121) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 121:chnId: %s", p, err)
	}
	return err
}

func (p *AdCreativeTemplate) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCreativeTemplate(%+v)", *p)
}

type AdCreativeMaterial struct {
	Templates   []*AdCreativeTemplate `thrift:"templates,1" json:"templates"`
	RenderTypes []AdRenderType        `thrift:"renderTypes,2" json:"renderTypes"`
	// unused field # 3
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Text       string             `thrift:"text,10" json:"text"`
	IconType   AdCreativeIconType `thrift:"iconType,11" json:"iconType"`
	ForeColor  string             `thrift:"foreColor,12" json:"foreColor"`
	BgColor    string             `thrift:"bgColor,13" json:"bgColor"`
	DisplayUrl string             `thrift:"displayUrl,14" json:"displayUrl"`
	// unused field # 15
	// unused field # 16
	// unused field # 17
	// unused field # 18
	TemplateCode HtmlTemplateCodeInt `thrift:"templateCode,19" json:"templateCode"`
	JsonInfo     string              `thrift:"jsonInfo,20" json:"jsonInfo"`
	// unused field # 21
	// unused field # 22
	// unused field # 23
	// unused field # 24
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	AltText          string           `thrift:"altText,30" json:"altText"`
	LandingDirection LandingDirection `thrift:"landingDirection,31" json:"landingDirection"`
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	AppId int32 `thrift:"appId,40" json:"appId"`
	ChnId int32 `thrift:"chnId,41" json:"chnId"`
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	ActionUrl             string  `thrift:"actionUrl,64" json:"actionUrl"`
	Longitude             float64 `thrift:"longitude,65" json:"longitude"`
	Latitude              float64 `thrift:"latitude,66" json:"latitude"`
	ActionCall            string  `thrift:"actionCall,67" json:"actionCall"`
	ActionSMSNumber       string  `thrift:"actionSMSNumber,68" json:"actionSMSNumber"`
	ActionSMSMessage      string  `thrift:"actionSMSMessage,69" json:"actionSMSMessage"`
	ActionMailAddress     string  `thrift:"actionMailAddress,70" json:"actionMailAddress"`
	ActionMailSubject     string  `thrift:"actionMailSubject,71" json:"actionMailSubject"`
	ActionMailBody        string  `thrift:"actionMailBody,72" json:"actionMailBody"`
	ActionUrlFailsafe     string  `thrift:"actionUrlFailsafe,73" json:"actionUrlFailsafe"`
	ImpressionTrackingUrl string  `thrift:"impressionTrackingUrl,74" json:"impressionTrackingUrl"`
	ClickTrackingUrl      string  `thrift:"clickTrackingUrl,75" json:"clickTrackingUrl"`
}

func NewAdCreativeMaterial() *AdCreativeMaterial {
	return &AdCreativeMaterial{
		IconType: math.MinInt32 - 1, // unset sentinal value

		LandingDirection: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCreativeMaterial) IsSetIconType() bool {
	return int64(p.IconType) != math.MinInt32-1
}

func (p *AdCreativeMaterial) IsSetLandingDirection() bool {
	return int64(p.LandingDirection) != math.MinInt32-1
}

func (p *AdCreativeMaterial) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.LIST {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRING {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.STRING {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.STRING {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.STRING {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.STRING {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.I32 {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 64:
			if fieldTypeId == thrift.STRING {
				if err := p.readField64(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 65:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField65(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 66:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField66(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 67:
			if fieldTypeId == thrift.STRING {
				if err := p.readField67(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 68:
			if fieldTypeId == thrift.STRING {
				if err := p.readField68(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 69:
			if fieldTypeId == thrift.STRING {
				if err := p.readField69(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 70:
			if fieldTypeId == thrift.STRING {
				if err := p.readField70(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 71:
			if fieldTypeId == thrift.STRING {
				if err := p.readField71(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 72:
			if fieldTypeId == thrift.STRING {
				if err := p.readField72(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 73:
			if fieldTypeId == thrift.STRING {
				if err := p.readField73(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 74:
			if fieldTypeId == thrift.STRING {
				if err := p.readField74(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 75:
			if fieldTypeId == thrift.STRING {
				if err := p.readField75(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeMaterial) readField1(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.Templates = make([]*AdCreativeTemplate, 0, size)
	for i := 0; i < size; i++ {
		_elem33 := NewAdCreativeTemplate()
		if err := _elem33.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem33)
		}
		p.Templates = append(p.Templates, _elem33)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeMaterial) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RenderTypes = make([]AdRenderType, 0, size)
	for i := 0; i < size; i++ {
		var _elem34 AdRenderType
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem34 = AdRenderType(v)
		}
		p.RenderTypes = append(p.RenderTypes, _elem34)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreativeMaterial) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Text = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.IconType = AdCreativeIconType(v)
	}
	return nil
}

func (p *AdCreativeMaterial) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.ForeColor = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.BgColor = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DisplayUrl = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.TemplateCode = HtmlTemplateCodeInt(v)
	}
	return nil
}

func (p *AdCreativeMaterial) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.JsonInfo = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField30(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 30: %s", err)
	} else {
		p.AltText = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField31(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 31: %s", err)
	} else {
		p.LandingDirection = LandingDirection(v)
	}
	return nil
}

func (p *AdCreativeMaterial) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField64(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 64: %s", err)
	} else {
		p.ActionUrl = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField65(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 65: %s", err)
	} else {
		p.Longitude = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField66(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 66: %s", err)
	} else {
		p.Latitude = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField67(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 67: %s", err)
	} else {
		p.ActionCall = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField68(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 68: %s", err)
	} else {
		p.ActionSMSNumber = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField69(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 69: %s", err)
	} else {
		p.ActionSMSMessage = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField70(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 70: %s", err)
	} else {
		p.ActionMailAddress = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField71(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 71: %s", err)
	} else {
		p.ActionMailSubject = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField72(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 72: %s", err)
	} else {
		p.ActionMailBody = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField73(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 73: %s", err)
	} else {
		p.ActionUrlFailsafe = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField74(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 74: %s", err)
	} else {
		p.ImpressionTrackingUrl = v
	}
	return nil
}

func (p *AdCreativeMaterial) readField75(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 75: %s", err)
	} else {
		p.ClickTrackingUrl = v
	}
	return nil
}

func (p *AdCreativeMaterial) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCreativeMaterial"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField64(oprot); err != nil {
		return err
	}
	if err := p.writeField65(oprot); err != nil {
		return err
	}
	if err := p.writeField66(oprot); err != nil {
		return err
	}
	if err := p.writeField67(oprot); err != nil {
		return err
	}
	if err := p.writeField68(oprot); err != nil {
		return err
	}
	if err := p.writeField69(oprot); err != nil {
		return err
	}
	if err := p.writeField70(oprot); err != nil {
		return err
	}
	if err := p.writeField71(oprot); err != nil {
		return err
	}
	if err := p.writeField72(oprot); err != nil {
		return err
	}
	if err := p.writeField73(oprot); err != nil {
		return err
	}
	if err := p.writeField74(oprot); err != nil {
		return err
	}
	if err := p.writeField75(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCreativeMaterial) writeField1(oprot thrift.TProtocol) (err error) {
	if p.Templates != nil {
		if err := oprot.WriteFieldBegin("templates", thrift.LIST, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:templates: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.Templates)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.Templates {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:templates: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeMaterial) writeField2(oprot thrift.TProtocol) (err error) {
	if p.RenderTypes != nil {
		if err := oprot.WriteFieldBegin("renderTypes", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:renderTypes: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RenderTypes)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RenderTypes {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:renderTypes: %s", p, err)
		}
	}
	return err
}

func (p *AdCreativeMaterial) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("text", thrift.STRING, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:text: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Text)); err != nil {
		return fmt.Errorf("%T.text (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:text: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("iconType", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:iconType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IconType)); err != nil {
		return fmt.Errorf("%T.iconType (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:iconType: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("foreColor", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:foreColor: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ForeColor)); err != nil {
		return fmt.Errorf("%T.foreColor (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:foreColor: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bgColor", thrift.STRING, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:bgColor: %s", p, err)
	}
	if err := oprot.WriteString(string(p.BgColor)); err != nil {
		return fmt.Errorf("%T.bgColor (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:bgColor: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayUrl", thrift.STRING, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:displayUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DisplayUrl)); err != nil {
		return fmt.Errorf("%T.displayUrl (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:displayUrl: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("templateCode", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:templateCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TemplateCode)); err != nil {
		return fmt.Errorf("%T.templateCode (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:templateCode: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("jsonInfo", thrift.STRING, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:jsonInfo: %s", p, err)
	}
	if err := oprot.WriteString(string(p.JsonInfo)); err != nil {
		return fmt.Errorf("%T.jsonInfo (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:jsonInfo: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField30(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("altText", thrift.STRING, 30); err != nil {
		return fmt.Errorf("%T write field begin error 30:altText: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AltText)); err != nil {
		return fmt.Errorf("%T.altText (30) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 30:altText: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField31(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("landingDirection", thrift.I32, 31); err != nil {
		return fmt.Errorf("%T write field begin error 31:landingDirection: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.LandingDirection)); err != nil {
		return fmt.Errorf("%T.landingDirection (31) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 31:landingDirection: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:appId: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:chnId: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField64(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrl", thrift.STRING, 64); err != nil {
		return fmt.Errorf("%T write field begin error 64:actionUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrl)); err != nil {
		return fmt.Errorf("%T.actionUrl (64) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 64:actionUrl: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField65(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("longitude", thrift.DOUBLE, 65); err != nil {
		return fmt.Errorf("%T write field begin error 65:longitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Longitude)); err != nil {
		return fmt.Errorf("%T.longitude (65) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 65:longitude: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField66(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("latitude", thrift.DOUBLE, 66); err != nil {
		return fmt.Errorf("%T write field begin error 66:latitude: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Latitude)); err != nil {
		return fmt.Errorf("%T.latitude (66) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 66:latitude: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField67(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionCall", thrift.STRING, 67); err != nil {
		return fmt.Errorf("%T write field begin error 67:actionCall: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionCall)); err != nil {
		return fmt.Errorf("%T.actionCall (67) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 67:actionCall: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField68(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionSMSNumber", thrift.STRING, 68); err != nil {
		return fmt.Errorf("%T write field begin error 68:actionSMSNumber: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionSMSNumber)); err != nil {
		return fmt.Errorf("%T.actionSMSNumber (68) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 68:actionSMSNumber: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField69(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionSMSMessage", thrift.STRING, 69); err != nil {
		return fmt.Errorf("%T write field begin error 69:actionSMSMessage: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionSMSMessage)); err != nil {
		return fmt.Errorf("%T.actionSMSMessage (69) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 69:actionSMSMessage: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField70(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailAddress", thrift.STRING, 70); err != nil {
		return fmt.Errorf("%T write field begin error 70:actionMailAddress: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailAddress)); err != nil {
		return fmt.Errorf("%T.actionMailAddress (70) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 70:actionMailAddress: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField71(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailSubject", thrift.STRING, 71); err != nil {
		return fmt.Errorf("%T write field begin error 71:actionMailSubject: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailSubject)); err != nil {
		return fmt.Errorf("%T.actionMailSubject (71) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 71:actionMailSubject: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField72(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionMailBody", thrift.STRING, 72); err != nil {
		return fmt.Errorf("%T write field begin error 72:actionMailBody: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionMailBody)); err != nil {
		return fmt.Errorf("%T.actionMailBody (72) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 72:actionMailBody: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField73(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionUrlFailsafe", thrift.STRING, 73); err != nil {
		return fmt.Errorf("%T write field begin error 73:actionUrlFailsafe: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ActionUrlFailsafe)); err != nil {
		return fmt.Errorf("%T.actionUrlFailsafe (73) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 73:actionUrlFailsafe: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField74(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("impressionTrackingUrl", thrift.STRING, 74); err != nil {
		return fmt.Errorf("%T write field begin error 74:impressionTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ImpressionTrackingUrl)); err != nil {
		return fmt.Errorf("%T.impressionTrackingUrl (74) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 74:impressionTrackingUrl: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) writeField75(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("clickTrackingUrl", thrift.STRING, 75); err != nil {
		return fmt.Errorf("%T write field begin error 75:clickTrackingUrl: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ClickTrackingUrl)); err != nil {
		return fmt.Errorf("%T.clickTrackingUrl (75) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 75:clickTrackingUrl: %s", p, err)
	}
	return err
}

func (p *AdCreativeMaterial) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCreativeMaterial(%+v)", *p)
}

type AdCreative struct {
	Uid         UidInt              `thrift:"uid,1" json:"uid"`
	Pid         AdPlanIdInt         `thrift:"pid,2" json:"pid"`
	Sid         AdStrategyIdInt     `thrift:"sid,3" json:"sid"`
	Id          AdCreativeIdInt     `thrift:"id,4" json:"id"`
	Name        string              `thrift:"name,5" json:"name"`
	Keywords    string              `thrift:"keywords,6" json:"keywords"`
	AppName     string              `thrift:"appName,7" json:"appName"`
	DisplayType AdCreativeType      `thrift:"displayType,8" json:"displayType"`
	Bid         CurrencyAmount      `thrift:"bid,9" json:"bid"`
	Material    *AdCreativeMaterial `thrift:"material,10" json:"material"`
	ActionType  AdActionType        `thrift:"actionType,11" json:"actionType"`
	Comment     string              `thrift:"comment,12" json:"comment"`
	// unused field # 13
	// unused field # 14
	UrlOpenType           SDKUrlOpenType      `thrift:"urlOpenType,15" json:"urlOpenType"`
	PackageName           string              `thrift:"packageName,16" json:"packageName"`
	Version               string              `thrift:"version,17" json:"version"`
	MinRequiredSDKVersion map[int32]int32     `thrift:"minRequiredSDKVersion,18" json:"minRequiredSDKVersion"`
	PlacementType         AdPlacementType     `thrift:"placementType,19" json:"placementType"`
	EffectiveShowTime     TimeInt             `thrift:"effectiveShowTime,20" json:"effectiveShowTime"`
	CompleteShowTime      TimeInt             `thrift:"completeShowTime,21" json:"completeShowTime"`
	AutoClose             bool                `thrift:"autoClose,22" json:"autoClose"`
	DisableRotate         bool                `thrift:"disableRotate,23" json:"disableRotate"`
	CloseButtonPosition   CloseButtonPosition `thrift:"closeButtonPosition,24" json:"closeButtonPosition"`
	// unused field # 25
	// unused field # 26
	// unused field # 27
	// unused field # 28
	// unused field # 29
	ExtInfo            map[string]string `thrift:"extInfo,30" json:"extInfo"`
	RequiredCapability []CapabilityIdInt `thrift:"requiredCapability,31" json:"requiredCapability"`
	// unused field # 32
	// unused field # 33
	// unused field # 34
	// unused field # 35
	// unused field # 36
	// unused field # 37
	// unused field # 38
	// unused field # 39
	AppId int32 `thrift:"appId,40" json:"appId"`
	ChnId int32 `thrift:"chnId,41" json:"chnId"`
	// unused field # 42
	// unused field # 43
	// unused field # 44
	// unused field # 45
	// unused field # 46
	// unused field # 47
	// unused field # 48
	// unused field # 49
	// unused field # 50
	// unused field # 51
	// unused field # 52
	// unused field # 53
	// unused field # 54
	// unused field # 55
	// unused field # 56
	// unused field # 57
	// unused field # 58
	// unused field # 59
	// unused field # 60
	// unused field # 61
	// unused field # 62
	// unused field # 63
	// unused field # 64
	// unused field # 65
	// unused field # 66
	// unused field # 67
	// unused field # 68
	// unused field # 69
	// unused field # 70
	// unused field # 71
	// unused field # 72
	// unused field # 73
	// unused field # 74
	// unused field # 75
	// unused field # 76
	// unused field # 77
	// unused field # 78
	// unused field # 79
	// unused field # 80
	// unused field # 81
	// unused field # 82
	// unused field # 83
	// unused field # 84
	// unused field # 85
	// unused field # 86
	// unused field # 87
	// unused field # 88
	// unused field # 89
	// unused field # 90
	// unused field # 91
	// unused field # 92
	// unused field # 93
	// unused field # 94
	// unused field # 95
	// unused field # 96
	// unused field # 97
	// unused field # 98
	// unused field # 99
	// unused field # 100
	CreateTime TimeInt          `thrift:"createTime,101" json:"createTime"`
	LastUpdate TimeInt          `thrift:"lastUpdate,102" json:"lastUpdate"`
	Paused     AdPauseStatus    `thrift:"paused,103" json:"paused"`
	Status     AdCreativeStatus `thrift:"status,104" json:"status"`
	// unused field # 105
	// unused field # 106
	// unused field # 107
	// unused field # 108
	// unused field # 109
	MediaBid    CurrencyAmount `thrift:"mediaBid,110" json:"mediaBid"`
	RealBid     CurrencyAmount `thrift:"realBid,111" json:"realBid"`
	VersionCode IdInt          `thrift:"versionCode,112" json:"versionCode"`
}

func NewAdCreative() *AdCreative {
	return &AdCreative{
		DisplayType: math.MinInt32 - 1, // unset sentinal value

		ActionType: math.MinInt32 - 1, // unset sentinal value

		UrlOpenType: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value

		CloseButtonPosition: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdCreative) IsSetDisplayType() bool {
	return int64(p.DisplayType) != math.MinInt32-1
}

func (p *AdCreative) IsSetActionType() bool {
	return int64(p.ActionType) != math.MinInt32-1
}

func (p *AdCreative) IsSetUrlOpenType() bool {
	return int64(p.UrlOpenType) != math.MinInt32-1
}

func (p *AdCreative) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *AdCreative) IsSetCloseButtonPosition() bool {
	return int64(p.CloseButtonPosition) != math.MinInt32-1
}

func (p *AdCreative) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdCreative) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdCreative) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.STRING {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.STRING {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I64 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 15:
			if fieldTypeId == thrift.I32 {
				if err := p.readField15(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 16:
			if fieldTypeId == thrift.STRING {
				if err := p.readField16(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 17:
			if fieldTypeId == thrift.STRING {
				if err := p.readField17(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 18:
			if fieldTypeId == thrift.MAP {
				if err := p.readField18(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 19:
			if fieldTypeId == thrift.I32 {
				if err := p.readField19(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 20:
			if fieldTypeId == thrift.I64 {
				if err := p.readField20(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 21:
			if fieldTypeId == thrift.I64 {
				if err := p.readField21(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 22:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField22(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 23:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField23(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 24:
			if fieldTypeId == thrift.I32 {
				if err := p.readField24(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 30:
			if fieldTypeId == thrift.MAP {
				if err := p.readField30(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 31:
			if fieldTypeId == thrift.LIST {
				if err := p.readField31(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 40:
			if fieldTypeId == thrift.I32 {
				if err := p.readField40(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 41:
			if fieldTypeId == thrift.I32 {
				if err := p.readField41(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 101:
			if fieldTypeId == thrift.I64 {
				if err := p.readField101(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 102:
			if fieldTypeId == thrift.I64 {
				if err := p.readField102(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 103:
			if fieldTypeId == thrift.I32 {
				if err := p.readField103(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 104:
			if fieldTypeId == thrift.I32 {
				if err := p.readField104(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 110:
			if fieldTypeId == thrift.I64 {
				if err := p.readField110(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 111:
			if fieldTypeId == thrift.I64 {
				if err := p.readField111(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 112:
			if fieldTypeId == thrift.I32 {
				if err := p.readField112(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdCreative) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Uid = UidInt(v)
	}
	return nil
}

func (p *AdCreative) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdCreative) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Sid = AdStrategyIdInt(v)
	}
	return nil
}

func (p *AdCreative) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Id = AdCreativeIdInt(v)
	}
	return nil
}

func (p *AdCreative) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdCreative) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.Keywords = v
	}
	return nil
}

func (p *AdCreative) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AppName = v
	}
	return nil
}

func (p *AdCreative) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.DisplayType = AdCreativeType(v)
	}
	return nil
}

func (p *AdCreative) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.Bid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdCreative) readField10(iprot thrift.TProtocol) error {
	p.Material = NewAdCreativeMaterial()
	if err := p.Material.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Material)
	}
	return nil
}

func (p *AdCreative) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.ActionType = AdActionType(v)
	}
	return nil
}

func (p *AdCreative) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.Comment = v
	}
	return nil
}

func (p *AdCreative) readField15(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 15: %s", err)
	} else {
		p.UrlOpenType = SDKUrlOpenType(v)
	}
	return nil
}

func (p *AdCreative) readField16(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 16: %s", err)
	} else {
		p.PackageName = v
	}
	return nil
}

func (p *AdCreative) readField17(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 17: %s", err)
	} else {
		p.Version = v
	}
	return nil
}

func (p *AdCreative) readField18(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.MinRequiredSDKVersion = make(map[int32]int32, size)
	for i := 0; i < size; i++ {
		var _key35 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key35 = v
		}
		var _val36 int32
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val36 = v
		}
		p.MinRequiredSDKVersion[_key35] = _val36
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdCreative) readField19(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 19: %s", err)
	} else {
		p.PlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *AdCreative) readField20(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 20: %s", err)
	} else {
		p.EffectiveShowTime = TimeInt(v)
	}
	return nil
}

func (p *AdCreative) readField21(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 21: %s", err)
	} else {
		p.CompleteShowTime = TimeInt(v)
	}
	return nil
}

func (p *AdCreative) readField22(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 22: %s", err)
	} else {
		p.AutoClose = v
	}
	return nil
}

func (p *AdCreative) readField23(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 23: %s", err)
	} else {
		p.DisableRotate = v
	}
	return nil
}

func (p *AdCreative) readField24(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 24: %s", err)
	} else {
		p.CloseButtonPosition = CloseButtonPosition(v)
	}
	return nil
}

func (p *AdCreative) readField30(iprot thrift.TProtocol) error {
	_, _, size, err := iprot.ReadMapBegin()
	if err != nil {
		return fmt.Errorf("error reading map begin: %s")
	}
	p.ExtInfo = make(map[string]string, size)
	for i := 0; i < size; i++ {
		var _key37 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_key37 = v
		}
		var _val38 string
		if v, err := iprot.ReadString(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_val38 = v
		}
		p.ExtInfo[_key37] = _val38
	}
	if err := iprot.ReadMapEnd(); err != nil {
		return fmt.Errorf("error reading map end: %s", err)
	}
	return nil
}

func (p *AdCreative) readField31(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.RequiredCapability = make([]CapabilityIdInt, 0, size)
	for i := 0; i < size; i++ {
		var _elem39 CapabilityIdInt
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem39 = CapabilityIdInt(v)
		}
		p.RequiredCapability = append(p.RequiredCapability, _elem39)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdCreative) readField40(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 40: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *AdCreative) readField41(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 41: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *AdCreative) readField101(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 101: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdCreative) readField102(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 102: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdCreative) readField103(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 103: %s", err)
	} else {
		p.Paused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdCreative) readField104(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 104: %s", err)
	} else {
		p.Status = AdCreativeStatus(v)
	}
	return nil
}

func (p *AdCreative) readField110(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 110: %s", err)
	} else {
		p.MediaBid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdCreative) readField111(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 111: %s", err)
	} else {
		p.RealBid = CurrencyAmount(v)
	}
	return nil
}

func (p *AdCreative) readField112(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 112: %s", err)
	} else {
		p.VersionCode = IdInt(v)
	}
	return nil
}

func (p *AdCreative) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdCreative"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField15(oprot); err != nil {
		return err
	}
	if err := p.writeField16(oprot); err != nil {
		return err
	}
	if err := p.writeField17(oprot); err != nil {
		return err
	}
	if err := p.writeField18(oprot); err != nil {
		return err
	}
	if err := p.writeField19(oprot); err != nil {
		return err
	}
	if err := p.writeField20(oprot); err != nil {
		return err
	}
	if err := p.writeField21(oprot); err != nil {
		return err
	}
	if err := p.writeField22(oprot); err != nil {
		return err
	}
	if err := p.writeField23(oprot); err != nil {
		return err
	}
	if err := p.writeField24(oprot); err != nil {
		return err
	}
	if err := p.writeField30(oprot); err != nil {
		return err
	}
	if err := p.writeField31(oprot); err != nil {
		return err
	}
	if err := p.writeField40(oprot); err != nil {
		return err
	}
	if err := p.writeField41(oprot); err != nil {
		return err
	}
	if err := p.writeField101(oprot); err != nil {
		return err
	}
	if err := p.writeField102(oprot); err != nil {
		return err
	}
	if err := p.writeField103(oprot); err != nil {
		return err
	}
	if err := p.writeField104(oprot); err != nil {
		return err
	}
	if err := p.writeField110(oprot); err != nil {
		return err
	}
	if err := p.writeField111(oprot); err != nil {
		return err
	}
	if err := p.writeField112(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdCreative) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("uid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:uid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Uid)); err != nil {
		return fmt.Errorf("%T.uid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:uid: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:pid: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:sid: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:id: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:name: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keywords", thrift.STRING, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:keywords: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Keywords)); err != nil {
		return fmt.Errorf("%T.keywords (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:keywords: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appName", thrift.STRING, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:appName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppName)); err != nil {
		return fmt.Errorf("%T.appName (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:appName: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("displayType", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:displayType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DisplayType)); err != nil {
		return fmt.Errorf("%T.displayType (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:displayType: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("bid", thrift.I64, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:bid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Bid)); err != nil {
		return fmt.Errorf("%T.bid (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:bid: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField10(oprot thrift.TProtocol) (err error) {
	if p.Material != nil {
		if err := oprot.WriteFieldBegin("material", thrift.STRUCT, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:material: %s", p, err)
		}
		if err := p.Material.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Material)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:material: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("actionType", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:actionType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ActionType)); err != nil {
		return fmt.Errorf("%T.actionType (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:actionType: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("comment", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:comment: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Comment)); err != nil {
		return fmt.Errorf("%T.comment (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:comment: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField15(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("urlOpenType", thrift.I32, 15); err != nil {
		return fmt.Errorf("%T write field begin error 15:urlOpenType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.UrlOpenType)); err != nil {
		return fmt.Errorf("%T.urlOpenType (15) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 15:urlOpenType: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField16(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("packageName", thrift.STRING, 16); err != nil {
		return fmt.Errorf("%T write field begin error 16:packageName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.PackageName)); err != nil {
		return fmt.Errorf("%T.packageName (16) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 16:packageName: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField17(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("version", thrift.STRING, 17); err != nil {
		return fmt.Errorf("%T write field begin error 17:version: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Version)); err != nil {
		return fmt.Errorf("%T.version (17) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 17:version: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField18(oprot thrift.TProtocol) (err error) {
	if p.MinRequiredSDKVersion != nil {
		if err := oprot.WriteFieldBegin("minRequiredSDKVersion", thrift.MAP, 18); err != nil {
			return fmt.Errorf("%T write field begin error 18:minRequiredSDKVersion: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.I32, thrift.I32, len(p.MinRequiredSDKVersion)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.MinRequiredSDKVersion {
			if err := oprot.WriteI32(int32(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 18:minRequiredSDKVersion: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField19(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 19); err != nil {
		return fmt.Errorf("%T write field begin error 19:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (19) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 19:placementType: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField20(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("effectiveShowTime", thrift.I64, 20); err != nil {
		return fmt.Errorf("%T write field begin error 20:effectiveShowTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.EffectiveShowTime)); err != nil {
		return fmt.Errorf("%T.effectiveShowTime (20) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 20:effectiveShowTime: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField21(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("completeShowTime", thrift.I64, 21); err != nil {
		return fmt.Errorf("%T write field begin error 21:completeShowTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CompleteShowTime)); err != nil {
		return fmt.Errorf("%T.completeShowTime (21) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 21:completeShowTime: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField22(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("autoClose", thrift.BOOL, 22); err != nil {
		return fmt.Errorf("%T write field begin error 22:autoClose: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.AutoClose)); err != nil {
		return fmt.Errorf("%T.autoClose (22) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 22:autoClose: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField23(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("disableRotate", thrift.BOOL, 23); err != nil {
		return fmt.Errorf("%T write field begin error 23:disableRotate: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.DisableRotate)); err != nil {
		return fmt.Errorf("%T.disableRotate (23) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 23:disableRotate: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField24(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("closeButtonPosition", thrift.I32, 24); err != nil {
		return fmt.Errorf("%T write field begin error 24:closeButtonPosition: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CloseButtonPosition)); err != nil {
		return fmt.Errorf("%T.closeButtonPosition (24) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 24:closeButtonPosition: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField30(oprot thrift.TProtocol) (err error) {
	if p.ExtInfo != nil {
		if err := oprot.WriteFieldBegin("extInfo", thrift.MAP, 30); err != nil {
			return fmt.Errorf("%T write field begin error 30:extInfo: %s", p, err)
		}
		if err := oprot.WriteMapBegin(thrift.STRING, thrift.STRING, len(p.ExtInfo)); err != nil {
			return fmt.Errorf("error writing map begin: %s")
		}
		for k, v := range p.ExtInfo {
			if err := oprot.WriteString(string(k)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
			if err := oprot.WriteString(string(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteMapEnd(); err != nil {
			return fmt.Errorf("error writing map end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 30:extInfo: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField31(oprot thrift.TProtocol) (err error) {
	if p.RequiredCapability != nil {
		if err := oprot.WriteFieldBegin("requiredCapability", thrift.LIST, 31); err != nil {
			return fmt.Errorf("%T write field begin error 31:requiredCapability: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.RequiredCapability)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.RequiredCapability {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 31:requiredCapability: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField40(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("appId", thrift.I32, 40); err != nil {
		return fmt.Errorf("%T write field begin error 40:appId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.appId (40) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 40:appId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField41(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chnId", thrift.I32, 41); err != nil {
		return fmt.Errorf("%T write field begin error 41:chnId: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chnId (41) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 41:chnId: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField101(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 101); err != nil {
		return fmt.Errorf("%T write field begin error 101:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (101) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 101:createTime: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField102(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 102); err != nil {
		return fmt.Errorf("%T write field begin error 102:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (102) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 102:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField103(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 103); err != nil {
			return fmt.Errorf("%T write field begin error 103:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (103) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 103:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField104(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 104); err != nil {
			return fmt.Errorf("%T write field begin error 104:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (104) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 104:status: %s", p, err)
		}
	}
	return err
}

func (p *AdCreative) writeField110(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("mediaBid", thrift.I64, 110); err != nil {
		return fmt.Errorf("%T write field begin error 110:mediaBid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.MediaBid)); err != nil {
		return fmt.Errorf("%T.mediaBid (110) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 110:mediaBid: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField111(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("realBid", thrift.I64, 111); err != nil {
		return fmt.Errorf("%T write field begin error 111:realBid: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.RealBid)); err != nil {
		return fmt.Errorf("%T.realBid (111) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 111:realBid: %s", p, err)
	}
	return err
}

func (p *AdCreative) writeField112(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("versionCode", thrift.I32, 112); err != nil {
		return fmt.Errorf("%T write field begin error 112:versionCode: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.VersionCode)); err != nil {
		return fmt.Errorf("%T.versionCode (112) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 112:versionCode: %s", p, err)
	}
	return err
}

func (p *AdCreative) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdCreative(%+v)", *p)
}

type AdStatusLamp struct {
	Status       AdStatusLampColor  `thrift:"status,1" json:"status"`
	KnownReasons []AdStatusLampCode `thrift:"knownReasons,2" json:"knownReasons"`
}

func NewAdStatusLamp() *AdStatusLamp {
	return &AdStatusLamp{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdStatusLamp) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdStatusLamp) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.LIST {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdStatusLamp) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = AdStatusLampColor(v)
	}
	return nil
}

func (p *AdStatusLamp) readField2(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.KnownReasons = make([]AdStatusLampCode, 0, size)
	for i := 0; i < size; i++ {
		var _elem40 AdStatusLampCode
		if v, err := iprot.ReadI32(); err != nil {
			return fmt.Errorf("error reading field 0: %s", err)
		} else {
			_elem40 = AdStatusLampCode(v)
		}
		p.KnownReasons = append(p.KnownReasons, _elem40)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdStatusLamp) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdStatusLamp"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdStatusLamp) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *AdStatusLamp) writeField2(oprot thrift.TProtocol) (err error) {
	if p.KnownReasons != nil {
		if err := oprot.WriteFieldBegin("knownReasons", thrift.LIST, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:knownReasons: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.I32, len(p.KnownReasons)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.KnownReasons {
			if err := oprot.WriteI32(int32(v)); err != nil {
				return fmt.Errorf("%T. (0) field write error: %s", p, err)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:knownReasons: %s", p, err)
		}
	}
	return err
}

func (p *AdStatusLamp) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdStatusLamp(%+v)", *p)
}

type AdInfoCidResult struct {
	Cid     AdCreativeIdInt `thrift:"cid,1" json:"cid"`
	Deleted bool            `thrift:"deleted,2" json:"deleted"`
	Name    string          `thrift:"name,3" json:"name"`
	// unused field # 4
	// unused field # 5
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Status AdCreativeStatus `thrift:"status,10" json:"status"`
	Paused AdPauseStatus    `thrift:"paused,11" json:"paused"`
}

func NewAdInfoCidResult() *AdInfoCidResult {
	return &AdInfoCidResult{
		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdInfoCidResult) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdInfoCidResult) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdInfoCidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdInfoCidResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Cid = AdCreativeIdInt(v)
	}
	return nil
}

func (p *AdInfoCidResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AdInfoCidResult) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdInfoCidResult) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = AdCreativeStatus(v)
	}
	return nil
}

func (p *AdInfoCidResult) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Paused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdInfoCidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdInfoCidResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdInfoCidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:cid: %s", p, err)
	}
	return err
}

func (p *AdInfoCidResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deleted: %s", p, err)
	}
	return err
}

func (p *AdInfoCidResult) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:name: %s", p, err)
	}
	return err
}

func (p *AdInfoCidResult) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:status: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoCidResult) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoCidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdInfoCidResult(%+v)", *p)
}

type AdInfoSidResult struct {
	Sid        AdStrategyIdInt    `thrift:"sid,1" json:"sid"`
	Deleted    bool               `thrift:"deleted,2" json:"deleted"`
	CidResults []*AdInfoCidResult `thrift:"cidResults,3" json:"cidResults"`
	Name       string             `thrift:"name,4" json:"name"`
	TypeA1     AdStrategyType     `thrift:"type,5" json:"type"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Status        AdStrategyStatus `thrift:"status,10" json:"status"`
	Paused        AdPauseStatus    `thrift:"paused,11" json:"paused"`
	PlacementType AdPlacementType  `thrift:"placementType,12" json:"placementType"`
}

func NewAdInfoSidResult() *AdInfoSidResult {
	return &AdInfoSidResult{
		TypeA1: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value

		PlacementType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdInfoSidResult) IsSetTypeA1() bool {
	return int64(p.TypeA1) != math.MinInt32-1
}

func (p *AdInfoSidResult) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdInfoSidResult) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdInfoSidResult) IsSetPlacementType() bool {
	return int64(p.PlacementType) != math.MinInt32-1
}

func (p *AdInfoSidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.I32 {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdInfoSidResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Sid = AdStrategyIdInt(v)
	}
	return nil
}

func (p *AdInfoSidResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AdInfoSidResult) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.CidResults = make([]*AdInfoCidResult, 0, size)
	for i := 0; i < size; i++ {
		_elem41 := NewAdInfoCidResult()
		if err := _elem41.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem41)
		}
		p.CidResults = append(p.CidResults, _elem41)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdInfoSidResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdInfoSidResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.TypeA1 = AdStrategyType(v)
	}
	return nil
}

func (p *AdInfoSidResult) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = AdStrategyStatus(v)
	}
	return nil
}

func (p *AdInfoSidResult) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Paused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdInfoSidResult) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.PlacementType = AdPlacementType(v)
	}
	return nil
}

func (p *AdInfoSidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdInfoSidResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdInfoSidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:sid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Sid)); err != nil {
		return fmt.Errorf("%T.sid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:sid: %s", p, err)
	}
	return err
}

func (p *AdInfoSidResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deleted: %s", p, err)
	}
	return err
}

func (p *AdInfoSidResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.CidResults != nil {
		if err := oprot.WriteFieldBegin("cidResults", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:cidResults: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.CidResults)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.CidResults {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:cidResults: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoSidResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AdInfoSidResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("type", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.TypeA1)); err != nil {
		return fmt.Errorf("%T.type (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:type: %s", p, err)
	}
	return err
}

func (p *AdInfoSidResult) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:status: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoSidResult) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoSidResult) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("placementType", thrift.I32, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:placementType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PlacementType)); err != nil {
		return fmt.Errorf("%T.placementType (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:placementType: %s", p, err)
	}
	return err
}

func (p *AdInfoSidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdInfoSidResult(%+v)", *p)
}

type AdInfoPidResult struct {
	Pid        AdPlanIdInt        `thrift:"pid,1" json:"pid"`
	Deleted    bool               `thrift:"deleted,2" json:"deleted"`
	SidResults []*AdInfoSidResult `thrift:"sidResults,3" json:"sidResults"`
	Name       string             `thrift:"name,4" json:"name"`
	CostType   CostType           `thrift:"costType,5" json:"costType"`
	// unused field # 6
	// unused field # 7
	// unused field # 8
	// unused field # 9
	Status AdPlanStatus  `thrift:"status,10" json:"status"`
	Paused AdPauseStatus `thrift:"paused,11" json:"paused"`
}

func NewAdInfoPidResult() *AdInfoPidResult {
	return &AdInfoPidResult{
		CostType: math.MinInt32 - 1, // unset sentinal value

		Status: math.MinInt32 - 1, // unset sentinal value

		Paused: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AdInfoPidResult) IsSetCostType() bool {
	return int64(p.CostType) != math.MinInt32-1
}

func (p *AdInfoPidResult) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AdInfoPidResult) IsSetPaused() bool {
	return int64(p.Paused) != math.MinInt32-1
}

func (p *AdInfoPidResult) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.LIST {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdInfoPidResult) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Pid = AdPlanIdInt(v)
	}
	return nil
}

func (p *AdInfoPidResult) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Deleted = v
	}
	return nil
}

func (p *AdInfoPidResult) readField3(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.SidResults = make([]*AdInfoSidResult, 0, size)
	for i := 0; i < size; i++ {
		_elem42 := NewAdInfoSidResult()
		if err := _elem42.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem42)
		}
		p.SidResults = append(p.SidResults, _elem42)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *AdInfoPidResult) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Name = v
	}
	return nil
}

func (p *AdInfoPidResult) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CostType = CostType(v)
	}
	return nil
}

func (p *AdInfoPidResult) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.Status = AdPlanStatus(v)
	}
	return nil
}

func (p *AdInfoPidResult) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.Paused = AdPauseStatus(v)
	}
	return nil
}

func (p *AdInfoPidResult) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdInfoPidResult"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdInfoPidResult) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("pid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:pid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Pid)); err != nil {
		return fmt.Errorf("%T.pid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:pid: %s", p, err)
	}
	return err
}

func (p *AdInfoPidResult) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deleted", thrift.BOOL, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:deleted: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.Deleted)); err != nil {
		return fmt.Errorf("%T.deleted (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:deleted: %s", p, err)
	}
	return err
}

func (p *AdInfoPidResult) writeField3(oprot thrift.TProtocol) (err error) {
	if p.SidResults != nil {
		if err := oprot.WriteFieldBegin("sidResults", thrift.LIST, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:sidResults: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.SidResults)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.SidResults {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:sidResults: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoPidResult) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("name", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:name: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Name)); err != nil {
		return fmt.Errorf("%T.name (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:name: %s", p, err)
	}
	return err
}

func (p *AdInfoPidResult) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("costType", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:costType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CostType)); err != nil {
		return fmt.Errorf("%T.costType (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:costType: %s", p, err)
	}
	return err
}

func (p *AdInfoPidResult) writeField10(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 10); err != nil {
			return fmt.Errorf("%T write field begin error 10:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (10) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 10:status: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoPidResult) writeField11(oprot thrift.TProtocol) (err error) {
	if p.IsSetPaused() {
		if err := oprot.WriteFieldBegin("paused", thrift.I32, 11); err != nil {
			return fmt.Errorf("%T write field begin error 11:paused: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Paused)); err != nil {
			return fmt.Errorf("%T.paused (11) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 11:paused: %s", p, err)
		}
	}
	return err
}

func (p *AdInfoPidResult) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdInfoPidResult(%+v)", *p)
}

type AdInfoExtInfo struct {
	Id         IdInt     `thrift:"id,1" json:"id"`
	IdType     IdTypeInt `thrift:"idType,2" json:"idType"`
	KeyName    string    `thrift:"keyName,3" json:"keyName"`
	Value      string    `thrift:"value,4" json:"value"`
	CreateTime TimeInt   `thrift:"createTime,5" json:"createTime"`
	LastUpdate TimeInt   `thrift:"lastUpdate,6" json:"lastUpdate"`
}

func NewAdInfoExtInfo() *AdInfoExtInfo {
	return &AdInfoExtInfo{}
}

func (p *AdInfoExtInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.STRING {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I64 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AdInfoExtInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Id = IdInt(v)
	}
	return nil
}

func (p *AdInfoExtInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.IdType = IdTypeInt(v)
	}
	return nil
}

func (p *AdInfoExtInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.KeyName = v
	}
	return nil
}

func (p *AdInfoExtInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Value = v
	}
	return nil
}

func (p *AdInfoExtInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.CreateTime = TimeInt(v)
	}
	return nil
}

func (p *AdInfoExtInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.LastUpdate = TimeInt(v)
	}
	return nil
}

func (p *AdInfoExtInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AdInfoExtInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AdInfoExtInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("id", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Id)); err != nil {
		return fmt.Errorf("%T.id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:id: %s", p, err)
	}
	return err
}

func (p *AdInfoExtInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("idType", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:idType: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.IdType)); err != nil {
		return fmt.Errorf("%T.idType (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:idType: %s", p, err)
	}
	return err
}

func (p *AdInfoExtInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("keyName", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:keyName: %s", p, err)
	}
	if err := oprot.WriteString(string(p.KeyName)); err != nil {
		return fmt.Errorf("%T.keyName (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:keyName: %s", p, err)
	}
	return err
}

func (p *AdInfoExtInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("value", thrift.STRING, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:value: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Value)); err != nil {
		return fmt.Errorf("%T.value (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:value: %s", p, err)
	}
	return err
}

func (p *AdInfoExtInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("createTime", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:createTime: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.CreateTime)); err != nil {
		return fmt.Errorf("%T.createTime (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:createTime: %s", p, err)
	}
	return err
}

func (p *AdInfoExtInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("lastUpdate", thrift.I64, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:lastUpdate: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.LastUpdate)); err != nil {
		return fmt.Errorf("%T.lastUpdate (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:lastUpdate: %s", p, err)
	}
	return err
}

func (p *AdInfoExtInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AdInfoExtInfo(%+v)", *p)
}
