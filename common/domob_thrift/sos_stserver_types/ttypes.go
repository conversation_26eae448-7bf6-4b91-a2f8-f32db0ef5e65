// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_stserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var GoUnusedProtection__ int

type StStatus int64

const (
	StStatus_SUCCESS StStatus = 0
	StStatus_FAIL    StStatus = 1
)

func (p StStatus) String() string {
	switch p {
	case StStatus_SUCCESS:
		return "StStatus_SUCCESS"
	case StStatus_FAIL:
		return "StStatus_FAIL"
	}
	return "<UNSET>"
}

func StStatusFromString(s string) (StStatus, error) {
	switch s {
	case "StStatus_SUCCESS":
		return StStatus_SUCCESS, nil
	case "StStatus_FAIL":
		return StStatus_FAIL, nil
	}
	return StStatus(math.MinInt32 - 1), fmt.<PERSON><PERSON><PERSON>("not a valid StStatus string")
}

type DrawStatus int64

const (
	DrawStatus_LUCKY   DrawStatus = 0
	DrawStatus_UNLUCKY DrawStatus = 1
)

func (p DrawStatus) String() string {
	switch p {
	case DrawStatus_LUCKY:
		return "DrawStatus_LUCKY"
	case DrawStatus_UNLUCKY:
		return "DrawStatus_UNLUCKY"
	}
	return "<UNSET>"
}

func DrawStatusFromString(s string) (DrawStatus, error) {
	switch s {
	case "DrawStatus_LUCKY":
		return DrawStatus_LUCKY, nil
	case "DrawStatus_UNLUCKY":
		return DrawStatus_UNLUCKY, nil
	}
	return DrawStatus(math.MinInt32 - 1), fmt.Errorf("not a valid DrawStatus string")
}

type StObject struct {
	Stid         int32 `thrift:"stid,1" json:"stid"`
	Userid       int32 `thrift:"userid,2" json:"userid"`
	Deviceid     int32 `thrift:"deviceid,3" json:"deviceid"`
	WithDivide   bool  `thrift:"with_divide,4" json:"with_divide"`
	UseSuperTask bool  `thrift:"use_super_task,5" json:"use_super_task"`
}

func NewStObject() *StObject {
	return &StObject{}
}

func (p *StObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StObject) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Stid = v
	}
	return nil
}

func (p *StObject) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *StObject) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Deviceid = v
	}
	return nil
}

func (p *StObject) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.WithDivide = v
	}
	return nil
}

func (p *StObject) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.UseSuperTask = v
	}
	return nil
}

func (p *StObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("stid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:stid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Stid)); err != nil {
		return fmt.Errorf("%T.stid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:stid: %s", p, err)
	}
	return err
}

func (p *StObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:userid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:userid: %s", p, err)
	}
	return err
}

func (p *StObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deviceid", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:deviceid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Deviceid)); err != nil {
		return fmt.Errorf("%T.deviceid (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:deviceid: %s", p, err)
	}
	return err
}

func (p *StObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_divide", thrift.BOOL, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:with_divide: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithDivide)); err != nil {
		return fmt.Errorf("%T.with_divide (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:with_divide: %s", p, err)
	}
	return err
}

func (p *StObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("use_super_task", thrift.BOOL, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:use_super_task: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.UseSuperTask)); err != nil {
		return fmt.Errorf("%T.use_super_task (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:use_super_task: %s", p, err)
	}
	return err
}

func (p *StObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StObject(%+v)", *p)
}

type StResponse struct {
	Status StStatus `thrift:"status,1" json:"status"`
	Msg    string   `thrift:"msg,2" json:"msg"`
}

func NewStResponse() *StResponse {
	return &StResponse{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *StResponse) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *StResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *StResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = StStatus(v)
	}
	return nil
}

func (p *StResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *StResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("StResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *StResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *StResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *StResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("StResponse(%+v)", *p)
}

type DrawObject struct {
	Userid int32 `thrift:"userid,1" json:"userid"`
}

func NewDrawObject() *DrawObject {
	return &DrawObject{}
}

func (p *DrawObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DrawObject) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *DrawObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DrawObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DrawObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:userid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:userid: %s", p, err)
	}
	return err
}

func (p *DrawObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DrawObject(%+v)", *p)
}

type DrawResponse struct {
	Status DrawStatus `thrift:"status,1" json:"status"`
	Point  float64    `thrift:"point,2" json:"point"`
	Msg    string     `thrift:"msg,3" json:"msg"`
}

func NewDrawResponse() *DrawResponse {
	return &DrawResponse{
		Status: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *DrawResponse) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *DrawResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DrawResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = DrawStatus(v)
	}
	return nil
}

func (p *DrawResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Point = v
	}
	return nil
}

func (p *DrawResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *DrawResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DrawResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DrawResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *DrawResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:point: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Point)); err != nil {
		return fmt.Errorf("%T.point (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:point: %s", p, err)
	}
	return err
}

func (p *DrawResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:msg: %s", p, err)
	}
	return err
}

func (p *DrawResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DrawResponse(%+v)", *p)
}

type PartnerObject struct {
	Masterid int32  `thrift:"masterid,1" json:"masterid"`
	Slaveid  int32  `thrift:"slaveid,2" json:"slaveid"`
	Ip       string `thrift:"ip,3" json:"ip"`
	Did      int32  `thrift:"did,4" json:"did"`
	Psd      string `thrift:"psd,5" json:"psd"`
	WithSim  bool   `thrift:"with_sim,6" json:"with_sim"`
}

func NewPartnerObject() *PartnerObject {
	return &PartnerObject{}
}

func (p *PartnerObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.BOOL {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PartnerObject) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Masterid = v
	}
	return nil
}

func (p *PartnerObject) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Slaveid = v
	}
	return nil
}

func (p *PartnerObject) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ip = v
	}
	return nil
}

func (p *PartnerObject) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Did = v
	}
	return nil
}

func (p *PartnerObject) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.Psd = v
	}
	return nil
}

func (p *PartnerObject) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadBool(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.WithSim = v
	}
	return nil
}

func (p *PartnerObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PartnerObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PartnerObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("masterid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:masterid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Masterid)); err != nil {
		return fmt.Errorf("%T.masterid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:masterid: %s", p, err)
	}
	return err
}

func (p *PartnerObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("slaveid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:slaveid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Slaveid)); err != nil {
		return fmt.Errorf("%T.slaveid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:slaveid: %s", p, err)
	}
	return err
}

func (p *PartnerObject) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ip", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ip: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Ip)); err != nil {
		return fmt.Errorf("%T.ip (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ip: %s", p, err)
	}
	return err
}

func (p *PartnerObject) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("did", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:did: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Did)); err != nil {
		return fmt.Errorf("%T.did (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:did: %s", p, err)
	}
	return err
}

func (p *PartnerObject) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("psd", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:psd: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Psd)); err != nil {
		return fmt.Errorf("%T.psd (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:psd: %s", p, err)
	}
	return err
}

func (p *PartnerObject) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("with_sim", thrift.BOOL, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:with_sim: %s", p, err)
	}
	if err := oprot.WriteBool(bool(p.WithSim)); err != nil {
		return fmt.Errorf("%T.with_sim (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:with_sim: %s", p, err)
	}
	return err
}

func (p *PartnerObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PartnerObject(%+v)", *p)
}

type GiftObject struct {
	Userid   int32  `thrift:"userid,1" json:"userid"`
	GiftCode string `thrift:"gift_code,2" json:"gift_code"`
}

func NewGiftObject() *GiftObject {
	return &GiftObject{}
}

func (p *GiftObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GiftObject) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Userid = v
	}
	return nil
}

func (p *GiftObject) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.GiftCode = v
	}
	return nil
}

func (p *GiftObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GiftObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GiftObject) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("userid", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:userid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Userid)); err != nil {
		return fmt.Errorf("%T.userid (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:userid: %s", p, err)
	}
	return err
}

func (p *GiftObject) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("gift_code", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:gift_code: %s", p, err)
	}
	if err := oprot.WriteString(string(p.GiftCode)); err != nil {
		return fmt.Errorf("%T.gift_code (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:gift_code: %s", p, err)
	}
	return err
}

func (p *GiftObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GiftObject(%+v)", *p)
}

type GiftResponse struct {
	Status int32   `thrift:"status,1" json:"status"`
	Points float64 `thrift:"points,2" json:"points"`
	Msg    string  `thrift:"msg,3" json:"msg"`
}

func NewGiftResponse() *GiftResponse {
	return &GiftResponse{}
}

func (p *GiftResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.DOUBLE {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.STRING {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *GiftResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *GiftResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadDouble(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Points = v
	}
	return nil
}

func (p *GiftResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *GiftResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("GiftResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *GiftResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *GiftResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("points", thrift.DOUBLE, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:points: %s", p, err)
	}
	if err := oprot.WriteDouble(float64(p.Points)); err != nil {
		return fmt.Errorf("%T.points (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:points: %s", p, err)
	}
	return err
}

func (p *GiftResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:msg: %s", p, err)
	}
	return err
}

func (p *GiftResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("GiftResponse(%+v)", *p)
}

type DrawConfig struct {
	NumLimit   int32 `thrift:"num_limit,1" json:"num_limit"`
	PointLimit int32 `thrift:"point_limit,2" json:"point_limit"`
}

func NewDrawConfig() *DrawConfig {
	return &DrawConfig{}
}

func (p *DrawConfig) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *DrawConfig) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.NumLimit = v
	}
	return nil
}

func (p *DrawConfig) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.PointLimit = v
	}
	return nil
}

func (p *DrawConfig) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("DrawConfig"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *DrawConfig) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("num_limit", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:num_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.NumLimit)); err != nil {
		return fmt.Errorf("%T.num_limit (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:num_limit: %s", p, err)
	}
	return err
}

func (p *DrawConfig) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("point_limit", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:point_limit: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.PointLimit)); err != nil {
		return fmt.Errorf("%T.point_limit (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:point_limit: %s", p, err)
	}
	return err
}

func (p *DrawConfig) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("DrawConfig(%+v)", *p)
}
