// Autogenerated by Thrift Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package sos_antiserver_types

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/common"
	"rtb_model_server/common/domob_thrift/sos_ui_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = common.GoUnusedProtection__
var _ = sos_ui_types.GoUnusedProtection__
var GoUnusedProtection__ int

type AntiStatus int64

const (
	AntiStatus_SUCCESS AntiStatus = 0
	AntiStatus_FAIL    AntiStatus = 1
)

func (p AntiStatus) String() string {
	switch p {
	case AntiStatus_SUCCESS:
		return "AntiStatus_SUCCESS"
	case AntiStatus_FAIL:
		return "AntiStatus_FAIL"
	}
	return "<UNSET>"
}

func AntiStatusFromString(s string) (AntiStatus, error) {
	switch s {
	case "AntiStatus_SUCCESS":
		return AntiStatus_SUCCESS, nil
	case "AntiStatus_FAIL":
		return AntiStatus_FAIL, nil
	}
	return AntiStatus(math.MinInt32 - 1), fmt.Errorf("not a valid AntiStatus string")
}

type SpamType int64

const (
	SpamType_NOSPAM       SpamType = 0
	SpamType_DEVICESPAM   SpamType = 1
	SpamType_INTERVALSPAM SpamType = 2
	SpamType_IPSPAM       SpamType = 3
	SpamType_AMAC         SpamType = 4
	SpamType_USER         SpamType = 5
)

func (p SpamType) String() string {
	switch p {
	case SpamType_NOSPAM:
		return "SpamType_NOSPAM"
	case SpamType_DEVICESPAM:
		return "SpamType_DEVICESPAM"
	case SpamType_INTERVALSPAM:
		return "SpamType_INTERVALSPAM"
	case SpamType_IPSPAM:
		return "SpamType_IPSPAM"
	case SpamType_AMAC:
		return "SpamType_AMAC"
	case SpamType_USER:
		return "SpamType_USER"
	}
	return "<UNSET>"
}

func SpamTypeFromString(s string) (SpamType, error) {
	switch s {
	case "SpamType_NOSPAM":
		return SpamType_NOSPAM, nil
	case "SpamType_DEVICESPAM":
		return SpamType_DEVICESPAM, nil
	case "SpamType_INTERVALSPAM":
		return SpamType_INTERVALSPAM, nil
	case "SpamType_IPSPAM":
		return SpamType_IPSPAM, nil
	case "SpamType_AMAC":
		return SpamType_AMAC, nil
	case "SpamType_USER":
		return SpamType_USER, nil
	}
	return SpamType(math.MinInt32 - 1), fmt.Errorf("not a valid SpamType string")
}

type AntiObject struct {
	UiRequest *sos_ui_types.SosUIProccessedRequest `thrift:"ui_request,1" json:"ui_request"`
	Tracker   *sos_ui_types.SosTracker             `thrift:"tracker,2" json:"tracker"`
	JudgeType sos_ui_types.JudgeType               `thrift:"judge_type,3" json:"judge_type"`
}

func NewAntiObject() *AntiObject {
	return &AntiObject{
		JudgeType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AntiObject) IsSetJudgeType() bool {
	return int64(p.JudgeType) != math.MinInt32-1
}

func (p *AntiObject) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRUCT {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AntiObject) readField1(iprot thrift.TProtocol) error {
	p.UiRequest = sos_ui_types.NewSosUIProccessedRequest()
	if err := p.UiRequest.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.UiRequest)
	}
	return nil
}

func (p *AntiObject) readField2(iprot thrift.TProtocol) error {
	p.Tracker = sos_ui_types.NewSosTracker()
	if err := p.Tracker.Read(iprot); err != nil {
		return fmt.Errorf("%T error reading struct: %s", p.Tracker)
	}
	return nil
}

func (p *AntiObject) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.JudgeType = sos_ui_types.JudgeType(v)
	}
	return nil
}

func (p *AntiObject) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AntiObject"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AntiObject) writeField1(oprot thrift.TProtocol) (err error) {
	if p.UiRequest != nil {
		if err := oprot.WriteFieldBegin("ui_request", thrift.STRUCT, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:ui_request: %s", p, err)
		}
		if err := p.UiRequest.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.UiRequest)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:ui_request: %s", p, err)
		}
	}
	return err
}

func (p *AntiObject) writeField2(oprot thrift.TProtocol) (err error) {
	if p.Tracker != nil {
		if err := oprot.WriteFieldBegin("tracker", thrift.STRUCT, 2); err != nil {
			return fmt.Errorf("%T write field begin error 2:tracker: %s", p, err)
		}
		if err := p.Tracker.Write(oprot); err != nil {
			return fmt.Errorf("%T error writing struct: %s", p.Tracker)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 2:tracker: %s", p, err)
		}
	}
	return err
}

func (p *AntiObject) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetJudgeType() {
		if err := oprot.WriteFieldBegin("judge_type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:judge_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.JudgeType)); err != nil {
			return fmt.Errorf("%T.judge_type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:judge_type: %s", p, err)
		}
	}
	return err
}

func (p *AntiObject) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AntiObject(%+v)", *p)
}

type AntiResponse struct {
	Status   AntiStatus `thrift:"status,1" json:"status"`
	Msg      string     `thrift:"msg,2" json:"msg"`
	SpamType SpamType   `thrift:"spam_type,3" json:"spam_type"`
}

func NewAntiResponse() *AntiResponse {
	return &AntiResponse{
		Status: math.MinInt32 - 1, // unset sentinal value

		SpamType: math.MinInt32 - 1, // unset sentinal value
	}
}

func (p *AntiResponse) IsSetStatus() bool {
	return int64(p.Status) != math.MinInt32-1
}

func (p *AntiResponse) IsSetSpamType() bool {
	return int64(p.SpamType) != math.MinInt32-1
}

func (p *AntiResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.STRING {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *AntiResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = AntiStatus(v)
	}
	return nil
}

func (p *AntiResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SpamType = SpamType(v)
	}
	return nil
}

func (p *AntiResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Msg = v
	}
	return nil
}

func (p *AntiResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("AntiResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *AntiResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if p.IsSetStatus() {
		if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
			return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.Status)); err != nil {
			return fmt.Errorf("%T.status (1) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 1:status: %s", p, err)
		}
	}
	return err
}

func (p *AntiResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("msg", thrift.STRING, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:msg: %s", p, err)
	}
	if err := oprot.WriteString(string(p.Msg)); err != nil {
		return fmt.Errorf("%T.msg (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:msg: %s", p, err)
	}
	return err
}

func (p *AntiResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if p.IsSetSpamType() {
		if err := oprot.WriteFieldBegin("spam_type", thrift.I32, 3); err != nil {
			return fmt.Errorf("%T write field begin error 3:spam_type: %s", p, err)
		}
		if err := oprot.WriteI32(int32(p.SpamType)); err != nil {
			return fmt.Errorf("%T.spam_type (3) field write error: %s", p, err)
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 3:spam_type: %s", p, err)
		}
	}
	return err
}

func (p *AntiResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("AntiResponse(%+v)", *p)
}
