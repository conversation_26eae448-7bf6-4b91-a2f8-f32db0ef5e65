// Autogenerated by <PERSON>hr<PERSON> Compiler (0.9.1)
// DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING

package predict_model_server

import (
	"fmt"
	"git.apache.org/thrift.git/lib/go/thrift"
	"math"
	"rtb_model_server/common/domob_thrift/dm303"
	"rtb_model_server/common/domob_thrift/rtb_adinfo_types"
	"rtb_model_server/common/domob_thrift/rtb_types"
)

// (needed to ensure safety because of naive import list construction.)
var _ = math.MinInt32
var _ = thrift.ZERO
var _ = fmt.Printf

var _ = dm303.GoUnusedProtection__
var _ = rtb_types.GoUnusedProtection__
var _ = rtb_adinfo_types.GoUnusedProtection__
var GoUnusedProtection__ int

type PredictAdInfo struct {
	AdIndex        int32 `thrift:"ad_index,1" json:"ad_index"`
	CreativeId     int32 `thrift:"creative_id,2" json:"creative_id"`
	StrategyId     int32 `thrift:"strategy_id,3" json:"strategy_id"`
	CampaignId     int32 `thrift:"campaign_id,4" json:"campaign_id"`
	SponsorId      int32 `thrift:"sponsor_id,5" json:"sponsor_id"`
	ProductId      int32 `thrift:"product_id,6" json:"product_id"`
	AdCostType     int32 `thrift:"ad_cost_type,7" json:"ad_cost_type"`
	ChnId          int32 `thrift:"chn_id,8" json:"chn_id"`
	AppId          int32 `thrift:"app_id,9" json:"app_id"`
	FinPriceSource int32 `thrift:"fin_price_source,10" json:"fin_price_source"`
	AdPosId        int64 `thrift:"ad_pos_id,11" json:"ad_pos_id"`
}

func NewPredictAdInfo() *PredictAdInfo {
	return &PredictAdInfo{}
}

func (p *PredictAdInfo) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I32 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I32 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I32 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I64 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictAdInfo) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdIndex = v
	}
	return nil
}

func (p *PredictAdInfo) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.CreativeId = v
	}
	return nil
}

func (p *PredictAdInfo) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.StrategyId = v
	}
	return nil
}

func (p *PredictAdInfo) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.CampaignId = v
	}
	return nil
}

func (p *PredictAdInfo) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.SponsorId = v
	}
	return nil
}

func (p *PredictAdInfo) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.ProductId = v
	}
	return nil
}

func (p *PredictAdInfo) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.AdCostType = v
	}
	return nil
}

func (p *PredictAdInfo) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.ChnId = v
	}
	return nil
}

func (p *PredictAdInfo) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.AppId = v
	}
	return nil
}

func (p *PredictAdInfo) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.FinPriceSource = v
	}
	return nil
}

func (p *PredictAdInfo) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.AdPosId = v
	}
	return nil
}

func (p *PredictAdInfo) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictAdInfo"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictAdInfo) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_index", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ad_index: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdIndex)); err != nil {
		return fmt.Errorf("%T.ad_index (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ad_index: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("creative_id", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:creative_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CreativeId)); err != nil {
		return fmt.Errorf("%T.creative_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:creative_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("strategy_id", thrift.I32, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:strategy_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.StrategyId)); err != nil {
		return fmt.Errorf("%T.strategy_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:strategy_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("campaign_id", thrift.I32, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:campaign_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.CampaignId)); err != nil {
		return fmt.Errorf("%T.campaign_id (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:campaign_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("sponsor_id", thrift.I32, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:sponsor_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.SponsorId)); err != nil {
		return fmt.Errorf("%T.sponsor_id (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:sponsor_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("product_id", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:product_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ProductId)); err != nil {
		return fmt.Errorf("%T.product_id (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:product_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_cost_type", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:ad_cost_type: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdCostType)); err != nil {
		return fmt.Errorf("%T.ad_cost_type (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:ad_cost_type: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("chn_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:chn_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ChnId)); err != nil {
		return fmt.Errorf("%T.chn_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:chn_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_id", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:app_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AppId)); err != nil {
		return fmt.Errorf("%T.app_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:app_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("fin_price_source", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:fin_price_source: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.FinPriceSource)); err != nil {
		return fmt.Errorf("%T.fin_price_source (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:fin_price_source: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_pos_id", thrift.I64, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:ad_pos_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.AdPosId)); err != nil {
		return fmt.Errorf("%T.ad_pos_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:ad_pos_id: %s", p, err)
	}
	return err
}

func (p *PredictAdInfo) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictAdInfo(%+v)", *p)
}

type PredictResponseItem struct {
	AdIndex int32 `thrift:"ad_index,1" json:"ad_index"`
	Cid     int32 `thrift:"cid,2" json:"cid"`
	Ctr     int64 `thrift:"ctr,3" json:"ctr"`
	Cvr     int64 `thrift:"cvr,4" json:"cvr"`
	DeepCvr int64 `thrift:"deep_cvr,5" json:"deep_cvr"`
}

func NewPredictResponseItem() *PredictResponseItem {
	return &PredictResponseItem{}
}

func (p *PredictResponseItem) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.I64 {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.I64 {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictResponseItem) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.AdIndex = v
	}
	return nil
}

func (p *PredictResponseItem) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.Cid = v
	}
	return nil
}

func (p *PredictResponseItem) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.Ctr = v
	}
	return nil
}

func (p *PredictResponseItem) readField4(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 4: %s", err)
	} else {
		p.Cvr = v
	}
	return nil
}

func (p *PredictResponseItem) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DeepCvr = v
	}
	return nil
}

func (p *PredictResponseItem) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictResponseItem"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictResponseItem) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ad_index", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:ad_index: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdIndex)); err != nil {
		return fmt.Errorf("%T.ad_index (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:ad_index: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cid", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:cid: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Cid)); err != nil {
		return fmt.Errorf("%T.cid (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:cid: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("ctr", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:ctr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Ctr)); err != nil {
		return fmt.Errorf("%T.ctr (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:ctr: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField4(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("cvr", thrift.I64, 4); err != nil {
		return fmt.Errorf("%T write field begin error 4:cvr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.Cvr)); err != nil {
		return fmt.Errorf("%T.cvr (4) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 4:cvr: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("deep_cvr", thrift.I64, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:deep_cvr: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.DeepCvr)); err != nil {
		return fmt.Errorf("%T.deep_cvr (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:deep_cvr: %s", p, err)
	}
	return err
}

func (p *PredictResponseItem) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictResponseItem(%+v)", *p)
}

type PredictModelServerRequest struct {
	ReqId          string           `thrift:"req_id,1" json:"req_id"`
	ReqTs          int32            `thrift:"req_ts,2" json:"req_ts"`
	SearchId       int64            `thrift:"search_id,3" json:"search_id"`
	AdList         []*PredictAdInfo `thrift:"ad_list,4" json:"ad_list"`
	DeviceIdMd5    string           `thrift:"device_id_md5,5" json:"device_id_md5"`
	DmPlatform     int32            `thrift:"dm_platform,6" json:"dm_platform"`
	ExchangeId     int32            `thrift:"exchange_id,7" json:"exchange_id"`
	AdxExchangeId  int32            `thrift:"adx_exchange_id,8" json:"adx_exchange_id"`
	DmGeoId        int32            `thrift:"dm_geo_id,9" json:"dm_geo_id"`
	DmOsId         int32            `thrift:"dm_os_id,10" json:"dm_os_id"`
	DmMediaId      int32            `thrift:"dm_media_id,11" json:"dm_media_id"`
	AppBundle      string           `thrift:"app_bundle,12" json:"app_bundle"`
	AbsolutePos    int32            `thrift:"absolute_pos,13" json:"absolute_pos"`
	DmAccesstypeId int32            `thrift:"dm_accesstype_id,14" json:"dm_accesstype_id"`
}

func NewPredictModelServerRequest() *PredictModelServerRequest {
	return &PredictModelServerRequest{}
}

func (p *PredictModelServerRequest) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.STRING {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I32 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 5:
			if fieldTypeId == thrift.STRING {
				if err := p.readField5(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 6:
			if fieldTypeId == thrift.I32 {
				if err := p.readField6(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 7:
			if fieldTypeId == thrift.I32 {
				if err := p.readField7(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 8:
			if fieldTypeId == thrift.I32 {
				if err := p.readField8(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 9:
			if fieldTypeId == thrift.I32 {
				if err := p.readField9(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 10:
			if fieldTypeId == thrift.I32 {
				if err := p.readField10(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 11:
			if fieldTypeId == thrift.I32 {
				if err := p.readField11(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 12:
			if fieldTypeId == thrift.STRING {
				if err := p.readField12(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 13:
			if fieldTypeId == thrift.I32 {
				if err := p.readField13(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 14:
			if fieldTypeId == thrift.I32 {
				if err := p.readField14(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerRequest) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.ReqId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.ReqTs = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.AdList = make([]*PredictAdInfo, 0, size)
	for i := 0; i < size; i++ {
		_elem0 := NewPredictAdInfo()
		if err := _elem0.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem0)
		}
		p.AdList = append(p.AdList, _elem0)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PredictModelServerRequest) readField5(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 5: %s", err)
	} else {
		p.DeviceIdMd5 = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField6(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 6: %s", err)
	} else {
		p.DmPlatform = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField7(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 7: %s", err)
	} else {
		p.ExchangeId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField8(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 8: %s", err)
	} else {
		p.AdxExchangeId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField9(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 9: %s", err)
	} else {
		p.DmGeoId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField10(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 10: %s", err)
	} else {
		p.DmOsId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField11(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 11: %s", err)
	} else {
		p.DmMediaId = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField12(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadString(); err != nil {
		return fmt.Errorf("error reading field 12: %s", err)
	} else {
		p.AppBundle = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField13(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 13: %s", err)
	} else {
		p.AbsolutePos = v
	}
	return nil
}

func (p *PredictModelServerRequest) readField14(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 14: %s", err)
	} else {
		p.DmAccesstypeId = v
	}
	return nil
}

func (p *PredictModelServerRequest) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictModelServerRequest"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := p.writeField5(oprot); err != nil {
		return err
	}
	if err := p.writeField6(oprot); err != nil {
		return err
	}
	if err := p.writeField7(oprot); err != nil {
		return err
	}
	if err := p.writeField8(oprot); err != nil {
		return err
	}
	if err := p.writeField9(oprot); err != nil {
		return err
	}
	if err := p.writeField10(oprot); err != nil {
		return err
	}
	if err := p.writeField11(oprot); err != nil {
		return err
	}
	if err := p.writeField12(oprot); err != nil {
		return err
	}
	if err := p.writeField13(oprot); err != nil {
		return err
	}
	if err := p.writeField14(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerRequest) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_id", thrift.STRING, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:req_id: %s", p, err)
	}
	if err := oprot.WriteString(string(p.ReqId)); err != nil {
		return fmt.Errorf("%T.req_id (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:req_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("req_ts", thrift.I32, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:req_ts: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ReqTs)); err != nil {
		return fmt.Errorf("%T.req_ts (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:req_ts: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:search_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField4(oprot thrift.TProtocol) (err error) {
	if p.AdList != nil {
		if err := oprot.WriteFieldBegin("ad_list", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:ad_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.AdList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.AdList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:ad_list: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerRequest) writeField5(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("device_id_md5", thrift.STRING, 5); err != nil {
		return fmt.Errorf("%T write field begin error 5:device_id_md5: %s", p, err)
	}
	if err := oprot.WriteString(string(p.DeviceIdMd5)); err != nil {
		return fmt.Errorf("%T.device_id_md5 (5) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 5:device_id_md5: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField6(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dm_platform", thrift.I32, 6); err != nil {
		return fmt.Errorf("%T write field begin error 6:dm_platform: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmPlatform)); err != nil {
		return fmt.Errorf("%T.dm_platform (6) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 6:dm_platform: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField7(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("exchange_id", thrift.I32, 7); err != nil {
		return fmt.Errorf("%T write field begin error 7:exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.ExchangeId)); err != nil {
		return fmt.Errorf("%T.exchange_id (7) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 7:exchange_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField8(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("adx_exchange_id", thrift.I32, 8); err != nil {
		return fmt.Errorf("%T write field begin error 8:adx_exchange_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AdxExchangeId)); err != nil {
		return fmt.Errorf("%T.adx_exchange_id (8) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 8:adx_exchange_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField9(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dm_geo_id", thrift.I32, 9); err != nil {
		return fmt.Errorf("%T write field begin error 9:dm_geo_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmGeoId)); err != nil {
		return fmt.Errorf("%T.dm_geo_id (9) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 9:dm_geo_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField10(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dm_os_id", thrift.I32, 10); err != nil {
		return fmt.Errorf("%T write field begin error 10:dm_os_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmOsId)); err != nil {
		return fmt.Errorf("%T.dm_os_id (10) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 10:dm_os_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField11(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dm_media_id", thrift.I32, 11); err != nil {
		return fmt.Errorf("%T write field begin error 11:dm_media_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmMediaId)); err != nil {
		return fmt.Errorf("%T.dm_media_id (11) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 11:dm_media_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField12(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("app_bundle", thrift.STRING, 12); err != nil {
		return fmt.Errorf("%T write field begin error 12:app_bundle: %s", p, err)
	}
	if err := oprot.WriteString(string(p.AppBundle)); err != nil {
		return fmt.Errorf("%T.app_bundle (12) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 12:app_bundle: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField13(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("absolute_pos", thrift.I32, 13); err != nil {
		return fmt.Errorf("%T write field begin error 13:absolute_pos: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.AbsolutePos)); err != nil {
		return fmt.Errorf("%T.absolute_pos (13) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 13:absolute_pos: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) writeField14(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("dm_accesstype_id", thrift.I32, 14); err != nil {
		return fmt.Errorf("%T write field begin error 14:dm_accesstype_id: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.DmAccesstypeId)); err != nil {
		return fmt.Errorf("%T.dm_accesstype_id (14) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 14:dm_accesstype_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerRequest) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictModelServerRequest(%+v)", *p)
}

type PredictModelServerResponse struct {
	Status       int32                  `thrift:"status,1" json:"status"`
	SearchId     int64                  `thrift:"search_id,2" json:"search_id"`
	PredictTs    int64                  `thrift:"predict_ts,3" json:"predict_ts"`
	ResponseList []*PredictResponseItem `thrift:"response_list,4" json:"response_list"`
}

func NewPredictModelServerResponse() *PredictModelServerResponse {
	return &PredictModelServerResponse{}
}

func (p *PredictModelServerResponse) Read(iprot thrift.TProtocol) error {
	if _, err := iprot.ReadStructBegin(); err != nil {
		return fmt.Errorf("%T read error", p)
	}
	for {
		_, fieldTypeId, fieldId, err := iprot.ReadFieldBegin()
		if err != nil {
			return fmt.Errorf("%T field %d read error: %s", p, fieldId, err)
		}
		if fieldTypeId == thrift.STOP {
			break
		}
		switch fieldId {
		case 1:
			if fieldTypeId == thrift.I32 {
				if err := p.readField1(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 2:
			if fieldTypeId == thrift.I64 {
				if err := p.readField2(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 3:
			if fieldTypeId == thrift.I64 {
				if err := p.readField3(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		case 4:
			if fieldTypeId == thrift.LIST {
				if err := p.readField4(iprot); err != nil {
					return err
				}
			} else {
				if err := iprot.Skip(fieldTypeId); err != nil {
					return err
				}
			}
		default:
			if err := iprot.Skip(fieldTypeId); err != nil {
				return err
			}
		}
		if err := iprot.ReadFieldEnd(); err != nil {
			return err
		}
	}
	if err := iprot.ReadStructEnd(); err != nil {
		return fmt.Errorf("%T read struct end error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerResponse) readField1(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI32(); err != nil {
		return fmt.Errorf("error reading field 1: %s", err)
	} else {
		p.Status = v
	}
	return nil
}

func (p *PredictModelServerResponse) readField2(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 2: %s", err)
	} else {
		p.SearchId = v
	}
	return nil
}

func (p *PredictModelServerResponse) readField3(iprot thrift.TProtocol) error {
	if v, err := iprot.ReadI64(); err != nil {
		return fmt.Errorf("error reading field 3: %s", err)
	} else {
		p.PredictTs = v
	}
	return nil
}

func (p *PredictModelServerResponse) readField4(iprot thrift.TProtocol) error {
	_, size, err := iprot.ReadListBegin()
	if err != nil {
		return fmt.Errorf("error reading list being: %s", err)
	}
	p.ResponseList = make([]*PredictResponseItem, 0, size)
	for i := 0; i < size; i++ {
		_elem1 := NewPredictResponseItem()
		if err := _elem1.Read(iprot); err != nil {
			return fmt.Errorf("%T error reading struct: %s", _elem1)
		}
		p.ResponseList = append(p.ResponseList, _elem1)
	}
	if err := iprot.ReadListEnd(); err != nil {
		return fmt.Errorf("error reading list end: %s", err)
	}
	return nil
}

func (p *PredictModelServerResponse) Write(oprot thrift.TProtocol) error {
	if err := oprot.WriteStructBegin("PredictModelServerResponse"); err != nil {
		return fmt.Errorf("%T write struct begin error: %s", p, err)
	}
	if err := p.writeField1(oprot); err != nil {
		return err
	}
	if err := p.writeField2(oprot); err != nil {
		return err
	}
	if err := p.writeField3(oprot); err != nil {
		return err
	}
	if err := p.writeField4(oprot); err != nil {
		return err
	}
	if err := oprot.WriteFieldStop(); err != nil {
		return fmt.Errorf("%T write field stop error: %s", p, err)
	}
	if err := oprot.WriteStructEnd(); err != nil {
		return fmt.Errorf("%T write struct stop error: %s", p, err)
	}
	return nil
}

func (p *PredictModelServerResponse) writeField1(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("status", thrift.I32, 1); err != nil {
		return fmt.Errorf("%T write field begin error 1:status: %s", p, err)
	}
	if err := oprot.WriteI32(int32(p.Status)); err != nil {
		return fmt.Errorf("%T.status (1) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 1:status: %s", p, err)
	}
	return err
}

func (p *PredictModelServerResponse) writeField2(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("search_id", thrift.I64, 2); err != nil {
		return fmt.Errorf("%T write field begin error 2:search_id: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.SearchId)); err != nil {
		return fmt.Errorf("%T.search_id (2) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 2:search_id: %s", p, err)
	}
	return err
}

func (p *PredictModelServerResponse) writeField3(oprot thrift.TProtocol) (err error) {
	if err := oprot.WriteFieldBegin("predict_ts", thrift.I64, 3); err != nil {
		return fmt.Errorf("%T write field begin error 3:predict_ts: %s", p, err)
	}
	if err := oprot.WriteI64(int64(p.PredictTs)); err != nil {
		return fmt.Errorf("%T.predict_ts (3) field write error: %s", p, err)
	}
	if err := oprot.WriteFieldEnd(); err != nil {
		return fmt.Errorf("%T write field end error 3:predict_ts: %s", p, err)
	}
	return err
}

func (p *PredictModelServerResponse) writeField4(oprot thrift.TProtocol) (err error) {
	if p.ResponseList != nil {
		if err := oprot.WriteFieldBegin("response_list", thrift.LIST, 4); err != nil {
			return fmt.Errorf("%T write field begin error 4:response_list: %s", p, err)
		}
		if err := oprot.WriteListBegin(thrift.STRUCT, len(p.ResponseList)); err != nil {
			return fmt.Errorf("error writing list begin: %s")
		}
		for _, v := range p.ResponseList {
			if err := v.Write(oprot); err != nil {
				return fmt.Errorf("%T error writing struct: %s", v)
			}
		}
		if err := oprot.WriteListEnd(); err != nil {
			return fmt.Errorf("error writing list end: %s")
		}
		if err := oprot.WriteFieldEnd(); err != nil {
			return fmt.Errorf("%T write field end error 4:response_list: %s", p, err)
		}
	}
	return err
}

func (p *PredictModelServerResponse) String() string {
	if p == nil {
		return "<nil>"
	}
	return fmt.Sprintf("PredictModelServerResponse(%+v)", *p)
}
